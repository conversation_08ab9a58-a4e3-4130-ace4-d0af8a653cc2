/**
 * Enhanced Orders Management System for Care Admin Dashboard
 * نظام إدارة الطلبات المحسن للوحة التحكم الإدارية
 *
 * Features:
 * - Load and display orders from Supabase
 * - Order status management with real-time updates
 * - Advanced search and filtering
 * - Detailed order modal with customer info and products
 * - WhatsApp integration for customer communication
 * - Order deletion with confirmation
 * - Professional Arabic localization
 * - Comprehensive error handling and user feedback
 * - Real-time statistics and analytics
 */

class OrdersManager {
    constructor() {
        this.supabase = null;
        this.orders = [];
        this.filteredOrders = [];
        this.currentPage = 1;
        this.ordersPerPage = 20;
        this.isLoading = false;
        this.currentOrderToDelete = null;
        this.currentOrderForPrint = null;

        this.init();
    }

    async init() {
        try {
            // Initialize Supabase client
            await this.initializeSupabase();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Load initial data
            await this.loadOrders();
            await this.loadOrdersStats();
            
            console.log('Orders Manager initialized successfully');
        } catch (error) {
            console.error('Error initializing Orders Manager:', error);
            this.showError('حدث خطأ في تهيئة نظام إدارة الطلبات');
        }
    }

    async initializeSupabase() {
        if (typeof window.supabase === 'undefined') {
            throw new Error('Supabase library not loaded');
        }

        const supabaseUrl = 'https://krqijjttwllohulmdwgs.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70';
        
        this.supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
    }

    setupEventListeners() {
        // Search input
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterOrders();
            });
        }

        // Status filter
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filterOrders();
            });
        }

        // Modal close events
        const orderModal = document.getElementById('orderModal');
        if (orderModal) {
            orderModal.addEventListener('click', (e) => {
                if (e.target === orderModal) {
                    this.closeOrderModal();
                }
            });
        }
    }

    async loadOrders() {
        try {
            this.showLoading(true);
            
            const { data: orders, error } = await this.supabase
                .from('orders')
                .select(`
                    *,
                    order_items (
                        *,
                        products (
                            name,
                            price
                        )
                    )
                `)
                .order('created_at', { ascending: false });

            if (error) throw error;

            this.orders = orders || [];
            this.filteredOrders = [...this.orders];
            
            this.renderOrdersTable();
            this.showLoading(false);
            
            console.log(`Loaded ${this.orders.length} orders`);
            
        } catch (error) {
            console.error('Error loading orders:', error);
            this.showError('حدث خطأ في تحميل الطلبات');
            this.showLoading(false);
        }
    }

    async loadOrdersStats() {
        try {
            const total = this.orders.length;
            const pending = this.orders.filter(order => order.status === 'pending').length;
            const confirmed = this.orders.filter(order => order.status === 'confirmed').length;
            const shipped = this.orders.filter(order => order.status === 'shipped').length;
            const delivered = this.orders.filter(order => order.status === 'delivered').length;
            const cancelled = this.orders.filter(order => order.status === 'cancelled').length;
            const completed = delivered + shipped;

            // Update stats display
            this.updateStatsDisplay({
                total,
                pending,
                completed,
                cancelled,
                confirmed,
                shipped
            });

        } catch (error) {
            console.error('Error loading orders stats:', error);
            this.showError('خطأ في تحميل إحصائيات الطلبات');
        }
    }



    updateStatsDisplay(stats) {
        const elements = {
            totalOrders: document.getElementById('totalOrders'),
            pendingOrders: document.getElementById('pendingOrders'),
            completedOrders: document.getElementById('completedOrders'),
            cancelledOrders: document.getElementById('cancelledOrders'),
            confirmedOrders: document.getElementById('confirmedOrders'),
            shippedOrders: document.getElementById('shippedOrders')
        };

        // Update with animation
        if (elements.totalOrders) {
            this.animateNumber(elements.totalOrders, stats.total);
        }
        if (elements.pendingOrders) {
            this.animateNumber(elements.pendingOrders, stats.pending);
        }
        if (elements.completedOrders) {
            this.animateNumber(elements.completedOrders, stats.completed);
        }
        if (elements.cancelledOrders) {
            this.animateNumber(elements.cancelledOrders, stats.cancelled);
        }
        if (elements.confirmedOrders) {
            this.animateNumber(elements.confirmedOrders, stats.confirmed);
        }
        if (elements.shippedOrders) {
            this.animateNumber(elements.shippedOrders, stats.shipped);
        }
    }

    animateNumber(element, targetValue, isCurrency = false) {
        const currentValue = parseInt(element.textContent.replace(/[^\d]/g, '')) || 0;
        const increment = Math.ceil(Math.abs(targetValue - currentValue) / 20);

        if (currentValue === targetValue) return;

        const timer = setInterval(() => {
            const current = parseInt(element.textContent.replace(/[^\d]/g, '')) || 0;
            let newValue;

            if (current < targetValue) {
                newValue = Math.min(current + increment, targetValue);
            } else {
                newValue = Math.max(current - increment, targetValue);
            }

            if (newValue === targetValue) {
                element.textContent = isCurrency ?
                    `${Math.round(targetValue).toLocaleString('ar-IQ')} د.ع` :
                    targetValue.toString();
                clearInterval(timer);
            } else {
                element.textContent = isCurrency ?
                    `${Math.round(newValue).toLocaleString('ar-IQ')} د.ع` :
                    newValue.toString();
            }
        }, 50);
    }

    filterOrders() {
        const searchTerm = document.getElementById('searchInput')?.value.toLowerCase() || '';
        const statusFilter = document.getElementById('statusFilter')?.value || '';

        this.filteredOrders = this.orders.filter(order => {
            const matchesSearch = !searchTerm ||
                (order.customer_name && order.customer_name.toLowerCase().includes(searchTerm)) ||
                (order.customer_phone && order.customer_phone.includes(searchTerm)) ||
                (order.id && order.id.toLowerCase().includes(searchTerm)) ||
                (order.id && order.id.substring(0, 8).toLowerCase().includes(searchTerm)) ||
                (order.customer_address && order.customer_address.toLowerCase().includes(searchTerm)) ||
                (order.customer_notes && order.customer_notes.toLowerCase().includes(searchTerm));

            const matchesStatus = !statusFilter || order.status === statusFilter;

            return matchesSearch && matchesStatus;
        });

        this.renderOrdersTable();

        // Update search results info
        const totalResults = this.filteredOrders.length;
        const totalOrders = this.orders.length;

        if (searchTerm || statusFilter) {
            console.log(`عرض ${totalResults} من أصل ${totalOrders} طلب`);
        }
    }

    renderOrdersTable() {
        const container = document.getElementById('ordersTableContainer');
        if (!container) return;

        if (this.filteredOrders.length === 0) {
            container.innerHTML = this.getEmptyStateHTML();
            return;
        }

        const tableHTML = `
            <div class="table-responsive">
                <table class="orders-table">
                    <thead>
                        <tr>
                            <th>رقم الطلب</th>
                            <th>العميل</th>
                            <th>المنتجات</th>
                            <th>المجموع</th>
                            <th>الحالة</th>
                            <th>التاريخ</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.filteredOrders.map(order => this.getOrderRowHTML(order)).join('')}
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = tableHTML;
    }

    getOrderRowHTML(order) {
        const orderDate = this.formatArabicDate(order.created_at);

        const statusClass = this.getStatusClass(order.status);

        // Get products summary
        const orderItems = order.order_items || [];
        const productsCount = orderItems.length;
        const totalQuantity = orderItems.reduce((sum, item) => sum + item.quantity, 0);
        const productsText = productsCount > 0 ?
            `${productsCount} منتج (${totalQuantity} قطعة)` :
            'لا توجد منتجات';

        return `
            <tr>
                <td>
                    <span class="order-id">#${order.id.substring(0, 8)}</span>
                </td>
                <td>
                    <div class="customer-info">
                        <div class="customer-name">${order.customer_name || 'غير محدد'}</div>
                        <div class="customer-phone">${order.customer_phone || 'غير محدد'}</div>
                    </div>
                </td>
                <td>
                    <div class="products-summary">
                        <span class="products-count">${productsText}</span>
                    </div>
                </td>
                <td>
                    <span class="order-total">${parseFloat(order.total || 0).toLocaleString('ar-IQ')} د.ع</span>
                </td>
                <td>
                    <select class="status-select ${statusClass}" onchange="ordersManager.updateOrderStatus('${order.id}', this.value)">
                        <option value="pending" ${order.status === 'pending' ? 'selected' : ''}>معلق</option>
                        <option value="confirmed" ${order.status === 'confirmed' ? 'selected' : ''}>مؤكد</option>
                        <option value="shipped" ${order.status === 'shipped' ? 'selected' : ''}>تم الشحن</option>
                        <option value="delivered" ${order.status === 'delivered' ? 'selected' : ''}>تم التسليم</option>
                        <option value="cancelled" ${order.status === 'cancelled' ? 'selected' : ''}>ملغي</option>
                    </select>
                </td>
                <td>
                    <span class="order-date">${orderDate}</span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-view" onclick="ordersManager.viewOrderDetails('${order.id}')" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-whatsapp" onclick="ordersManager.contactCustomer('${order.customer_phone}', '${order.id}')" title="التواصل عبر واتساب">
                            <i class="fab fa-whatsapp"></i>
                        </button>
                        <button class="btn btn-delete" onclick="ordersManager.showDeleteModal('${order.id}')" title="حذف الطلب">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    getEmptyStateHTML() {
        return `
            <div class="empty-state">
                <i class="fas fa-shopping-cart"></i>
                <h3>لا توجد طلبات</h3>
                <p>لم يتم العثور على أي طلبات تطابق معايير البحث</p>
            </div>
        `;
    }

    getStatusClass(status) {
        const statusClasses = {
            'pending': 'pending',
            'confirmed': 'confirmed',
            'shipped': 'shipped',
            'delivered': 'delivered',
            'cancelled': 'cancelled'
        };
        return statusClasses[status] || 'pending';
    }

    getStatusText(status) {
        const statusTexts = {
            'pending': 'معلق',
            'confirmed': 'مؤكد',
            'shipped': 'تم الشحن',
            'delivered': 'تم التسليم',
            'cancelled': 'ملغي'
        };
        return statusTexts[status] || 'معلق';
    }

    formatArabicDate(dateString) {
        const date = new Date(dateString);
        const arabicMonths = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];

        const day = date.getDate();
        const month = arabicMonths[date.getMonth()];
        const year = date.getFullYear();
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');

        return `${day} ${month} ${year} - ${hours}:${minutes}`;
    }

    showLoading(show) {
        const spinner = document.getElementById('loadingSpinner');
        if (spinner) {
            spinner.style.display = show ? 'flex' : 'none';
        }
        this.isLoading = show;
    }

    showSuccess(message) {
        // Remove any existing notifications
        const existingNotifications = document.querySelectorAll('.enhanced-success-notification');
        existingNotifications.forEach(notification => notification.remove());

        // Create enhanced notification
        const notification = document.createElement('div');
        notification.className = 'enhanced-success-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="notification-text">
                    <div class="notification-title">تم بنجاح!</div>
                    <div class="notification-message">${message}</div>
                </div>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="notification-progress"></div>
        `;

        // Add enhanced styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid rgba(40, 167, 69, 0.2);
            border-left: 4px solid #28a745;
            border-radius: 12px;
            box-shadow: 0 12px 40px rgba(40, 167, 69, 0.15), 0 4px 20px rgba(0, 0, 0, 0.1);
            z-index: 10000;
            min-width: 350px;
            max-width: 450px;
            font-family: 'Cairo', sans-serif;
            transform: translateX(100%) scale(0.8);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            animation: enhancedSlideIn 0.5s ease-out forwards;
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'enhancedSlideOut 0.4s ease-in forwards';
                setTimeout(() => notification.remove(), 400);
            }
        }, 5000);
    }

    showError(message) {
        const errorElement = document.getElementById('errorMessage');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
            setTimeout(() => {
                errorElement.style.display = 'none';
            }, 5000);
        }
    }

    async updateOrderStatus(orderId, newStatus) {
        try {
            const { error } = await this.supabase
                .from('orders')
                .update({
                    status: newStatus,
                    updated_at: new Date().toISOString()
                })
                .eq('id', orderId);

            if (error) throw error;

            // Update local data
            const orderIndex = this.orders.findIndex(order => order.id === orderId);
            if (orderIndex !== -1) {
                this.orders[orderIndex].status = newStatus;
            }

            this.filterOrders();
            this.loadOrdersStats();
            this.showSuccess('تم تحديث حالة الطلب بنجاح');

        } catch (error) {
            console.error('Error updating order status:', error);
            this.showError('حدث خطأ في تحديث حالة الطلب');
            // Reload to reset the select value
            this.loadOrders();
        }
    }

    async viewOrderDetails(orderId) {
        try {
            const order = this.orders.find(o => o.id === orderId);
            if (!order) {
                this.showError('لم يتم العثور على الطلب');
                return;
            }

            this.showOrderModal(order);

        } catch (error) {
            console.error('Error viewing order details:', error);
            this.showError('حدث خطأ في عرض تفاصيل الطلب');
        }
    }

    showOrderModal(order) {
        const modal = document.getElementById('orderModal');
        const modalBody = document.getElementById('orderModalBody');

        if (!modal || !modalBody) return;

        // Store current order for printing
        this.currentOrderForPrint = order;

        // Add keyboard event listener for Ctrl+P
        this.addPrintKeyboardShortcut();

        const orderDate = this.formatArabicDate(order.created_at);

        const orderItems = order.order_items || [];
        const itemsHTML = orderItems.map((item, index) => `
            <div class="product-item">
                <div class="product-info">
                    <div class="product-name">
                        <span class="product-number">${index + 1}.</span>
                        ${item.products?.name || 'منتج غير معروف'}
                    </div>
                    <div class="product-details">
                        <span class="quantity-info">الكمية: ${item.quantity}</span>
                        <span class="price-info">السعر: ${parseFloat(item.price || 0).toLocaleString('ar-IQ')} د.ع</span>
                    </div>
                </div>
                <div class="product-total">${(item.quantity * parseFloat(item.price || 0)).toLocaleString('ar-IQ')} د.ع</div>
            </div>
        `).join('');

        modalBody.innerHTML = `
            <div class="order-details-grid">
                <div class="detail-section">
                    <h4><i class="fas fa-user"></i> معلومات العميل</h4>
                    <div class="detail-item">
                        <span class="detail-label">الاسم:</span>
                        <span class="detail-value rtl">${order.customer_name || 'غير محدد'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">رقم الهاتف:</span>
                        <span class="detail-value">${order.customer_phone || 'غير محدد'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">عنوان التوصيل:</span>
                        <span class="detail-value rtl">${order.customer_address || 'غير محدد'}</span>
                    </div>
                    ${order.customer_notes ? `
                    <div class="detail-item">
                        <span class="detail-label">ملاحظات العميل:</span>
                        <span class="detail-value rtl">${order.customer_notes}</span>
                    </div>
                    ` : ''}
                    <div class="detail-item">
                        <span class="detail-label">إجراءات التواصل:</span>
                        <span class="detail-value">
                            <button class="btn btn-whatsapp btn-sm" onclick="ordersManager.contactCustomer('${order.customer_phone}', '${order.id}')" style="padding: 0.3rem 0.8rem; font-size: 0.8rem;">
                                <i class="fab fa-whatsapp"></i>
                                واتساب
                            </button>
                        </span>
                    </div>
                </div>

                <div class="detail-section">
                    <h4><i class="fas fa-info-circle"></i> معلومات الطلب</h4>
                    <div class="detail-item">
                        <span class="detail-label">رقم الطلب:</span>
                        <span class="detail-value">#${order.id.substring(0, 8)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">تاريخ الطلب:</span>
                        <span class="detail-value rtl">${orderDate}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">الحالة الحالية:</span>
                        <span class="detail-value rtl">
                            <span class="status-badge ${this.getStatusClass(order.status)}">${this.getStatusText(order.status)}</span>
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">المبلغ الفرعي:</span>
                        <span class="detail-value">${parseFloat(order.subtotal || 0).toLocaleString('ar-IQ')} د.ع</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">رسوم التوصيل:</span>
                        <span class="detail-value">${parseFloat(order.delivery_fee || 0).toLocaleString('ar-IQ')} د.ع</span>
                    </div>
                    ${order.discount_code ? `
                    <div class="detail-item">
                        <span class="detail-label">كود الخصم:</span>
                        <span class="detail-value" style="font-weight: 600; color: #28a745; background: #f0f9f0; padding: 0.3rem 0.8rem; border-radius: 6px; border: 1px solid #c3e6cb;">${order.discount_code}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">مبلغ الخصم:</span>
                        <span class="detail-value" style="font-weight: 600; color: #dc3545; background: #fdf2f2; padding: 0.3rem 0.8rem; border-radius: 6px; border: 1px solid #f5c6cb;">-${parseFloat(order.discount_amount || 0).toLocaleString('ar-IQ')} د.ع</span>
                    </div>
                    ` : ''}
                    <div class="detail-item">
                        <span class="detail-label">المبلغ الإجمالي:</span>
                        <span class="detail-value" style="font-weight: 700; color: #000000; font-size: 1.1rem; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);">${parseFloat(order.total || 0).toLocaleString('ar-IQ')} د.ع</span>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h4><i class="fas fa-shopping-bag"></i> المنتجات المطلوبة (${orderItems.length} منتج)</h4>
                <div class="products-list">
                    ${itemsHTML || '<p style="text-align: center; padding: 2rem; color: var(--color-text-muted);">لا توجد منتجات مرتبطة بهذا الطلب</p>'}
                </div>
                ${orderItems.length > 0 ? `
                <div class="order-summary" style="margin-top: var(--spacing-lg); padding: var(--spacing-lg); background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%); border: 1px solid rgba(130, 135, 122, 0.12); border-radius: var(--radius-xl); text-align: center; box-shadow: 0 2px 10px rgba(130, 135, 122, 0.05);">
                    <strong style="color: #000000; font-size: 1rem; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);">إجمالي المنتجات: ${orderItems.length} | إجمالي الكمية: ${orderItems.reduce((sum, item) => sum + item.quantity, 0)} | المبلغ الإجمالي: ${parseFloat(order.total || 0).toLocaleString('ar-IQ')} د.ع</strong>
                </div>
                ` : ''}
            </div>
        `;

        modal.style.display = 'flex';
        modal.classList.add('show');
        modal.setAttribute('aria-hidden', 'false');

        // Focus on the modal for accessibility
        setTimeout(() => {
            const modalTitle = document.getElementById('orderModalTitle');
            if (modalTitle) {
                modalTitle.focus();
            }
        }, 100);
    }

    closeOrderModal() {
        const modal = document.getElementById('orderModal');
        if (modal) {
            modal.style.display = 'none';
            modal.classList.remove('show');
            modal.setAttribute('aria-hidden', 'true');
        }

        // Remove keyboard event listener
        this.removePrintKeyboardShortcut();

        // Clear current order for printing
        this.currentOrderForPrint = null;
    }

    printOrderDetails() {
        const modalBody = document.getElementById('orderModalBody');
        const printBtn = document.querySelector('.modal-print-btn');

        if (!modalBody || !this.currentOrderForPrint) {
            this.showError('لا توجد تفاصيل طلب متاحة للطباعة');
            return;
        }

        try {
            // Show loading state
            if (printBtn) {
                printBtn.disabled = true;
                printBtn.innerHTML = '<i class="fas fa-spinner fa-spin" aria-hidden="true"></i><span>جاري التحضير...</span>';
            }

            // Create print window
            const printWindow = window.open('', '_blank', 'width=800,height=600');

            if (!printWindow) {
                throw new Error('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
            }

            // Generate print content
            const printContent = this.generatePrintContent(this.currentOrderForPrint);

            // Write content to print window
            printWindow.document.write(printContent);
            printWindow.document.close();

            // Wait for content to load then print
            printWindow.onload = () => {
                setTimeout(() => {
                    printWindow.focus();
                    printWindow.print();

                    // Close window after print dialog
                    setTimeout(() => {
                        printWindow.close();
                    }, 1000);
                }, 500);
            };

            this.showSuccess('تم فتح نافذة الطباعة بنجاح');

        } catch (error) {
            console.error('Error printing order details:', error);
            this.showError(error.message || 'حدث خطأ أثناء محاولة طباعة تفاصيل الطلب');
        } finally {
            // Reset button state
            if (printBtn) {
                setTimeout(() => {
                    printBtn.disabled = false;
                    printBtn.innerHTML = '<i class="fas fa-print" aria-hidden="true"></i><span>طباعة</span>';
                }, 2000);
            }
        }
    }

    generatePrintContent(order) {
        const orderDate = this.formatArabicDate(order.created_at);
        const orderItems = order.order_items || [];

        const itemsHTML = orderItems.map((item, index) => `
            <tr>
                <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: center;">${index + 1}</td>
                <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: right;">${item.products?.name || 'منتج غير معروف'}</td>
                <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: center;">${item.quantity}</td>
                <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: center;">${parseFloat(item.price || 0).toLocaleString('ar-IQ')} د.ع</td>
                <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: center;">${(item.quantity * parseFloat(item.price || 0)).toLocaleString('ar-IQ')} د.ع</td>
            </tr>
        `).join('');

        return `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>تفاصيل الطلب #${order.id.substring(0, 8)}</title>
                <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
                <style>
                    * {
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                    }

                    body {
                        font-family: 'Cairo', sans-serif;
                        direction: rtl;
                        color: #000;
                        background: #fff;
                        padding: 20px;
                        line-height: 1.6;
                    }

                    .print-header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 3px solid #82877a;
                        padding-bottom: 20px;
                    }

                    .company-name {
                        font-size: 28px;
                        font-weight: 700;
                        color: #82877a;
                        margin-bottom: 10px;
                    }

                    .document-title {
                        font-size: 20px;
                        font-weight: 600;
                        color: #333;
                    }

                    .order-info {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 30px;
                        margin-bottom: 30px;
                    }

                    .info-section {
                        border: 1px solid #ddd;
                        border-radius: 8px;
                        padding: 20px;
                    }

                    .section-title {
                        font-size: 16px;
                        font-weight: 700;
                        color: #82877a;
                        margin-bottom: 15px;
                        border-bottom: 2px solid #82877a;
                        padding-bottom: 5px;
                    }

                    .info-item {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 8px;
                        padding: 5px 0;
                    }

                    .info-label {
                        font-weight: 600;
                        color: #333;
                    }

                    .info-value {
                        font-weight: 500;
                        color: #000;
                    }

                    .products-section {
                        margin-bottom: 30px;
                    }

                    .products-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-top: 15px;
                    }

                    .products-table th {
                        background: #82877a;
                        color: white;
                        padding: 12px 8px;
                        text-align: center;
                        font-weight: 700;
                        border: 1px solid #666;
                    }

                    .products-table td {
                        padding: 8px;
                        border-bottom: 1px solid #ddd;
                        text-align: center;
                    }

                    .products-table tr:nth-child(even) {
                        background: #f9f9f9;
                    }

                    .totals-section {
                        border: 2px solid #82877a;
                        border-radius: 8px;
                        padding: 20px;
                        background: #f8f9fa;
                    }

                    .total-item {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 10px;
                        padding: 5px 0;
                    }

                    .total-item.final {
                        border-top: 2px solid #82877a;
                        padding-top: 15px;
                        margin-top: 15px;
                        font-size: 18px;
                        font-weight: 700;
                        color: #82877a;
                    }

                    .print-footer {
                        margin-top: 40px;
                        text-align: center;
                        font-size: 12px;
                        color: #666;
                        border-top: 1px solid #ddd;
                        padding-top: 20px;
                    }

                    @media print {
                        body {
                            padding: 0;
                        }

                        .print-header {
                            margin-bottom: 20px;
                        }

                        .order-info {
                            margin-bottom: 20px;
                        }

                        .products-section {
                            margin-bottom: 20px;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="print-header">
                    <div class="company-name">Care</div>
                    <div class="document-title">تفاصيل الطلب</div>
                </div>

                <div class="order-info">
                    <div class="info-section">
                        <div class="section-title">معلومات الطلب</div>
                        <div class="info-item">
                            <span class="info-label">رقم الطلب:</span>
                            <span class="info-value">#${order.id.substring(0, 8)}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">تاريخ الطلب:</span>
                            <span class="info-value">${orderDate}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">حالة الطلب:</span>
                            <span class="info-value">${this.getStatusText(order.status)}</span>
                        </div>
                    </div>

                    <div class="info-section">
                        <div class="section-title">معلومات العميل</div>
                        <div class="info-item">
                            <span class="info-label">الاسم:</span>
                            <span class="info-value">${order.customer_name || 'غير محدد'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">رقم الهاتف:</span>
                            <span class="info-value">${order.customer_phone || 'غير محدد'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">عنوان التوصيل:</span>
                            <span class="info-value">${order.customer_address || 'غير محدد'}</span>
                        </div>
                        ${order.customer_notes ? `
                        <div class="info-item">
                            <span class="info-label">ملاحظات العميل:</span>
                            <span class="info-value">${order.customer_notes}</span>
                        </div>
                        ` : ''}
                    </div>
                </div>

                <div class="products-section">
                    <div class="section-title">المنتجات المطلوبة (${orderItems.length} منتج)</div>
                    <table class="products-table">
                        <thead>
                            <tr>
                                <th>م</th>
                                <th>اسم المنتج</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${itemsHTML}
                        </tbody>
                    </table>
                </div>

                <div class="totals-section">
                    <div class="section-title">ملخص الطلب</div>
                    <div class="total-item">
                        <span>المبلغ الفرعي:</span>
                        <span>${parseFloat(order.subtotal || 0).toLocaleString('ar-IQ')} د.ع</span>
                    </div>
                    <div class="total-item">
                        <span>رسوم التوصيل:</span>
                        <span>${parseFloat(order.delivery_fee || 0).toLocaleString('ar-IQ')} د.ع</span>
                    </div>
                    ${order.discount_code ? `
                    <div class="total-item" style="color: #28a745; font-weight: 600;">
                        <span>كود الخصم (${order.discount_code}):</span>
                        <span>-${parseFloat(order.discount_amount || 0).toLocaleString('ar-IQ')} د.ع</span>
                    </div>
                    ` : ''}
                    <div class="total-item final">
                        <span>المبلغ الإجمالي:</span>
                        <span>${parseFloat(order.total || 0).toLocaleString('ar-IQ')} د.ع</span>
                    </div>
                </div>

                <div class="print-footer">
                    <p>تم طباعة هذا المستند في: ${new Date().toLocaleDateString('ar-IQ')} - ${new Date().toLocaleTimeString('ar-IQ')}</p>
                    <p>Care - نظام إدارة الطلبات</p>
                </div>
            </body>
            </html>
        `;
    }

    addPrintKeyboardShortcut() {
        // Remove existing listener if any
        this.removePrintKeyboardShortcut();

        // Create new listener
        this.printKeyboardHandler = (event) => {
            if ((event.ctrlKey || event.metaKey) && event.key === 'p') {
                event.preventDefault();
                this.printOrderDetails();
            }
        };

        // Add listener
        document.addEventListener('keydown', this.printKeyboardHandler);
    }

    removePrintKeyboardShortcut() {
        if (this.printKeyboardHandler) {
            document.removeEventListener('keydown', this.printKeyboardHandler);
            this.printKeyboardHandler = null;
        }
    }

    contactCustomer(phoneNumber, orderId = null) {
        if (!phoneNumber || phoneNumber === 'غير محدد' || phoneNumber === '0') {
            this.showError('رقم الهاتف غير متوفر أو غير صحيح');
            return;
        }

        // Clean phone number and format for WhatsApp
        let cleanPhone = phoneNumber.replace(/\D/g, '');

        // Add Iraq country code if not present
        if (!cleanPhone.startsWith('964') && cleanPhone.length === 11) {
            cleanPhone = '964' + cleanPhone.substring(1);
        } else if (!cleanPhone.startsWith('964') && cleanPhone.length === 10) {
            cleanPhone = '964' + cleanPhone;
        }

        // Prepare WhatsApp message
        let message = 'مرحباً، نتواصل معك بخصوص طلبك من متجر Care.';

        if (orderId) {
            const order = this.orders.find(o => o.id === orderId);
            if (order) {
                message += `\n\nرقم الطلب: #${order.id.substring(0, 8)}`;
                message += `\nالمبلغ الإجمالي: ${parseFloat(order.total || 0).toLocaleString('ar-IQ')} د.ع`;
                message += `\nحالة الطلب: ${this.getStatusText(order.status)}`;
            }
        }

        const encodedMessage = encodeURIComponent(message);
        const whatsappUrl = `https://wa.me/${cleanPhone}?text=${encodedMessage}`;

        window.open(whatsappUrl, '_blank');

        this.showSuccess('تم فتح واتساب للتواصل مع العميل');
    }

    showDeleteModal(orderId) {
        const order = this.orders.find(o => o.id === orderId);
        if (!order) {
            this.showError('لم يتم العثور على الطلب');
            return;
        }

        this.currentOrderToDelete = order;

        const modal = document.getElementById('deleteModal');
        const orderInfo = document.getElementById('deleteOrderInfo');

        if (!modal || !orderInfo) return;

        const orderDate = this.formatArabicDate(order.created_at);

        const orderItems = order.order_items || [];
        const productsCount = orderItems.length;

        orderInfo.innerHTML = `
            <div class="order-summary">
                <p><strong>رقم الطلب:</strong> #${order.id.substring(0, 8)}</p>
                <p><strong>اسم العميل:</strong> ${order.customer_name || 'غير محدد'}</p>
                <p><strong>رقم الهاتف:</strong> ${order.customer_phone || 'غير محدد'}</p>
                <p><strong>عدد المنتجات:</strong> ${productsCount} منتج</p>
                <p><strong>تاريخ الطلب:</strong> ${orderDate}</p>
                <p><strong>المبلغ الإجمالي:</strong> ${parseFloat(order.total || 0).toLocaleString('ar-IQ')} د.ع</p>
                <p><strong>حالة الطلب:</strong> ${this.getStatusText(order.status)}</p>
            </div>
            <div class="warning-note" style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 1rem; margin-top: 1rem; color: #856404;">
                <i class="fas fa-exclamation-triangle" style="margin-left: 0.5rem;"></i>
                <strong>تحذير:</strong> سيتم حذف الطلب وجميع المنتجات المرتبطة به نهائياً من قاعدة البيانات.
            </div>
        `;

        modal.style.display = 'flex';
        modal.classList.add('show');
    }

    hideDeleteModal() {
        const modal = document.getElementById('deleteModal');
        if (modal) {
            modal.style.display = 'none';
            modal.classList.remove('show');
        }
        this.currentOrderToDelete = null;
    }

    async confirmDeleteOrder() {
        if (!this.currentOrderToDelete) {
            this.showError('لم يتم تحديد طلب للحذف');
            return;
        }

        try {
            // Delete order items first
            const { error: itemsError } = await this.supabase
                .from('order_items')
                .delete()
                .eq('order_id', this.currentOrderToDelete.id);

            if (itemsError) throw itemsError;

            // Delete the order
            const { error: orderError } = await this.supabase
                .from('orders')
                .delete()
                .eq('id', this.currentOrderToDelete.id);

            if (orderError) throw orderError;

            // Update local data
            this.orders = this.orders.filter(order => order.id !== this.currentOrderToDelete.id);
            this.filterOrders();
            this.loadOrdersStats();

            this.hideDeleteModal();
            this.showSuccess('تم حذف الطلب بنجاح');

        } catch (error) {
            console.error('Error deleting order:', error);
            this.showError('حدث خطأ في حذف الطلب');
        }
    }

    clearFilters() {
        const searchInput = document.getElementById('searchInput');
        const statusFilter = document.getElementById('statusFilter');

        if (searchInput) searchInput.value = '';
        if (statusFilter) statusFilter.value = '';

        this.filterOrders();
        this.showSuccess('تم مسح الفلاتر');
    }

    async refreshData() {
        try {
            this.showLoading(true);
            await this.loadOrders();
            await this.loadOrdersStats();
            this.showSuccess('تم تحديث البيانات بنجاح');
        } catch (error) {
            console.error('Error refreshing data:', error);
            this.showError('حدث خطأ في تحديث البيانات');
        }
    }
}

// Initialize the orders manager when DOM is loaded
let ordersManager;

document.addEventListener('DOMContentLoaded', function() {
    // Check authentication first
    if (typeof checkAuth === 'function' && !checkAuth()) {
        return;
    }

    // Initialize orders manager
    ordersManager = new OrdersManager();
});

// Global functions for HTML onclick handlers
function refreshOrders() {
    if (ordersManager) {
        ordersManager.refreshData();
    }
}

function closeOrderModal() {
    if (ordersManager) {
        ordersManager.closeOrderModal();
    }
}

function clearFilters() {
    if (ordersManager) {
        ordersManager.clearFilters();
    }
}

function hideDeleteModal() {
    if (ordersManager) {
        ordersManager.hideDeleteModal();
    }
}

function printOrderDetails() {
    if (ordersManager) {
        ordersManager.printOrderDetails();
    }
}

function confirmDeleteOrder() {
    if (ordersManager) {
        ordersManager.confirmDeleteOrder();
    }
}
