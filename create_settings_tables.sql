-- إن<PERSON>ا<PERSON> جداول الإعدادات والملف الشخصي

-- 1. جدول إعدادات الموقع (موجود بالفعل ولكن سنحسنه)
CREATE TABLE IF NOT EXISTS site_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    setting_key TEXT NOT NULL UNIQUE,
    setting_value TEXT,
    setting_group TEXT DEFAULT 'general',
    setting_type TEXT DEFAULT 'text',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. جدول الملف الشخصي للمدير
CREATE TABLE IF NOT EXISTS admin_profile (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    username TEXT NOT NULL UNIQUE,
    email TEXT NOT NULL UNIQUE,
    full_name TEXT,
    phone TEXT,
    avatar_url TEXT,
    password_hash TEXT NOT NULL,
    last_login TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. جدول إعدادات النظام المتقدمة
CREATE TABLE IF NOT EXISTS system_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    setting_name TEXT NOT NULL UNIQUE,
    setting_value JSONB,
    setting_description TEXT,
    is_editable BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. جدول سجل النشاطات
CREATE TABLE IF NOT EXISTS activity_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    admin_id UUID REFERENCES admin_profile(id),
    action TEXT NOT NULL,
    table_name TEXT,
    record_id TEXT,
    old_values JSONB,
    new_values JSONB,
    ip_address TEXT,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_site_settings_key ON site_settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_site_settings_group ON site_settings(setting_group);
CREATE INDEX IF NOT EXISTS idx_admin_profile_username ON admin_profile(username);
CREATE INDEX IF NOT EXISTS idx_admin_profile_email ON admin_profile(email);
CREATE INDEX IF NOT EXISTS idx_system_settings_name ON system_settings(setting_name);
CREATE INDEX IF NOT EXISTS idx_activity_log_admin ON activity_log(admin_id);
CREATE INDEX IF NOT EXISTS idx_activity_log_created ON activity_log(created_at);

-- تفعيل Row Level Security
ALTER TABLE site_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_profile ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_log ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسات الأمان
-- سياسات site_settings
DROP POLICY IF EXISTS "Enable read access for all users" ON site_settings;
CREATE POLICY "Enable read access for all users" ON site_settings FOR SELECT USING (true);

DROP POLICY IF EXISTS "Enable write access for authenticated users" ON site_settings;
CREATE POLICY "Enable write access for authenticated users" ON site_settings FOR ALL USING (true);

-- سياسات admin_profile
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON admin_profile;
CREATE POLICY "Enable read access for authenticated users" ON admin_profile FOR SELECT USING (true);

DROP POLICY IF EXISTS "Enable write access for authenticated users" ON admin_profile;
CREATE POLICY "Enable write access for authenticated users" ON admin_profile FOR ALL USING (true);

-- سياسات system_settings
DROP POLICY IF EXISTS "Enable read access for all users" ON system_settings;
CREATE POLICY "Enable read access for all users" ON system_settings FOR SELECT USING (true);

DROP POLICY IF EXISTS "Enable write access for authenticated users" ON system_settings;
CREATE POLICY "Enable write access for authenticated users" ON system_settings FOR ALL USING (true);

-- سياسات activity_log
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON activity_log;
CREATE POLICY "Enable read access for authenticated users" ON activity_log FOR SELECT USING (true);

DROP POLICY IF EXISTS "Enable insert access for authenticated users" ON activity_log;
CREATE POLICY "Enable insert access for authenticated users" ON activity_log FOR INSERT WITH CHECK (true);

-- إدراج البيانات الأولية للملف الشخصي
INSERT INTO admin_profile (username, email, full_name, phone, password_hash) VALUES
('admin', '<EMAIL>', 'مدير النظام', '***********', '$2b$10$rQJ8vQZ9Z9Z9Z9Z9Z9Z9ZO')
ON CONFLICT (username) DO NOTHING;

-- إدراج إعدادات النظام الأولية
INSERT INTO system_settings (setting_name, setting_value, setting_description, is_editable) VALUES
('site_maintenance', '{"enabled": false, "message": "الموقع تحت الصيانة"}', 'وضع الصيانة للموقع', true),
('backup_settings', '{"auto_backup": true, "backup_frequency": "daily", "retention_days": 30}', 'إعدادات النسخ الاحتياطي', true),
('security_settings', '{"max_login_attempts": 5, "session_timeout": 8, "require_2fa": false}', 'إعدادات الأمان', true),
('notification_settings', '{"email_notifications": true, "sms_notifications": false, "push_notifications": true}', 'إعدادات الإشعارات', true),
('delivery_settings', '{"baghdad_fee": 5000, "other_provinces_fee": 10000, "free_delivery_threshold": 50000}', 'إعدادات التوصيل', true),
('payment_settings', '{"cash_on_delivery": true, "online_payment": false, "payment_methods": ["cash"]}', 'إعدادات الدفع', true)
ON CONFLICT (setting_name) DO NOTHING;

-- إدراج إعدادات الموقع الأولية إذا لم تكن موجودة
INSERT INTO site_settings (setting_key, setting_value, setting_group, setting_type) VALUES
('business_name', 'Care', 'contact', 'text'),
('business_phone', '***********', 'contact', 'tel'),
('business_email', '<EMAIL>', 'contact', 'email'),
('business_address', 'الكرادة، قرب مطعم المحطة، بغداد', 'contact', 'text'),
('working_days', 'السبت - الخميس', 'hours', 'text'),
('working_hours', '10 صباحاً - 5 مساءً', 'hours', 'text'),
('closed_day', 'الجمعة', 'hours', 'text'),
('about_title', 'نبذة عن متجر Care', 'about', 'text'),
('about_description', 'متجر Care هو وجهتك المثالية للحصول على أفضل منتجات العناية بالبشرة والشعر عالية الجودة من أفضل العلامات التجارية العالمية.', 'about', 'textarea'),
('whatsapp_number', '9647713688302', 'social', 'tel'),
('facebook_url', '', 'social', 'url'),
('instagram_url', '', 'social', 'url'),
('telegram_url', '', 'social', 'url'),
('tiktok_url', '', 'social', 'url'),
('youtube_url', '', 'social', 'url'),
('twitter_url', '', 'social', 'url'),
('linkedin_url', '', 'social', 'url'),
('snapchat_url', '', 'social', 'url'),
('site_name', 'Care', 'system', 'text'),
('currency', 'IQD', 'system', 'select'),
('delivery_baghdad', '5000', 'system', 'number'),
('delivery_other', '10000', 'system', 'number')
ON CONFLICT (setting_key) DO NOTHING;
