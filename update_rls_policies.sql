-- Update RLS Policies for Admin Cart Management
-- This file contains the correct RLS policies for discount_codes and delivery_pricing tables

-- First, drop existing policies
DROP POLICY IF EXISTS "Allow read access for discount codes" ON discount_codes;
DROP POLICY IF EXISTS "Allow admin access for discount codes" ON discount_codes;
DROP POLICY IF EXISTS "Allow read access for delivery pricing" ON delivery_pricing;
DROP POLICY IF EXISTS "Allow admin access for delivery pricing" ON delivery_pricing;
DROP POLICY IF EXISTS "Allow admin access for discount usage" ON discount_code_usage;

-- Create new RLS policies for discount_codes table
-- Allow public read access for active discount codes (for cart validation)
CREATE POLICY "Public read access for active discount codes" ON discount_codes
    FOR SELECT USING (is_active = true);

-- Allow authenticated admin users full access to discount codes
CREATE POLICY "Admin full access to discount codes" ON discount_codes
    FOR ALL USING (
        auth.uid() IS NOT NULL AND 
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE email = auth.jwt() ->> 'email' 
            AND is_active = true
        )
    );

-- Create new RLS policies for delivery_pricing table
-- Allow public read access for delivery pricing (for cart calculations)
CREATE POLICY "Public read access for delivery pricing" ON delivery_pricing
    FOR SELECT USING (is_active = true);

-- Allow authenticated admin users full access to delivery pricing
CREATE POLICY "Admin full access to delivery pricing" ON delivery_pricing
    FOR ALL USING (
        auth.uid() IS NOT NULL AND 
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE email = auth.jwt() ->> 'email' 
            AND is_active = true
        )
    );

-- Create RLS policies for discount_code_usage table
-- Allow public insert for tracking discount code usage
CREATE POLICY "Public insert for discount usage tracking" ON discount_code_usage
    FOR INSERT WITH CHECK (true);

-- Allow authenticated admin users full access to discount usage
CREATE POLICY "Admin full access to discount usage" ON discount_code_usage
    FOR ALL USING (
        auth.uid() IS NOT NULL AND 
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE email = auth.jwt() ->> 'email' 
            AND is_active = true
        )
    );

-- Ensure admin_users table has proper email column and RLS
-- Add email column if it doesn't exist
ALTER TABLE admin_users ADD COLUMN IF NOT EXISTS email TEXT UNIQUE;

-- Enable RLS on admin_users table
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- Create policy for admin_users table
DROP POLICY IF EXISTS "Admin users read access" ON admin_users;
CREATE POLICY "Admin users read access" ON admin_users
    FOR SELECT USING (
        auth.uid() IS NOT NULL AND 
        (
            email = auth.jwt() ->> 'email' OR
            EXISTS (
                SELECT 1 FROM admin_users au 
                WHERE au.email = auth.jwt() ->> 'email' 
                AND au.is_active = true
            )
        )
    );

-- Create policy for orders table to allow admin access
-- Enable RLS on orders table if not already enabled
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Public insert for orders" ON orders;
DROP POLICY IF EXISTS "Admin full access to orders" ON orders;

-- Allow public insert for order creation (from shopping cart)
CREATE POLICY "Public insert for orders" ON orders
    FOR INSERT WITH CHECK (true);

-- Allow public read for order verification (limited to order ID only)
CREATE POLICY "Public read for order verification" ON orders
    FOR SELECT USING (true);

-- Allow authenticated admin users full access to orders
CREATE POLICY "Admin full access to orders" ON orders
    FOR ALL USING (
        auth.uid() IS NOT NULL AND 
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE email = auth.jwt() ->> 'email' 
            AND is_active = true
        )
    );

-- Create policy for products table to allow admin management
-- Enable RLS on products table if not already enabled
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Public read access for products" ON products;
DROP POLICY IF EXISTS "Admin full access to products" ON products;

-- Allow public read access for active products
CREATE POLICY "Public read access for products" ON products
    FOR SELECT USING (is_active = true);

-- Allow authenticated admin users full access to products
CREATE POLICY "Admin full access to products" ON products
    FOR ALL USING (
        auth.uid() IS NOT NULL AND 
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE email = auth.jwt() ->> 'email' 
            AND is_active = true
        )
    );

-- Create policy for categories table
-- Enable RLS on categories table if not already enabled
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Public read access for categories" ON categories;
DROP POLICY IF EXISTS "Admin full access to categories" ON categories;

-- Allow public read access for active categories
CREATE POLICY "Public read access for categories" ON categories
    FOR SELECT USING (is_active = true);

-- Allow authenticated admin users full access to categories
CREATE POLICY "Admin full access to categories" ON categories
    FOR ALL USING (
        auth.uid() IS NOT NULL AND 
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE email = auth.jwt() ->> 'email' 
            AND is_active = true
        )
    );

-- Update admin_users table to include sample admin with email
-- This is for testing purposes - in production, use proper user management
INSERT INTO admin_users (username, password, email, is_active) 
VALUES ('admin', 'admin123', '<EMAIL>', true)
ON CONFLICT (username) DO UPDATE SET 
    email = EXCLUDED.email,
    is_active = EXCLUDED.is_active;

-- Create a function to check if user is admin (helper function)
CREATE OR REPLACE FUNCTION is_admin_user()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM admin_users 
        WHERE email = auth.jwt() ->> 'email' 
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION is_admin_user() TO anon, authenticated;

-- Alternative simpler policies using the helper function
-- You can use these instead of the complex policies above

-- DROP POLICY IF EXISTS "Admin full access to discount codes" ON discount_codes;
-- CREATE POLICY "Admin full access to discount codes" ON discount_codes
--     FOR ALL USING (is_admin_user());

-- DROP POLICY IF EXISTS "Admin full access to delivery pricing" ON delivery_pricing;
-- CREATE POLICY "Admin full access to delivery pricing" ON delivery_pricing
--     FOR ALL USING (is_admin_user());

-- Note: The above commented policies are simpler alternatives
-- Uncomment them if you prefer to use the helper function approach
