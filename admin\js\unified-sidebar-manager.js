/**
 * Unified Sidebar Manager for Care Admin Dashboard
 * Standardizes sidebar navigation functionality across all admin pages
 * Ensures consistent behavior on mobile (<768px) and tablet (768px-1024px) devices
 */

class UnifiedSidebarManager {
    constructor() {
        this.sidebar = null;
        this.backdrop = null;
        this.hamburgerBtn = null;
        this.isOpen = false;
        this.isMobile = window.innerWidth <= 1199;
        
        // Standardized configuration
        this.config = {
            breakpoints: {
                mobile: 768,
                tablet: 1024,
                desktop: 1199
            },
            animation: {
                duration: 400,
                easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
            },
            zIndex: {
                sidebar: 2000,
                backdrop: 1999,
                hamburger: 2100
            }
        };
        
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        console.log('🔧 Initializing Unified Sidebar Manager...');
        
        // Find elements using multiple selectors for compatibility
        this.findElements();
        
        if (!this.sidebar) {
            console.error('❌ Sidebar element not found');
            return;
        }

        // Setup event listeners
        this.setupEventListeners();

        // Highlight active navigation
        this.highlightActiveNavigation();

        // Initialize responsive behavior
        this.handleResize();

        // Ensure proper initial state
        this.closeSidebar();

        console.log('✅ Unified Sidebar Manager initialized successfully');
    }

    findElements() {
        // Find sidebar with multiple possible IDs/classes
        this.sidebar = document.getElementById('sidebar') || 
                     document.querySelector('.sidebar') ||
                     document.querySelector('[data-sidebar]');

        // Find backdrop with multiple possible IDs/classes
        this.backdrop = document.getElementById('sidebarBackdrop') || 
                       document.getElementById('sidebar-backdrop') ||
                       document.querySelector('.sidebar-backdrop') ||
                       document.querySelector('[data-sidebar-backdrop]');

        // Find hamburger button with multiple possible IDs/classes
        this.hamburgerBtn = document.getElementById('hamburgerBtn') ||
                           document.getElementById('mobileMenuBtn') ||
                           document.querySelector('.hamburger-btn') ||
                           document.querySelector('.mobile-menu-btn') ||
                           document.querySelector('[data-hamburger]');

        console.log('Elements found:', {
            sidebar: !!this.sidebar,
            backdrop: !!this.backdrop,
            hamburgerBtn: !!this.hamburgerBtn
        });
    }

    setupEventListeners() {
        // Hamburger button click/touch events
        if (this.hamburgerBtn) {
            // Remove existing listeners to prevent conflicts
            this.hamburgerBtn.replaceWith(this.hamburgerBtn.cloneNode(true));
            this.hamburgerBtn = document.getElementById(this.hamburgerBtn.id) || 
                               document.querySelector('.hamburger-btn') ||
                               document.querySelector('.mobile-menu-btn');

            // Add standardized event listeners
            this.hamburgerBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleSidebar();
            });

            // Touch support for mobile devices
            this.hamburgerBtn.addEventListener('touchstart', (e) => {
                e.preventDefault();
                this.toggleSidebar();
            }, { passive: false });

            // Keyboard accessibility
            this.hamburgerBtn.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.toggleSidebar();
                }
            });

            // Ensure button is visible on mobile/tablet
            this.ensureHamburgerVisibility();
        }

        // Backdrop click to close
        if (this.backdrop) {
            this.backdrop.addEventListener('click', () => {
                this.closeSidebar();
            });

            this.backdrop.addEventListener('touchstart', () => {
                this.closeSidebar();
            }, { passive: true });
        }

        // Sidebar link clicks (close on mobile)
        this.setupSidebarLinks();

        // Window resize handling
        window.addEventListener('resize', () => this.handleResize());

        // Escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.closeSidebar();
            }
        });

        // Prevent sidebar from closing when clicking inside it
        if (this.sidebar) {
            this.sidebar.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }
    }

    setupSidebarLinks() {
        const sidebarLinks = document.querySelectorAll('.sidebar-link, .menu-item, [data-sidebar-link]');
        sidebarLinks.forEach(link => {
            link.addEventListener('click', () => {
                // Only close on mobile/tablet, not on logout links
                if (this.isMobile && !link.classList.contains('logout-link')) {
                    setTimeout(() => {
                        this.closeSidebar();
                    }, 150);
                }
            });
        });
    }

    ensureHamburgerVisibility() {
        if (this.hamburgerBtn) {
            // Standardized hamburger button styling
            Object.assign(this.hamburgerBtn.style, {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '44px',
                height: '44px',
                minWidth: '44px',
                minHeight: '44px',
                background: 'transparent',
                border: 'none',
                cursor: 'pointer',
                zIndex: this.config.zIndex.hamburger.toString(),
                position: 'relative',
                transition: 'all 0.3s ease',
                touchAction: 'manipulation'
            });

            // Add ARIA attributes for accessibility
            this.hamburgerBtn.setAttribute('aria-label', this.isOpen ? 'إغلاق القائمة' : 'فتح القائمة');
            this.hamburgerBtn.setAttribute('aria-expanded', this.isOpen.toString());
            this.hamburgerBtn.setAttribute('role', 'button');
        }
    }

    toggleSidebar() {
        if (this.isOpen) {
            this.closeSidebar();
        } else {
            this.openSidebar();
        }
    }

    openSidebar() {
        if (!this.sidebar || this.isOpen) return;

        // Opening mobile sidebar

        // Add active classes
        this.sidebar.classList.add('active');
        if (this.backdrop) this.backdrop.classList.add('active');
        if (this.hamburgerBtn) this.hamburgerBtn.classList.add('active');

        // Prevent body scroll with enhanced method
        this.preventBodyScroll();

        // Update state
        this.isOpen = true;

        // Update ARIA attributes
        if (this.hamburgerBtn) {
            this.hamburgerBtn.setAttribute('aria-expanded', 'true');
            this.hamburgerBtn.setAttribute('aria-label', 'إغلاق القائمة');
        }

        // Focus management for accessibility
        setTimeout(() => {
            const firstFocusable = this.sidebar.querySelector('a, button, [tabindex]:not([tabindex="-1"])');
            if (firstFocusable) {
                firstFocusable.focus();
            }
        }, 100);
    }

    closeSidebar() {
        if (!this.sidebar || !this.isOpen) return;

        console.log('📱 Closing mobile sidebar');

        // Remove active classes
        this.sidebar.classList.remove('active');
        if (this.backdrop) this.backdrop.classList.remove('active');
        if (this.hamburgerBtn) this.hamburgerBtn.classList.remove('active');

        // Restore body scroll
        this.restoreBodyScroll();

        // Update state
        this.isOpen = false;

        // Update ARIA attributes
        if (this.hamburgerBtn) {
            this.hamburgerBtn.setAttribute('aria-expanded', 'false');
            this.hamburgerBtn.setAttribute('aria-label', 'فتح القائمة');
            
            // Return focus to hamburger button
            this.hamburgerBtn.focus();
        }
    }

    preventBodyScroll() {
        const scrollY = window.scrollY;
        document.body.style.position = 'fixed';
        document.body.style.top = `-${scrollY}px`;
        document.body.style.width = '100%';
        document.body.setAttribute('data-scroll-y', scrollY.toString());
    }

    restoreBodyScroll() {
        const scrollY = document.body.getAttribute('data-scroll-y') || '0';
        document.body.style.position = '';
        document.body.style.top = '';
        document.body.style.width = '';
        document.body.removeAttribute('data-scroll-y');
        window.scrollTo(0, parseInt(scrollY, 10));
    }

    highlightActiveNavigation() {
        const currentPage = window.location.pathname.split('/').pop() || 'dashboard.html';

        console.log('🎯 Highlighting active navigation for page:', currentPage);

        // Support both old nav-item and new sidebar-link classes
        const navItems = document.querySelectorAll('.nav-item, .sidebar-link');

        navItems.forEach(item => {
            item.classList.remove('active');
            const href = item.getAttribute('href');
            if (href && href.includes(currentPage)) {
                item.classList.add('active');
                console.log('✅ Set active state for:', href);
            }
        });

        // Default to dashboard if no match found
        if (!document.querySelector('.nav-item.active, .sidebar-link.active')) {
            const dashboardLink = document.querySelector('.nav-item[href*="dashboard"], .sidebar-link[href*="dashboard"]');
            if (dashboardLink) {
                dashboardLink.classList.add('active');
                console.log('✅ Set default active state for dashboard');
            }
        }
    }

    handleResize() {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= this.config.breakpoints.desktop;

        // Close sidebar when switching from mobile to desktop
        if (wasMobile && !this.isMobile && this.isOpen) {
            this.closeSidebar();
        }

        // Update hamburger button visibility
        this.ensureHamburgerVisibility();
    }
}

// Global instance and functions for backward compatibility
let unifiedSidebarManager;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        unifiedSidebarManager = new UnifiedSidebarManager();
        
        // Make available globally
        window.unifiedSidebarManager = unifiedSidebarManager;
        window.sidebarManager = unifiedSidebarManager; // Backward compatibility
    }, 100);
});

// Legacy functions for backward compatibility
function toggleMobileSidebar() {
    if (unifiedSidebarManager) {
        unifiedSidebarManager.toggleSidebar();
    }
}

function openMobileSidebar() {
    if (unifiedSidebarManager) {
        unifiedSidebarManager.openSidebar();
    }
}

function closeMobileSidebar() {
    if (unifiedSidebarManager) {
        unifiedSidebarManager.closeSidebar();
    }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UnifiedSidebarManager;
}
