<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>العروض والخصومات - Care</title>
    <meta name="description" content="اكتشف أفضل العروض والخصومات على منتجات العناية بالبشرة والشعر - عروض محدودة الوقت وأسعار مميزة">
    <meta name="keywords" content="عروض, خصومات, منتجات العناية, البشرة, الشعر, العراق, أسعار مميزة">
    <meta name="author" content="Care">
    <meta name="robots" content="index, follow">
    <meta name="theme-color" content="#4a90a4">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <link href="css/page-header-backgrounds.css" rel="stylesheet">
    <link href="css/standardized-typography.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <style>
        :root {
            /* Design System Colors */
            --primary-color: #4a90a4;
            --secondary-color: #2c3e50;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;

            /* Background Colors */
            --bg-primary: #FFFFFF;
            --bg-secondary: #f8f9fa;
            --bg-dark: #121414;

            /* Text Colors */
            --text-primary: #000000;
            --text-secondary: #2c3e50;
            --text-light: #6c757d;
            --text-white: #FFFFFF;

            /* Spacing System */
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 2rem;
            --spacing-2xl: 3rem;
            --spacing-3xl: 4rem;

            /* Shadows */
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.15);

            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;

            /* Typography */
            --font-family: 'Cairo', sans-serif;
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 2rem;
            --font-size-4xl: 2.5rem;
            --font-size-5xl: 3.5rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            direction: rtl;
            padding-top: 80px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-xl);
        }

        /* Header - Standardized Design Matching Homepage */
        header {
            background: #121414;
            color: white;
            padding: 1.5rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: var(--font-size-4xl);
            font-weight: 700;
            text-decoration: none;
            color: white;
            letter-spacing: 1px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1rem;
            margin: 0;
            padding: 0;
        }

        .nav-menu li a {
            color: white;
            text-decoration: none;
            padding: 0.8rem 1.2rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-menu li a:hover,
        .nav-menu li a.active {
            background: var(--primary-color);
            color: white;
        }

        /* Cart Icon */
        .cart-icon {
            position: relative;
            cursor: pointer;
            padding: 0.8rem;
            border-radius: 50%;
            transition: all 0.3s ease;
            color: white;
        }

        .cart-icon:hover {
            background: rgba(255,255,255,0.1);
        }

        .cart-icon i {
            font-size: var(--font-size-2xl);
        }

        .cart-count {
            position: absolute;
            top: 0;
            right: 0;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-xs);
            font-weight: 600;
        }
        /* Page Header - Standardized Design Matching Contact Us and FAQ */
        .page-header {
            background: linear-gradient(135deg, #2c3e50 0%, #4a90a4 100%);
            color: white;
            text-align: center;
            padding: 6rem 0 4rem;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(ellipse at center, rgba(255,255,255,0.1) 0%, transparent 70%);
            opacity: 0.8;
        }

        .page-header h1 {
            font-size: var(--font-size-5xl);
            font-weight: 700;
            margin-bottom: 2rem;
            position: relative;
            z-index: 2;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .page-header h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, transparent, white, transparent);
            border-radius: 2px;
        }

        .enhanced-text-container {
            position: relative;
            z-index: 2;
            margin-bottom: 2rem;
        }

        .section-subtitle {
            font-size: var(--font-size-xl);
            color: rgba(255,255,255,0.95);
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.7;
            font-weight: 400;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        .offer-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(45deg, #e74c3c, #ff6b6b);
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: var(--font-size-base);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
            animation: pulse 2s infinite;
            position: relative;
            z-index: 2;
        }

        .offer-badge i {
            font-size: var(--font-size-xl);
            animation: flame 1.5s ease-in-out infinite alternate;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes flame {
            0% { transform: rotate(-2deg); }
            100% { transform: rotate(2deg); }
        }

        /* Filters Section - Enhanced Professional Design */
        .filters-section {
            background: #f8f9fa;
            padding: 2rem 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }

        .filters-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
            background: #ffffff;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0,0,0,0.1);
        }

        .search-row {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .search-box {
            position: relative;
            flex: 1;
        }

        .search-box input {
            width: 100%;
            padding: 1rem 1rem 1rem 3.5rem;
            border: 2px solid #4a90a4;
            border-radius: 8px;
            font-size: var(--font-size-base);
            font-family: 'Cairo', sans-serif;
            background: #ffffff;
            color: #000000;
            transition: all 0.3s ease;
        }

        .search-box input:focus {
            outline: none;
            border-color: #2c3e50;
            box-shadow: 0 0 0 3px rgba(74, 144, 164, 0.1);
        }

        .search-box input::placeholder {
            color: #666666;
        }

        .search-box i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #4a90a4;
            font-size: var(--font-size-xl);
        }

        .clear-filters-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: var(--font-size-base);
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            white-space: nowrap;
        }

        .clear-filters-btn:hover {
            background: #c0392b;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .filters-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-label {
            font-weight: 600;
            color: #2c3e50;
            font-size: var(--font-size-sm);
            margin-bottom: 0.5rem;
        }

        .filter-select {
            padding: 1rem;
            border: 2px solid #4a90a4;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-size: var(--font-size-base);
            background: #ffffff;
            color: #000000;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-select:focus {
            outline: none;
            border-color: #2c3e50;
            box-shadow: 0 0 0 3px rgba(74, 144, 164, 0.1);
        }

        .quick-filters {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }

        .quick-filter-btn {
            background: #ffffff;
            color: #4a90a4;
            border: 2px solid #4a90a4;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: var(--font-size-sm);
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            white-space: nowrap;
        }

        .quick-filter-btn:hover {
            background: #4a90a4;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(74, 144, 164, 0.3);
        }

        .quick-filter-btn.active {
            background: #4a90a4;
            color: white;
            box-shadow: 0 4px 8px rgba(74, 144, 164, 0.3);
        }

        /* Products Section - Enhanced Professional Layout */
        .products-section {
            padding: 4rem 0;
            background: #ffffff;
        }

        .products-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 12px;
            border: 1px solid rgba(74, 144, 164, 0.2);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .products-count {
            font-size: var(--font-size-2xl);
            font-weight: 700;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .products-count::before {
            content: '';
            width: 4px;
            height: 30px;
            background: linear-gradient(135deg, #4a90a4, #2c3e50);
            border-radius: 2px;
        }

        .sort-container {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .sort-label {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: #2c3e50;
            white-space: nowrap;
        }

        .sort-select {
            padding: 1rem 1.5rem;
            border: 2px solid #4a90a4;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-size: var(--font-size-base);
            background: #ffffff;
            color: #000000;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 220px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .sort-select:focus {
            outline: none;
            border-color: #2c3e50;
            box-shadow: 0 0 0 3px rgba(74, 144, 164, 0.1);
            transform: translateY(-1px);
        }

        .sort-select:hover {
            border-color: #2c3e50;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        /* Products Grid - Modern Card Layout */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-2xl);
            margin-bottom: var(--spacing-3xl);
        }

        /* Enhanced Professional Product Cards */
        .product-card {
            background: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid rgba(74, 144, 164, 0.1);
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(74, 144, 164, 0.2);
            border-color: #4a90a4;
        }

        .product-image {
            height: 250px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-card:hover .product-image img {
            transform: scale(1.03);
        }

        .product-image i {
            font-size: var(--font-size-4xl);
            color: #cccccc;
        }

        .product-badges-container {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            z-index: 2;
        }

        .discount-badge {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 700;
            font-size: var(--font-size-sm);
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
            animation: pulse 2s infinite;
        }

        .featured-badge {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 15px;
            font-weight: 600;
            font-size: var(--font-size-xs);
            box-shadow: 0 2px 6px rgba(243, 156, 18, 0.3);
        }

        .offer-timer {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 15px;
            font-weight: 600;
            font-size: var(--font-size-xs);
            box-shadow: 0 2px 6px rgba(39, 174, 96, 0.3);
        }

        .product-info {
            padding: 1.5rem;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .product-category {
            color: #4a90a4;
            font-size: var(--font-size-sm);
            font-weight: 600;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .product-name {
            font-size: var(--font-size-lg);
            font-weight: 700;
            color: #000000;
            margin-bottom: 0.75rem;
            line-height: 1.4;
            min-height: 2.8rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .product-description {
            color: #666666;
            font-size: var(--font-size-sm);
            line-height: 1.5;
            margin-bottom: 1rem;
            flex: 1;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .price-section {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 8px;
            border: 1px solid rgba(74, 144, 164, 0.1);
        }

        .current-price {
            font-size: var(--font-size-2xl);
            font-weight: 800;
            color: #e74c3c;
            margin-bottom: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .current-price::before {
            content: '';
            width: 4px;
            height: 20px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border-radius: 2px;
        }

        .original-price {
            font-size: var(--font-size-base);
            color: #999999;
            text-decoration: line-through;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .savings {
            font-size: var(--font-size-sm);
            color: #27ae60;
            font-weight: 700;
            background: rgba(39, 174, 96, 0.1);
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            display: inline-block;
        }

        .add-to-cart {
            width: 100%;
            background: linear-gradient(135deg, #4a90a4, #2c3e50);
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(74, 144, 164, 0.3);
        }

        .add-to-cart::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .add-to-cart:hover::before {
            left: 100%;
        }

        .add-to-cart:hover {
            background: linear-gradient(135deg, #2c3e50, #4a90a4);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(74, 144, 164, 0.4);
        }

        .add-to-cart:disabled {
            background: #cccccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .add-to-cart:disabled::before {
            display: none;
        }

        /* Loading and Empty States */
        .loading {
            text-align: center;
            padding: var(--spacing-3xl);
            color: var(--text-light);
            font-size: var(--font-size-xl);
        }

        .no-products {
            text-align: center;
            padding: var(--spacing-3xl);
            color: var(--text-light);
        }

        .no-products i {
            font-size: var(--font-size-5xl);
            margin-bottom: var(--spacing-lg);
            color: var(--primary-color);
        }

        .no-products h3 {
            font-size: var(--font-size-2xl);
            margin-bottom: var(--spacing-md);
            color: var(--text-secondary);
        }

        .no-products p {
            font-size: var(--font-size-lg);
            line-height: 1.6;
        }

        /* Footer - Standardized Design Matching Homepage */
        footer {
            background: #0f1111;
            color: white;
            padding: 4rem 0 2rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
            margin-bottom: 3rem;
        }

        .footer-section h3 {
            color: #FFFFFF;
            margin-bottom: 2rem;
            font-size: var(--font-size-xl);
            font-weight: 700;
        }

        .footer-section p,
        .footer-section a {
            color: #FFFFFF;
            text-decoration: none;
            line-height: 1.8;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            margin-bottom: 0.8rem;
            transition: all 0.3s ease;
        }

        .footer-section a:hover {
            color: #4a90a4;
        }

        .footer-section i {
            font-size: var(--font-size-lg);
            color: #4a90a4;
            min-width: 20px;
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .social-links a:hover {
            border-color: #4a90a4;
            background: rgba(74, 144, 164, 0.2);
        }

        .social-links a i {
            font-size: var(--font-size-xl);
            min-width: auto;
        }

        .footer-bottom {
            text-align: center;
            padding: 2rem 0;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: #FFFFFF;
        }

        .business-name {
            color: #4a90a4;
            font-weight: 700;
        }

        /* Utility Classes */
        .text-center {
            text-align: center;
        }

        .mb-0 {
            margin-bottom: 0;
        }

        .mt-2 {
            margin-top: var(--spacing-lg);
        }

        /* Accessibility Improvements */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus States */
        *:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* Smooth Scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Notification System */
        .cart-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.4s ease;
        }

        .cart-notification.show {
            opacity: 1;
            transform: translateX(0);
        }

        .cart-notification.hide {
            opacity: 0;
            transform: translateX(100%);
        }

        .notification-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(74, 144, 164, 0.2);
            padding: 1.5rem;
            min-width: 350px;
            max-width: 400px;
            position: relative;
            overflow: hidden;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--font-size-xl);
            margin-bottom: 1rem;
        }

        .notification-title {
            font-size: var(--font-size-lg);
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .notification-message {
            color: #666666;
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .notification-actions {
            display: flex;
            gap: 0.75rem;
        }

        .notification-btn {
            padding: 0.75rem 1rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: var(--font-size-sm);
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .notification-btn.primary {
            background: linear-gradient(135deg, #4a90a4, #2c3e50);
            color: white;
        }

        .notification-btn.primary:hover {
            background: linear-gradient(135deg, #2c3e50, #4a90a4);
            transform: translateY(-1px);
        }

        .notification-btn.secondary {
            background: #f8f9fa;
            color: #666666;
            border: 1px solid #e9ecef;
        }

        .notification-btn.secondary:hover {
            background: #e9ecef;
            color: #495057;
        }

        .notification-close {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: #999999;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .notification-close:hover {
            background: #f8f9fa;
            color: #666666;
        }

        .notification-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: linear-gradient(90deg, #4a90a4, #2c3e50);
            animation: progress 5s linear;
        }

        @keyframes progress {
            from { width: 100%; }
            to { width: 0%; }
        }

        /* ===== COMPREHENSIVE RESPONSIVE STYLES ===== */

        /* Mobile Styles (320px - 768px) */
        @media (max-width: 767px) {
            /* Offers Section */
            .offers-section {
                padding: var(--spacing-mobile-xl) 0;
            }

            .offers-header {
                padding: 0 var(--spacing-mobile-md);
                margin-bottom: var(--spacing-mobile-xl);
                text-align: center;
            }

            .offers-header h2 {
                font-size: var(--font-size-mobile-xl);
                margin-bottom: var(--spacing-mobile-lg);
            }

            .offers-header p {
                font-size: var(--font-size-mobile-base);
                line-height: 1.6;
            }

            /* Offers Grid */
            .offers-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-mobile-lg);
                padding: 0 var(--spacing-mobile-md);
            }

            .offer-card {
                border-radius: 12px;
                padding: var(--spacing-mobile-lg);
            }

            .offer-card h3 {
                font-size: var(--font-size-mobile-lg);
                margin-bottom: var(--spacing-mobile-sm);
            }

            .offer-card p {
                font-size: var(--font-size-mobile-sm);
                margin-bottom: var(--spacing-mobile-md);
                line-height: 1.5;
            }

            .offer-price {
                font-size: var(--font-size-mobile-lg);
                margin-bottom: var(--spacing-mobile-md);
            }

            .offer-btn {
                padding: var(--spacing-mobile-lg);
                font-size: var(--font-size-mobile-base);
                min-height: 48px;
                width: 100%;
            }

            /* Filters Section */
            .filters-section {
                padding: var(--spacing-mobile-lg) 0;
            }

            .filters-container {
                flex-direction: column;
                gap: var(--spacing-mobile-md);
                padding: 0 var(--spacing-mobile-md);
            }

            .filter-group {
                width: 100%;
            }

            .filter-select {
                padding: var(--spacing-mobile-lg);
                font-size: var(--font-size-mobile-base);
                min-height: 48px;
                width: 100%;
            }
        }

        /* Tablet Styles (768px - 1024px) */
        @media (min-width: 768px) and (max-width: 1023px) {
            /* Offers Section */
            .offers-section {
                padding: var(--spacing-tablet-xxl) 0;
            }

            .offers-header {
                margin-bottom: var(--spacing-tablet-xxl);
                text-align: center;
            }

            .offers-header h2 {
                font-size: var(--font-size-tablet-2xl);
                margin-bottom: var(--spacing-tablet-lg);
            }

            .offers-header p {
                font-size: var(--font-size-tablet-base);
                line-height: 1.7;
                max-width: 600px;
                margin: 0 auto;
            }

            /* Offers Grid */
            .offers-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: var(--spacing-tablet-lg);
            }

            .offer-card {
                border-radius: 15px;
                padding: var(--spacing-tablet-xl);
            }

            .offer-card h3 {
                font-size: var(--font-size-tablet-lg);
                margin-bottom: var(--spacing-tablet-sm);
            }

            .offer-card p {
                font-size: var(--font-size-tablet-sm);
                margin-bottom: var(--spacing-tablet-md);
                line-height: 1.6;
            }

            .offer-price {
                font-size: var(--font-size-tablet-lg);
                margin-bottom: var(--spacing-tablet-md);
            }

            .offer-btn {
                padding: var(--spacing-tablet-lg);
                font-size: var(--font-size-tablet-base);
                min-width: 150px;
            }

            /* Filters Section */
            .filters-section {
                padding: var(--spacing-tablet-xl) 0;
            }

            .filters-container {
                gap: var(--spacing-tablet-lg);
                justify-content: center;
            }

            .filter-group {
                min-width: 200px;
            }

            .filter-select {
                padding: var(--spacing-tablet-lg);
                font-size: var(--font-size-tablet-base);
            }
        }

        /* Print Styles */
        @media print {
            header,
            .filters-section,
            .add-to-cart,
            .cart-notification,
            footer {
                display: none;
            }

            .page-header {
                padding: 2rem 0;
                background: none;
                color: var(--text-primary);
            }

            .product-card {
                break-inside: avoid;
                page-break-inside: avoid;
                box-shadow: none;
                border: 1px solid var(--text-primary);
            }
        }
    </style>
</head>
<body>
    <header role="banner">
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo business-name" aria-label="الصفحة الرئيسية" data-setting="business_name">Care</a>



                <nav>
                    <ul class="nav-menu" id="navMenu">
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="offers.html" class="active">العروض</a></li>
                        <li><a href="guidelines.html">الإرشادات</a></li>
                        <li><a href="faq.html">الأسئلة الشائعة</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                    </ul>
                </nav>
                <div class="cart-icon" onclick="window.location.href='cart.html'" role="button" aria-label="سلة التسوق" tabindex="0">
                    <i class="fas fa-shopping-cart" aria-hidden="true"></i>
                    <span class="cart-count" id="cartCount">0</span>
                </div>
            </div>
        </div>


    </header>

    <section class="page-header offers-bg" id="offersPageHeader">
        <div class="page-header-decoration"></div>
        <div class="container">
            <div class="page-header-content">
                <nav class="breadcrumb" aria-label="مسار التنقل">
                    <a href="index.html">الرئيسية</a>
                    <span class="separator">←</span>
                    <span class="current">العروض والخصومات</span>
                </nav>
                <h1>العروض والخصومات</h1>
                <p class="offers-page-description" data-setting="offers_page_description">اكتشف أفضل العروض على منتجات العناية بالبشرة والشعر</p>
                <div class="offer-badge" style="margin-top: 1rem; display: inline-flex; align-items: center; gap: 0.5rem; background: rgba(255,255,255,0.1); padding: 0.5rem 1rem; border-radius: 20px; font-size: var(--font-size-sm); font-weight: 600;">
                    <i class="fas fa-fire" style="color: #ff6b35;"></i>
                    عروض محدودة الوقت
                </div>
            </div>
        </div>
    </section>

    <section class="filters-section">
        <div class="container">
            <div class="filters-container">
                <div class="search-row">
                    <div class="search-box">
                        <input type="text" id="searchInput" placeholder="ابحث في العروض..." autocomplete="off">
                        <i class="fas fa-search"></i>
                    </div>
                    <button class="clear-filters-btn" onclick="clearAllFilters()">
                        <i class="fas fa-times"></i>
                        مسح الفلاتر
                    </button>
                </div>

                <div class="filters-row">
                    <div class="filter-group">
                        <label class="filter-label">الفئة</label>
                        <select id="categoryFilter" class="filter-select">
                            <option value="">جميع الفئات</option>
                            <option value="skincare">العناية بالبشرة</option>
                            <option value="haircare">العناية بالشعر</option>
                            <option value="makeup">المكياج</option>
                            <option value="fragrance">العطور</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label class="filter-label">نسبة الخصم</label>
                        <select id="discountFilter" class="filter-select">
                            <option value="">جميع الخصومات</option>
                            <option value="10">خصم 10% فأكثر</option>
                            <option value="20">خصم 20% فأكثر</option>
                            <option value="30">خصم 30% فأكثر</option>
                            <option value="50">خصم 50% فأكثر</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label class="filter-label">السعر</label>
                        <select id="priceFilter" class="filter-select">
                            <option value="">جميع الأسعار</option>
                            <option value="0-25000">أقل من 25,000 د.ع</option>
                            <option value="25000-50000">25,000 - 50,000 د.ع</option>
                            <option value="50000-100000">50,000 - 100,000 د.ع</option>
                            <option value="100000+">أكثر من 100,000 د.ع</option>
                        </select>
                    </div>
                </div>

                <div class="quick-filters">
                    <button class="quick-filter-btn" id="featuredFilter" onclick="toggleFilter('featured')">
                        <i class="fas fa-star"></i>
                        المنتجات المميزة
                    </button>
                    <button class="quick-filter-btn" id="newFilter" onclick="toggleFilter('new')">
                        <i class="fas fa-sparkles"></i>
                        المنتجات الجديدة
                    </button>
                    <button class="quick-filter-btn" id="bigSavingsFilter" onclick="toggleFilter('bigSavings')">
                        <i class="fas fa-fire"></i>
                        وفورات كبيرة
                    </button>
                    <button class="quick-filter-btn" id="availableFilter" onclick="toggleFilter('available')">
                        <i class="fas fa-check-circle"></i>
                        متوفر فقط
                    </button>
                </div>
            </div>
        </div>
    </section>

    <main class="products-section">
        <div class="container">
            <div class="products-header">
                <div class="products-count" id="productsCount">
                    <i class="fas fa-tags"></i>
                    جاري التحميل...
                </div>
                <div class="sort-container">
                    <label class="sort-label">ترتيب حسب:</label>
                    <select id="sortSelect" class="sort-select">
                        <option value="discount">أعلى خصم</option>
                        <option value="price_low">السعر: من الأقل للأعلى</option>
                        <option value="price_high">السعر: من الأعلى للأقل</option>
                        <option value="name">ترتيب حسب الاسم</option>
                    </select>
                </div>
            </div>

            <div class="loading" id="loading">جاري تحميل العروض...</div>

            <div class="products-grid" id="productsGrid">
                <!-- Products will be loaded here -->
            </div>

            <div class="no-products" id="noProducts" style="display: none;">
                <i class="fas fa-search"></i>
                <h3>لا توجد عروض</h3>
                <p>لم يتم العثور على عروض تطابق معايير البحث</p>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>معلومات التواصل</h3>
                    <p><i class="fas fa-map-marker-alt"></i> <span class="business-address" data-setting="business_address">الكرادة، قرب مطعم المحطة</span></p>
                    <p><i class="fas fa-phone"></i> <span class="business-phone" data-setting="business_phone">***********</span></p>
                    <p><i class="fas fa-envelope"></i> <span class="business-email" data-setting="business_email"><EMAIL></span></p>
                </div>
                <div class="footer-section">
                    <h3>أوقات العمل</h3>
                    <p><span class="working-days" data-setting="working_days">السبت - الخميس</span>: <span class="working-hours" data-setting="working_hours">10 صباحاً - 5 مساءً</span></p>
                    <p><span class="closed-day" data-setting="closed_day">الجمعة</span>: مغلق</p>
                </div>
                <div class="footer-section">
                    <h3>تابعنا على وسائل التواصل</h3>
                    <div class="social-links">
                        <a href="#" class="whatsapp-link" target="_blank" title="واتساب" style="color: #25d366;" aria-label="تواصل معنا عبر واتساب">
                            <i class="fab fa-whatsapp" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="facebook-link" target="_blank" title="فيسبوك" style="color: #1877f2; display: none;" aria-label="تابعنا على فيسبوك">
                            <i class="fab fa-facebook-f" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="instagram-link" target="_blank" title="إنستغرام" style="color: #e4405f; display: none;" aria-label="تابعنا على إنستغرام">
                            <i class="fab fa-instagram" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="twitter-link" target="_blank" title="تويتر" style="color: #1da1f2; display: none;" aria-label="تابعنا على تويتر">
                            <i class="fab fa-twitter" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="telegram-link" target="_blank" title="تيليجرام" style="color: #0088cc; display: none;" aria-label="تابعنا على تيليجرام">
                            <i class="fab fa-telegram-plane" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="linkedin-link" target="_blank" title="لينكد إن" style="color: #0077b5; display: none;" aria-label="تابعنا على لينكد إن">
                            <i class="fab fa-linkedin-in" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="tiktok-link" target="_blank" title="تيك توك" style="color: #ff0050; display: none;" aria-label="تابعنا على تيك توك">
                            <i class="fab fa-tiktok" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="youtube-link" target="_blank" title="يوتيوب" style="color: #ff0000; display: none;" aria-label="تابعنا على يوتيوب">
                            <i class="fab fa-youtube" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="snapchat-link" target="_blank" title="سناب شات" style="color: #fffc00; display: none;" aria-label="تابعنا على سناب شات">
                            <i class="fab fa-snapchat-ghost" aria-hidden="true"></i>
                        </a>
                    </div>
                    <div style="margin-top: 1.5rem;">
                        <p><a href="terms.html"><i class="fas fa-file-contract"></i>الشروط والأحكام</a></p>
                        <p><a href="terms.html"><i class="fas fa-shield-alt"></i>سياسة الخصوصية</a></p>
                        <p><a href="terms.html"><i class="fas fa-undo-alt"></i>سياسة الإرجاع</a></p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy;<span class="business-name" data-setting="business_name">Care</span>. <span class="copyright-text" data-setting="copyright_text">جميع الحقوق محفوظة</span>.</p>
            </div>
        </div>
    </footer>

    <!-- Shared Supabase Configuration (must load first) -->
    <script src="js/supabase-config.js"></script>

    <!-- Site Settings Script -->
    <script src="js/site-settings.js"></script>
    <!-- Offers Page Script -->
    <script src="js/offers.js"></script>

    <script>

    </script>
</body>
</html>
