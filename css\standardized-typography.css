/* Standardized Typography System for All Pages */
/* This file provides consistent typography variables and styles across the entire website */

:root {
    /* Enhanced Typography Scale */
    --font-family: 'Cairo', sans-serif;
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 2rem;      /* 32px */
    --font-size-4xl: 2.5rem;    /* 40px */
    --font-size-5xl: 3.5rem;    /* 56px */

    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
}

/* Standardized Typography Classes */

/* Page Headers (H1) - Used across all pages */
.page-header h1,
.standardized-h1 {
    font-family: var(--font-family);
    font-size: var(--font-size-5xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    letter-spacing: 1px;
}

/* Section Headers (H2) */
.standardized-h2,
h2.standardized {
    font-family: var(--font-family);
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    letter-spacing: -0.025em;
}

/* Subsection Headers (H3) */
.standardized-h3,
h3.standardized {
    font-family: var(--font-family);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-normal);
}

/* Body Text */
.standardized-body,
.body-text {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
    font-weight: var(--font-weight-normal);
}

/* Large Body Text */
.standardized-body-lg {
    font-family: var(--font-family);
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
    font-weight: var(--font-weight-normal);
}

/* Small Text */
.standardized-small {
    font-family: var(--font-family);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-normal);
}

/* Extra Small Text */
.standardized-xs {
    font-family: var(--font-family);
    font-size: var(--font-size-xs);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-normal);
}

/* Navigation Typography */
.nav-menu li a,
.standardized-nav {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
}

/* Logo Typography */
.logo,
.standardized-logo {
    font-family: var(--font-family);
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    letter-spacing: 1px;
}

/* Button Typography */
.standardized-btn,
button.standardized {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-normal);
}

/* Large Button Typography */
.standardized-btn-lg {
    font-family: var(--font-family);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-normal);
}

/* Small Button Typography */
.standardized-btn-sm {
    font-family: var(--font-family);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-normal);
}

/* Icon Typography */
.standardized-icon-sm {
    font-size: var(--font-size-lg);
}

.standardized-icon-md {
    font-size: var(--font-size-2xl);
}

.standardized-icon-lg {
    font-size: var(--font-size-3xl);
}

.standardized-icon-xl {
    font-size: var(--font-size-5xl);
}

/* Footer Typography */
.footer-section h3,
.standardized-footer-title {
    font-family: var(--font-family);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
}

.footer-section p,
.footer-section a,
.standardized-footer-text {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
}

.footer-section i,
.standardized-footer-icon {
    font-size: var(--font-size-lg);
}

.social-links a i,
.standardized-social-icon {
    font-size: var(--font-size-xl);
}

/* Product Card Typography */
.product-name,
.standardized-product-name {
    font-family: var(--font-family);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
}

.product-description,
.standardized-product-description {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
}

.product-price,
.standardized-product-price {
    font-family: var(--font-family);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-extrabold);
}

/* Notification Typography */
.notification-title,
.standardized-notification-title {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
}

.notification-message,
.standardized-notification-message {
    font-family: var(--font-family);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
}

.notification-icon,
.standardized-notification-icon {
    font-size: var(--font-size-2xl);
}

/* Form Typography */
.form-label,
.standardized-form-label {
    font-family: var(--font-family);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
}

.form-input,
.standardized-form-input {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
}

/* Cart Typography */
.cart-count,
.standardized-cart-count {
    font-family: var(--font-family);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
}

.cart-icon i,
.standardized-cart-icon {
    font-size: var(--font-size-2xl);
}

/* Message Typography */
.message-icon,
.standardized-message-icon {
    font-size: var(--font-size-3xl);
}

.error-icon,
.standardized-error-icon {
    font-size: var(--font-size-3xl);
}

.loading-icon,
.standardized-loading-icon {
    font-size: var(--font-size-3xl);
}

/* Category and Tag Typography */
.category-tag,
.standardized-category-tag {
    font-family: var(--font-family);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

/* Section Title Typography */
.section-title,
.standardized-section-title {
    font-family: var(--font-family);
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
}

.section-subtitle,
.standardized-section-subtitle {
    font-family: var(--font-family);
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
}
