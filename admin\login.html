<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - لوحة التحكم</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script type="text/javascript" src="js/secure-admin-config.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #4a90a4 0%, #357a8a 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            margin: 2rem;
        }

        .login-header {
            background: linear-gradient(135deg, #4a90a4 0%, #357a8a 100%);
            color: white;
            text-align: center;
            padding: 2rem;
        }

        .login-header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .login-header p {
            opacity: 0.9;
            font-size: 1rem;
        }

        .login-form {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-family: 'Cairo', sans-serif;
            font-size: 1rem;
            transition: all 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #82877a;
            box-shadow: 0 0 0 3px rgba(130, 135, 122, 0.1);
        }

        .password-field {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            font-size: 1.1rem;
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #4a90a4 0%, #357a8a 100%);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            font-family: 'Cairo', sans-serif;
            margin-bottom: 1rem;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(74, 144, 164, 0.3);
        }

        .login-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border: 1px solid #f5c6cb;
            display: none;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border: 1px solid #c3e6cb;
            display: none;
        }

        .back-link {
            text-align: center;
            margin-top: 1rem;
        }

        .back-link a {
            color: #82877a;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s;
        }

        .back-link a:hover {
            color: #6b7062;
        }

        .loading {
            display: none;
            text-align: center;
            color: #82877a;
            margin: 1rem 0;
        }

        .loading i {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 480px) {
            .login-container {
                margin: 1rem;
                border-radius: 15px;
            }

            .login-header {
                padding: 1.5rem;
            }

            .login-header h1 {
                font-size: 1.5rem;
            }

            .login-form {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1><i class="fas fa-shield-alt"></i> لوحة التحكم</h1>
            <p>متجر Care - إدارة المحتوى</p>
        </div>
        
        <div class="login-form">
            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>
            <div class="loading" id="loading">
                <i class="fas fa-spinner"></i>
                جاري التحقق...
            </div>
            
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <input type="text" id="username" name="username" required autocomplete="username">
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <div class="password-field">
                        <input type="password" id="password" name="password" required autocomplete="current-password">
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="fas fa-eye" id="passwordIcon"></i>
                        </button>
                    </div>
                </div>
                
                <button type="submit" class="login-btn" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
            </form>
            
            <div class="back-link">
                <a href="../index.html">
                    <i class="fas fa-arrow-right"></i>
                    العودة للموقع الرئيسي
                </a>
            </div>
        </div>
    </div>

    <script>
        // Supabase configuration
        const SUPABASE_URL = 'https://krqijjttwllohulmdwgs.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70';
        
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const passwordIcon = document.getElementById('passwordIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                passwordIcon.className = 'fas fa-eye';
            }
        }

        function showMessage(message, type = 'error') {
            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');
            
            if (type === 'error') {
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
                successDiv.style.display = 'none';
            } else {
                successDiv.textContent = message;
                successDiv.style.display = 'block';
                errorDiv.style.display = 'none';
            }
            
            // Hide message after 5 seconds
            setTimeout(() => {
                errorDiv.style.display = 'none';
                successDiv.style.display = 'none';
            }, 5000);
        }

        function setLoading(loading) {
            const loadingDiv = document.getElementById('loading');
            const loginBtn = document.getElementById('loginBtn');
            
            if (loading) {
                loadingDiv.style.display = 'block';
                loginBtn.disabled = true;
                loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحقق...';
            } else {
                loadingDiv.style.display = 'none';
                loginBtn.disabled = false;
                loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> تسجيل الدخول';
            }
        }

        async function handleLogin(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const username = formData.get('username').trim();
            const password = formData.get('password');
            
            if (!username || !password) {
                showMessage('يرجى ملء جميع الحقول');
                return;
            }
            
            setLoading(true);
            
            try {
                // Use secure admin client for login
                const result = await window.secureAdminClient.adminLogin(username, password);

                if (result.error) {
                    showMessage(result.error.message || 'حدث خطأ أثناء تسجيل الدخول');
                    setLoading(false);
                    return;
                }

                showMessage('تم تسجيل الدخول بنجاح! جاري التوجيه...', 'success');

                // Redirect to dashboard
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1500);

            } catch (error) {
                console.error('Login error:', error);
                showMessage('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.');
                setLoading(false);
            }
        }

        // Check if already logged in
        async function checkExistingSession() {
            try {
                // Check if there's an active Supabase session
                const isAuthenticated = await window.secureAdminClient.checkAuthentication();
                if (isAuthenticated) {
                    console.log('✅ Existing admin session found, redirecting to dashboard');
                    window.location.href = 'dashboard.html';
                    return;
                }

                // Clean up any stale session data
                sessionStorage.removeItem('adminUser');
                console.log('No valid admin session found');

            } catch (error) {
                console.error('Error checking existing session:', error);
                sessionStorage.removeItem('adminUser');
            }
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', async function() {
            await checkExistingSession();

            const loginForm = document.getElementById('loginForm');
            loginForm.addEventListener('submit', handleLogin);

            // Enter key support
            document.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    const loginBtn = document.getElementById('loginBtn');
                    if (!loginBtn.disabled) {
                        loginForm.dispatchEvent(new Event('submit'));
                    }
                }
            });
        });
    </script>
</body>
</html>
