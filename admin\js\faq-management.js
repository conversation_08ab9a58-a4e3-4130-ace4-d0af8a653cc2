/**
 * FAQ Management System for Care Admin Dashboard
 * نظام إدارة الأسئلة الشائعة للوحة التحكم الإدارية
 *
 * Features:
 * - Load and display FAQs from Supabase
 * - Add, edit, delete FAQ items
 * - Category management
 * - Display order controls
 * - Enable/disable toggles
 * - Search and filtering
 * - Professional Arabic localization
 * - Comprehensive error handling
 */

class FAQManager {
    constructor() {
        this.supabase = null;
        this.faqs = [];
        this.filteredFAQs = [];
        this.currentEditId = null;
        this.categories = ['عام', 'منتجات', 'طلبات', 'توصيل'];
        
        this.init();
    }

    async init() {
        try {
            // Initialize Supabase client
            this.supabase = window.supabase.createClient(
                'https://krqijjttwllohulmdwgs.supabase.co',
                'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70'
            );

            // Load FAQs
            await this.loadFAQs();
            
            // Setup event listeners
            this.setupEventListeners();
            
            console.log('FAQ Manager initialized successfully');
        } catch (error) {
            console.error('Error initializing FAQ Manager:', error);
            this.showError('خطأ في تهيئة نظام إدارة الأسئلة الشائعة');
        }
    }

    setupEventListeners() {
        // Search input
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', () => this.filterFAQs());
        }

        // Category filter
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', () => this.filterFAQs());
        }

        // Status filter
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => this.filterFAQs());
        }
    }

    async loadFAQs() {
        try {
            this.showLoading(true);

            const { data, error } = await this.supabase
                .from('faq')
                .select('*')
                .order('order_index', { ascending: true });

            if (error) throw error;

            this.faqs = data || [];
            this.filteredFAQs = [...this.faqs];
            
            this.updateStatistics();
            this.renderFAQTable();
            
        } catch (error) {
            console.error('Error loading FAQs:', error);
            this.showError('خطأ في تحميل الأسئلة الشائعة');
        } finally {
            this.showLoading(false);
        }
    }

    updateStatistics() {
        const totalFAQs = this.faqs.length;
        const activeFAQs = this.faqs.filter(faq => faq.is_active).length;
        const inactiveFAQs = totalFAQs - activeFAQs;
        const uniqueCategories = [...new Set(this.faqs.map(faq => faq.category))].length;

        document.getElementById('totalFAQs').textContent = totalFAQs;
        document.getElementById('activeFAQs').textContent = activeFAQs;
        document.getElementById('inactiveFAQs').textContent = inactiveFAQs;
        document.getElementById('totalCategories').textContent = uniqueCategories;
    }

    filterFAQs() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const categoryFilter = document.getElementById('categoryFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;

        this.filteredFAQs = this.faqs.filter(faq => {
            const matchesSearch = !searchTerm || 
                faq.question.toLowerCase().includes(searchTerm) ||
                faq.answer.toLowerCase().includes(searchTerm);
            
            const matchesCategory = !categoryFilter || faq.category === categoryFilter;
            
            const matchesStatus = !statusFilter || 
                faq.is_active.toString() === statusFilter;

            return matchesSearch && matchesCategory && matchesStatus;
        });

        this.renderFAQTable();
    }

    renderFAQTable() {
        const container = document.getElementById('faqTableContent');
        const emptyState = document.getElementById('emptyState');

        if (this.filteredFAQs.length === 0) {
            container.innerHTML = '';
            emptyState.style.display = 'block';
            return;
        }

        emptyState.style.display = 'none';

        const tableHTML = `
            <table class="faq-table">
                <thead>
                    <tr>
                        <th>الترتيب</th>
                        <th>السؤال</th>
                        <th>الفئة</th>
                        <th>الحالة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.filteredFAQs.map(faq => this.renderFAQRow(faq)).join('')}
                </tbody>
            </table>
        `;

        container.innerHTML = tableHTML;
    }

    renderFAQRow(faq) {
        const createdDate = new Date(faq.created_at).toLocaleDateString('ar-SA');
        const statusClass = faq.is_active ? 'status-active' : 'status-inactive';
        const statusText = faq.is_active ? 'نشط' : 'غير نشط';
        const toggleText = faq.is_active ? 'إلغاء التفعيل' : 'تفعيل';
        const toggleClass = faq.is_active ? 'btn-toggle' : 'btn-toggle inactive';

        return `
            <tr>
                <td>${faq.order_index || 0}</td>
                <td>
                    <div style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" 
                         title="${faq.question}">
                        ${faq.question}
                    </div>
                </td>
                <td>
                    <span class="category-badge">${faq.category || 'غير محدد'}</span>
                </td>
                <td>
                    <span class="status-badge ${statusClass}">${statusText}</span>
                </td>
                <td>${createdDate}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-action btn-edit" onclick="faqManager.editFAQ('${faq.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-action ${toggleClass}" onclick="faqManager.toggleFAQStatus('${faq.id}')" title="${toggleText}">
                            <i class="fas fa-${faq.is_active ? 'eye-slash' : 'eye'}"></i>
                        </button>
                        <button class="btn-action btn-delete" onclick="faqManager.deleteFAQ('${faq.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    async toggleFAQStatus(id) {
        try {
            const faq = this.faqs.find(f => f.id === id);
            if (!faq) return;

            const { error } = await this.supabase
                .from('faq')
                .update({ 
                    is_active: !faq.is_active,
                    updated_at: new Date().toISOString()
                })
                .eq('id', id);

            if (error) throw error;

            this.showSuccess(`تم ${faq.is_active ? 'إلغاء تفعيل' : 'تفعيل'} السؤال بنجاح`);
            await this.loadFAQs();

        } catch (error) {
            console.error('Error toggling FAQ status:', error);
            this.showError('خطأ في تغيير حالة السؤال');
        }
    }

    async deleteFAQ(id) {
        if (!confirm('هل أنت متأكد من حذف هذا السؤال؟ لا يمكن التراجع عن هذا الإجراء.')) {
            return;
        }

        try {
            const { error } = await this.supabase
                .from('faq')
                .delete()
                .eq('id', id);

            if (error) throw error;

            this.showSuccess('تم حذف السؤال بنجاح');
            await this.loadFAQs();

        } catch (error) {
            console.error('Error deleting FAQ:', error);
            this.showError('خطأ في حذف السؤال');
        }
    }

    editFAQ(id) {
        const faq = this.faqs.find(f => f.id === id);
        if (!faq) return;

        this.currentEditId = id;
        this.showFAQModal(faq);
    }

    showAddFAQModal() {
        this.currentEditId = null;
        this.showFAQModal();
    }

    showFAQModal(faq = null) {
        const isEdit = faq !== null;
        const modalTitle = isEdit ? 'تعديل السؤال الشائع' : 'إضافة سؤال شائع جديد';

        const modalHTML = `
            <div id="faqModal" class="modal-overlay" onclick="this.style.display='none'">
                <div class="modal-content" onclick="event.stopPropagation()">
                    <div class="modal-header">
                        <h3>${modalTitle}</h3>
                        <button class="modal-close" onclick="document.getElementById('faqModal').style.display='none'">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="faqForm">
                            <div class="form-group">
                                <label for="faqQuestion">السؤال *</label>
                                <textarea id="faqQuestion" name="question" rows="3" required
                                    placeholder="اكتب السؤال هنا...">${faq ? faq.question : ''}</textarea>
                            </div>

                            <div class="form-group">
                                <label for="faqAnswer">الإجابة *</label>
                                <textarea id="faqAnswer" name="answer" rows="5" required
                                    placeholder="اكتب الإجابة هنا...">${faq ? faq.answer : ''}</textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="faqCategory">الفئة *</label>
                                    <select id="faqCategory" name="category" required>
                                        <option value="">اختر الفئة</option>
                                        ${this.categories.map(cat =>
                                            `<option value="${cat}" ${faq && faq.category === cat ? 'selected' : ''}>${cat}</option>`
                                        ).join('')}
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="faqOrder">ترتيب العرض</label>
                                    <input type="number" id="faqOrder" name="order_index" min="0"
                                        value="${faq ? faq.order_index || 0 : 0}" placeholder="0">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="faqActive" name="is_active"
                                        ${faq ? (faq.is_active ? 'checked' : '') : 'checked'}>
                                    <span class="checkmark"></span>
                                    نشط (يظهر للعملاء)
                                </label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="document.getElementById('faqModal').style.display='none'">
                            إلغاء
                        </button>
                        <button type="button" class="btn btn-primary" onclick="faqManager.saveFAQ()">
                            <i class="fas fa-save"></i>
                            ${isEdit ? 'حفظ التعديلات' : 'إضافة السؤال'}
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('faqModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Show modal
        document.getElementById('faqModal').style.display = 'flex';
    }

    async saveFAQ() {
        try {
            const form = document.getElementById('faqForm');
            const formData = new FormData(form);

            const faqData = {
                question: formData.get('question').trim(),
                answer: formData.get('answer').trim(),
                category: formData.get('category'),
                order_index: parseInt(formData.get('order_index')) || 0,
                is_active: formData.get('is_active') === 'on',
                updated_at: new Date().toISOString()
            };

            // Validation
            if (!faqData.question || !faqData.answer || !faqData.category) {
                this.showError('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            let result;
            if (this.currentEditId) {
                // Update existing FAQ
                result = await this.supabase
                    .from('faq')
                    .update(faqData)
                    .eq('id', this.currentEditId);
            } else {
                // Create new FAQ
                faqData.created_at = new Date().toISOString();
                result = await this.supabase
                    .from('faq')
                    .insert([faqData]);
            }

            if (result.error) throw result.error;

            this.showSuccess(this.currentEditId ? 'تم تحديث السؤال بنجاح' : 'تم إضافة السؤال بنجاح');

            // Close modal and reload data
            document.getElementById('faqModal').style.display = 'none';
            await this.loadFAQs();

        } catch (error) {
            console.error('Error saving FAQ:', error);
            this.showError('خطأ في حفظ السؤال');
        }
    }

    showLoading(show) {
        const spinner = document.getElementById('loadingSpinner');
        if (spinner) {
            spinner.style.display = show ? 'flex' : 'none';
        }
    }

    showSuccess(message) {
        // Simple success notification - can be enhanced with a proper notification system
        alert(message);
    }

    showError(message) {
        // Simple error notification - can be enhanced with a proper notification system
        alert(message);
    }
}

// Global FAQ Manager instance
let faqManager;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication first
    if (typeof checkAuth === 'function' && !checkAuth()) {
        return;
    }

    // Initialize FAQ Manager
    faqManager = new FAQManager();
});

// Global functions for onclick handlers
window.showAddFAQModal = function() {
    if (faqManager) {
        faqManager.showAddFAQModal();
    }
};
