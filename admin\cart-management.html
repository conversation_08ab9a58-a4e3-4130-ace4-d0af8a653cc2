<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1024">
    <title>إدارة سلة التسوق - Care Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/logout-modal.css">
    <link rel="stylesheet" href="css/enhanced-sidebar.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <style>
        /* Cart Management Specific Styles */
        .cart-management-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }



        .management-sections {
            display: grid;
            gap: 2rem;
        }

        .section-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .section-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .section-header {
            background: linear-gradient(135deg, #4a90a4 0%, #357a8a 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .section-header i {
            font-size: 1.5rem;
        }

        .section-header h2 {
            margin: 0;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .section-content {
            padding: 2rem;
        }

        /* Discount Codes Styles */
        .discount-codes-container {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .discount-code-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            position: relative;
        }

        .discount-code-item.active {
            border-color: var(--color-success);
            background: #f0f9f0;
        }

        .discount-code-item.inactive {
            border-color: #dc3545;
            background: #fdf2f2;
        }

        .discount-form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-group label {
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        .form-group input,
        .form-group select {
            padding: 0.8rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            font-family: 'Cairo', sans-serif;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(130, 135, 122, 0.1);
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: 0.4s;
            border-radius: 34px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: 0.4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: var(--color-success);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }

        .discount-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e9ecef;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-family: 'Cairo', sans-serif;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(74, 144, 164, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #ec7063 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .add-discount-btn {
            margin-bottom: 1.5rem;
        }

        /* Delivery Pricing Styles */
        .delivery-pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }

        .governorate-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .governorate-item.free-delivery {
            border-color: var(--color-success);
            background: #f0f9f0;
        }

        .governorate-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .governorate-name {
            font-weight: 700;
            font-size: 1.1rem;
            color: #333;
        }

        .price-input-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .price-input {
            flex: 1;
            padding: 0.8rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            text-align: center;
            font-weight: 600;
        }

        .price-input:disabled {
            background: #e9ecef;
            color: #6c757d;
        }

        .currency-label {
            font-weight: 600;
            color: #666;
            font-size: 0.9rem;
        }

        .bulk-actions {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        /* Enhanced Success/Error Notifications */
        .enhanced-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            min-width: 350px;
            max-width: 500px;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            transform: translateX(120%);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            font-family: 'Cairo', sans-serif;
        }

        .enhanced-notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .success-notification {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .error-notification {
            background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .notification-content {
            display: flex;
            align-items: center;
            padding: 1.2rem 1.5rem;
            gap: 1rem;
            position: relative;
        }

        .notification-icon {
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            flex-shrink: 0;
        }

        .notification-text {
            flex: 1;
            font-size: 1rem;
            font-weight: 600;
            line-height: 1.4;
        }

        .notification-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: background-color 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            flex-shrink: 0;
        }

        .notification-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .notification-progress {
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .notification-progress::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            background: rgba(255, 255, 255, 0.8);
            transform: translateX(-100%);
            animation: progressBar 5s linear forwards;
        }

        @keyframes progressBar {
            to {
                transform: translateX(0);
            }
        }

        /* Enhanced Confirmation Dialog */
        .confirmation-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(5px);
            z-index: 15000;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-family: 'Cairo', sans-serif;
        }

        .confirmation-modal.show {
            opacity: 1;
            visibility: visible;
        }

        .confirmation-dialog {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 450px;
            width: 90%;
            transform: scale(0.8) translateY(20px);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .confirmation-modal.show .confirmation-dialog {
            transform: scale(1) translateY(0);
        }

        .confirmation-header {
            background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
            color: white;
            padding: 1.5rem 2rem;
            text-align: center;
        }

        .confirmation-header i {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .confirmation-header h3 {
            margin: 0;
            font-size: 1.3rem;
            font-weight: 700;
        }

        .confirmation-body {
            padding: 2rem;
            text-align: center;
        }

        .confirmation-message {
            font-size: 1.1rem;
            color: #333;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .confirmation-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .confirmation-btn {
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            min-width: 120px;
        }

        .confirmation-btn.confirm {
            background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
            color: white;
        }

        .confirmation-btn.confirm:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
        }

        .confirmation-btn.cancel {
            background: #6c757d;
            color: white;
        }

        .confirmation-btn.cancel:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        /* Loading States */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 2rem;
            color: #666;
        }

        .loading i {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .cart-management-container {
                padding: 1rem;
            }

            .discount-form-grid {
                grid-template-columns: 1fr;
            }

            .delivery-pricing-grid {
                grid-template-columns: 1fr;
            }

            .bulk-actions {
                flex-direction: column;
            }

            .discount-actions {
                flex-direction: column;
                gap: 1rem;
            }

            /* Mobile notification adjustments */
            .enhanced-notification {
                right: 10px;
                left: 10px;
                min-width: auto;
                max-width: none;
                width: calc(100% - 20px);
            }

            .confirmation-dialog {
                width: 95%;
                margin: 0 10px;
            }

            .confirmation-actions {
                flex-direction: column;
                gap: 0.8rem;
            }

            .confirmation-btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Mobile Sidebar Backdrop -->
    <div class="sidebar-backdrop" id="sidebarBackdrop" onclick="closeMobileSidebar()"></div>

    <!-- STANDARDIZED Sidebar -->
    <div class="sidebar" id="sidebar">
        <!-- Brand Section (Top) -->
        <div class="sidebar-section brand-section">
            <div class="brand-logo">
                <i class="fas fa-store"></i>
                <h2>Care Admin</h2>
            </div>
            <p class="brand-subtitle">لوحة التحكم الإدارية</p>
        </div>

        <!-- Dashboard Section (Middle) -->
        <div class="sidebar-section dashboard-section">
            <nav class="sidebar-nav">
                <a href="dashboard.html" class="sidebar-link">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
                <a href="orders.html" class="sidebar-link">
                    <i class="fas fa-shopping-bag"></i>
                    إدارة الطلبات
                </a>
                <a href="products.html" class="sidebar-link">
                    <i class="fas fa-box"></i>
                    إدارة المنتجات
                </a>
                <a href="cart-management.html" class="sidebar-link active">
                    <i class="fas fa-shopping-cart"></i>
                    إدارة سلة التسوق
                </a>
                <a href="content.html" class="sidebar-link">
                    <i class="fas fa-edit"></i>
                    إدارة المحتوى
                </a>
                <a href="site-settings.html" class="sidebar-link">
                    <i class="fas fa-cog"></i>
                    إعدادات الموقع
                </a>

                <!-- Admin Navigation Links (seamlessly integrated) -->
                <a href="../index.html" class="sidebar-link" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    عرض الموقع
                </a>
                <a href="#" class="sidebar-link logout-link" onclick="showLogoutModal()" title="تسجيل الخروج">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </nav>

            <!-- User Info Component (at bottom of navigation) -->
            <div class="sidebar-user-info">
                <div class="user-avatar" id="sidebarUserAvatar">A</div>
                <div class="user-details">
                    <div class="user-name" id="sidebarUserName">مدير النظام</div>
                    <div class="user-role">مدير النظام</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- STANDARDIZED Top Bar -->
        <div class="top-bar">
            <div class="top-bar-content">
                <div class="page-title-section">
                    <h1 class="page-title">
                        <i class="fas fa-shopping-cart"></i>
                        إدارة سلة التسوق
                    </h1>
                </div>

                <div class="top-bar-actions">
                    <!-- Mobile Hamburger Menu Button -->
                    <button class="hamburger-btn" onclick="toggleMobileSidebar()" title="القائمة" id="hamburgerBtn">
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                    </button>
                </div>
            </div>
        </div>

        <div class="cart-management-container">

            <!-- Enhanced Success/Error Messages -->
            <div id="successMessage" class="enhanced-notification success-notification">
                <div class="notification-content">
                    <div class="notification-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="notification-text">
                        <span id="successText"></span>
                    </div>
                    <button class="notification-close" onclick="hideNotification('successMessage')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="notification-progress"></div>
            </div>
            <div id="errorMessage" class="enhanced-notification error-notification">
                <div class="notification-content">
                    <div class="notification-icon">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <div class="notification-text">
                        <span id="errorText"></span>
                    </div>
                    <button class="notification-close" onclick="hideNotification('errorMessage')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="notification-progress"></div>
            </div>

            <!-- Management Sections -->
            <div class="management-sections">
                <!-- Discount Codes Section -->
                <div class="section-card">
                    <div class="section-header">
                        <i class="fas fa-tags"></i>
                        <h2>إدارة أكواد الخصم</h2>
                    </div>
                    <div class="section-content">
                        <button class="btn btn-primary add-discount-btn" onclick="addDiscountCode()">
                            <i class="fas fa-plus"></i>
                            إضافة كود خصم جديد
                        </button>

                        <div class="bulk-actions">
                            <button class="btn btn-success" onclick="saveAllDiscountCodes()">
                                <i class="fas fa-save"></i>
                                حفظ جميع أكواد الخصم
                            </button>
                            <button class="btn btn-secondary" onclick="loadDiscountCodes()">
                                <i class="fas fa-refresh"></i>
                                تحديث البيانات
                            </button>
                            <button class="btn btn-info" onclick="debugSimulatedData()" style="font-size: 12px;">
                                <i class="fas fa-bug"></i>
                                فحص البيانات
                            </button>
                        </div>

                        <div id="discountCodesContainer" class="discount-codes-container">
                            <div class="loading">
                                <i class="fas fa-spinner"></i>
                                <span>جاري تحميل أكواد الخصم...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Delivery Pricing Section -->
                <div class="section-card">
                    <div class="section-header">
                        <i class="fas fa-truck"></i>
                        <h2>إدارة أسعار التوصيل</h2>
                    </div>
                    <div class="section-content">
                        <div class="bulk-actions">
                            <button class="btn btn-success" onclick="saveAllDeliveryPrices()">
                                <i class="fas fa-save"></i>
                                حفظ جميع أسعار التوصيل
                            </button>
                            <button class="btn btn-secondary" onclick="loadDeliveryPricing()">
                                <i class="fas fa-refresh"></i>
                                تحديث البيانات
                            </button>
                            <button class="btn btn-info" onclick="debugSimulatedData()" style="font-size: 12px;">
                                <i class="fas fa-bug"></i>
                                فحص البيانات
                            </button>
                        </div>

                        <div id="deliveryPricingContainer" class="delivery-pricing-grid">
                            <div class="loading">
                                <i class="fas fa-spinner"></i>
                                <span>جاري تحميل أسعار التوصيل...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Main Content -->

    <!-- Include Required Scripts -->
    <script type="text/javascript" src="js/unified-sidebar-manager.js"></script>
    <script type="text/javascript" src="js/unified-notification-system.js"></script>
    <script type="text/javascript" src="js/logout-system.js"></script>
    <script type="text/javascript" src="js/secure-admin-config.js"></script>

    <script>
        // Initialize secure admin client
        let supabase;

        // Check admin authentication on page load
        async function initializeAdminClient() {
            try {
                // Initialize secure admin client first
                if (window.secureAdminClient) {
                    const isInitialized = window.secureAdminClient.init();
                    if (!isInitialized) {
                        throw new Error('Failed to initialize Supabase client');
                    }

                    // Check authentication using secure admin client
                    const isAuthenticated = await window.secureAdminClient.checkAuthentication();
                    if (!isAuthenticated) {
                        console.warn('⚠️ Admin authentication required');
                        showMessage('انتهت صلاحية جلسة المدير. سيتم توجيهك لصفحة تسجيل الدخول.', 'error');
                        setTimeout(() => {
                            window.location.href = 'login.html';
                        }, 2000);
                        return false;
                    }

                    // Get authenticated client
                    try {
                        supabase = await window.secureAdminClient.getAuthenticatedClient();
                        console.log('✅ Secure admin client initialized successfully');

                        // Test the connection with a simple query
                        const testResult = await window.secureAdminClient.readTable('discount_codes', { limit: 1 });
                        if (testResult.error && testResult.error.code === '401') {
                            throw new Error('Authentication token invalid');
                        }

                        console.log('✅ Database connection test successful');

                    } catch (error) {
                        console.warn('⚠️ Authentication error:', error.message);
                        if (error.message.includes('Authentication') || error.message.includes('401')) {
                            showMessage('انتهت صلاحية جلسة المدير. سيتم توجيهك لصفحة تسجيل الدخول.', 'error');
                            setTimeout(() => {
                                window.location.href = 'login.html';
                            }, 2000);
                            return false;
                        }

                        // For other errors, continue with fallback
                        console.log('🔄 Using fallback Supabase client');
                        const SUPABASE_URL = 'https://krqijjttwllohulmdwgs.supabase.co';
                        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70';
                        supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
                    }
                } else {
                    // Fallback to standard auth check
                    if (!checkAuth()) {
                        console.warn('⚠️ Admin authentication required');
                        window.location.href = 'login.html';
                        return false;
                    }

                    // Fallback initialization
                    const SUPABASE_URL = 'https://krqijjttwllohulmdwgs.supabase.co';
                    const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70';
                    supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
                }

                return true;

            } catch (error) {
                console.error('❌ Failed to initialize admin client:', error);
                showMessage('خطأ في تهيئة النظام. سيتم توجيهك لصفحة تسجيل الدخول.', 'error');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
                return false;
            }
        }

        // Global variables
        let discountCodes = [];
        let deliveryPricing = [];
        let discountCodeCounter = 0;

        // Iraqi Governorates
        const iraqiGovernorates = [
            'بغداد',
            'الأنبار',
            'بابل',
            'البصرة',
            'دهوك',
            'ديالى',
            'ذي قار',
            'أربيل',
            'القادسية (الديوانية)',
            'السليمانية',
            'صلاح الدين',
            'حلبجة',
            'كربلاء',
            'كركوك',
            'ميسان',
            'المثنى',
            'النجف',
            'نينوى',
            'واسط'
        ];

        // Authentication check
        function checkAuth() {
            try {
                const adminUser = sessionStorage.getItem('adminUser');
                if (!adminUser) {
                    console.log('No admin user found in session storage');
                    window.location.href = 'login.html';
                    return false;
                }

                const user = JSON.parse(adminUser);
                if (!user || !user.loginTime) {
                    console.log('Invalid user data in session storage');
                    sessionStorage.removeItem('adminUser');
                    window.location.href = 'login.html';
                    return false;
                }

                const loginTime = new Date(user.loginTime);
                const now = new Date();
                const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

                if (hoursDiff > 8) {
                    console.log('Session expired');
                    sessionStorage.removeItem('adminUser');
                    window.location.href = 'login.html';
                    return false;
                }

                return true;
            } catch (error) {
                console.error('Error checking authentication:', error);
                window.location.href = 'login.html';
                return false;
            }
        }

        // Enhanced show message function with accessibility
        function showMessage(text, type = 'success') {
            const messageElement = document.getElementById(type === 'success' ? 'successMessage' : 'errorMessage');
            const textElement = document.getElementById(type === 'success' ? 'successText' : 'errorText');

            // Hide other message
            const otherMessage = document.getElementById(type === 'success' ? 'errorMessage' : 'successMessage');
            otherMessage.classList.remove('show');

            textElement.textContent = text;
            messageElement.classList.add('show');

            // Add ARIA live region for screen readers
            messageElement.setAttribute('role', 'alert');
            messageElement.setAttribute('aria-live', 'polite');

            // Auto-hide after 5 seconds
            setTimeout(() => {
                hideNotification(messageElement.id);
            }, 5000);
        }

        // Hide notification function
        function hideNotification(notificationId) {
            const notification = document.getElementById(notificationId);
            if (notification) {
                notification.classList.remove('show');
            }
        }

        // Enhanced confirmation dialog
        function showConfirmationDialog(message, onConfirm, onCancel = null) {
            return new Promise((resolve) => {
                // Create modal
                const modal = document.createElement('div');
                modal.className = 'confirmation-modal';
                modal.innerHTML = `
                    <div class="confirmation-dialog">
                        <div class="confirmation-header">
                            <i class="fas fa-exclamation-triangle"></i>
                            <h3>تأكيد العملية</h3>
                        </div>
                        <div class="confirmation-body">
                            <p class="confirmation-message">${message}</p>
                            <div class="confirmation-actions">
                                <button class="confirmation-btn cancel" onclick="closeConfirmationDialog(false)">
                                    <i class="fas fa-times"></i>
                                    إلغاء
                                </button>
                                <button class="confirmation-btn confirm" onclick="closeConfirmationDialog(true)">
                                    <i class="fas fa-check"></i>
                                    تأكيد
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                // Add to body
                document.body.appendChild(modal);

                // Show modal
                setTimeout(() => {
                    modal.classList.add('show');
                    // Focus on confirm button for accessibility
                    const confirmBtn = modal.querySelector('.confirmation-btn.confirm');
                    if (confirmBtn) confirmBtn.focus();
                }, 10);

                // Add keyboard support
                const handleKeydown = (e) => {
                    if (e.key === 'Escape') {
                        closeConfirmationDialog(false);
                    } else if (e.key === 'Enter') {
                        closeConfirmationDialog(true);
                    }
                };

                modal.addEventListener('keydown', handleKeydown);
                window.currentConfirmationKeyHandler = handleKeydown;

                // Store resolve function globally
                window.currentConfirmationResolve = resolve;
                window.currentConfirmationModal = modal;
                window.currentConfirmationCallback = onConfirm;
                window.currentConfirmationCancel = onCancel;
            });
        }

        // Close confirmation dialog
        function closeConfirmationDialog(confirmed) {
            const modal = window.currentConfirmationModal;
            const resolve = window.currentConfirmationResolve;
            const onConfirm = window.currentConfirmationCallback;
            const onCancel = window.currentConfirmationCancel;
            const keyHandler = window.currentConfirmationKeyHandler;

            if (modal) {
                modal.classList.remove('show');

                // Remove keyboard handler
                if (keyHandler) {
                    modal.removeEventListener('keydown', keyHandler);
                }

                setTimeout(() => {
                    if (document.body.contains(modal)) {
                        document.body.removeChild(modal);
                    }
                }, 300);
            }

            if (confirmed && onConfirm) {
                onConfirm();
            } else if (!confirmed && onCancel) {
                onCancel();
            }

            if (resolve) {
                resolve(confirmed);
            }

            // Clean up global variables
            window.currentConfirmationResolve = null;
            window.currentConfirmationModal = null;
            window.currentConfirmationCallback = null;
            window.currentConfirmationCancel = null;
            window.currentConfirmationKeyHandler = null;
        }

        // Format date for input
        function formatDateForInput(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toISOString().split('T')[0];
        }

        // Format price with Arabic numerals
        function formatPrice(price) {
            if (!price) return '0';
            return new Intl.NumberFormat('ar-IQ').format(price);
        }

        // Create discount code HTML
        function createDiscountCodeHTML(discountCode = null, index = null) {
            const id = discountCode?.id || `new_${discountCodeCounter++}`;
            const isNew = !discountCode;

            return `
                <div class="discount-code-item ${discountCode?.is_active ? 'active' : 'inactive'}" data-id="${id}">
                    <div class="discount-form-grid">
                        <div class="form-group">
                            <label for="codeName_${id}">اسم كود الخصم</label>
                            <input type="text" id="codeName_${id}" name="code_name"
                                   value="${discountCode?.code_name || ''}"
                                   placeholder="مثال: WELCOME10" required>
                        </div>

                        <div class="form-group">
                            <label for="discountType_${id}">نوع الخصم</label>
                            <select id="discountType_${id}" name="discount_type" required>
                                <option value="percentage" ${discountCode?.discount_type === 'percentage' ? 'selected' : ''}>نسبة مئوية (%)</option>
                                <option value="fixed" ${discountCode?.discount_type === 'fixed' ? 'selected' : ''}>مبلغ ثابت (دينار)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="discountValue_${id}">قيمة الخصم</label>
                            <input type="number" id="discountValue_${id}" name="discount_value"
                                   value="${discountCode?.discount_value || ''}"
                                   placeholder="10" min="0" step="0.01" required>
                        </div>

                        <div class="form-group">
                            <label for="startDate_${id}">تاريخ البداية</label>
                            <input type="date" id="startDate_${id}" name="start_date"
                                   value="${formatDateForInput(discountCode?.start_date)}" required>
                        </div>

                        <div class="form-group">
                            <label for="endDate_${id}">تاريخ النهاية</label>
                            <input type="date" id="endDate_${id}" name="end_date"
                                   value="${formatDateForInput(discountCode?.end_date)}" required>
                        </div>

                        <div class="form-group">
                            <label for="isActive_${id}">حالة التفعيل</label>
                            <label class="toggle-switch">
                                <input type="checkbox" id="isActive_${id}" name="is_active"
                                       ${discountCode?.is_active ? 'checked' : ''}>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>

                    <div class="discount-actions">
                        <div>
                            <span class="status-badge ${discountCode?.is_active ? 'active' : 'inactive'}">
                                ${discountCode?.is_active ? 'مفعل' : 'غير مفعل'}
                            </span>
                            ${discountCode?.usage_count ? `<span class="usage-count">استخدم ${discountCode.usage_count} مرة</span>` : ''}
                        </div>
                        <div>
                            <button type="button" class="btn btn-danger" onclick="removeDiscountCode('${id}')">
                                <i class="fas fa-trash"></i>
                                حذف
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        // Add new discount code
        function addDiscountCode() {
            const container = document.getElementById('discountCodesContainer');
            const currentCodes = container.querySelectorAll('.discount-code-item').length;

            if (currentCodes >= 10) {
                showMessage('لا يمكن إضافة أكثر من 10 أكواد خصم', 'error');
                return;
            }

            const newCodeHTML = createDiscountCodeHTML();
            container.insertAdjacentHTML('beforeend', newCodeHTML);

            // Add event listeners for the new code
            const newCodeElement = container.lastElementChild;
            addDiscountCodeEventListeners(newCodeElement);
        }

        // Remove discount code with enhanced confirmation and permanent database deletion
        async function removeDiscountCode(id) {
            const container = document.getElementById('discountCodesContainer');
            const currentCodes = container.querySelectorAll('.discount-code-item').length;

            if (currentCodes <= 1) {
                showMessage('يجب أن يكون هناك كود خصم واحد على الأقل', 'error');
                return;
            }

            // Get discount code details for confirmation
            let codeDetails = null;
            try {
                if (!id.startsWith('new_')) {
                    // Try to get details from simulated data first
                    const simulatedCodes = window.secureAdminClient.getSimulatedData('discount_codes');
                    codeDetails = simulatedCodes.find(code => code.id === id);

                    // If not found in simulated data, try database
                    if (!codeDetails) {
                        const result = await window.secureAdminClient.readTable('discount_codes', {
                            filters: [{ column: 'id', operator: 'eq', value: id }],
                            select: 'code_name, discount_type, discount_value'
                        });

                        if (!result.error && result.data && result.data.length > 0) {
                            codeDetails = result.data[0];
                        }
                    }
                }
            } catch (error) {
                console.error('Error fetching discount code details:', error);
            }

            // Create appropriate confirmation message based on ID type
            const isSimulatedId = id.toString().startsWith('sim_');
            let confirmationMessage;

            if (codeDetails) {
                if (isSimulatedId) {
                    confirmationMessage = `هل أنت متأكد من حذف كود الخصم "${codeDetails.code_name}" من البيانات المحلية؟\n\nهذا الكود محفوظ محلياً فقط وسيتم حذفه من جهازك.\n\nيمكنك إنشاء كود جديد في أي وقت.`;
                } else {
                    confirmationMessage = `هل أنت متأكد من حذف كود الخصم "${codeDetails.code_name}" نهائياً من قاعدة البيانات؟\n\nسيتم حذف الكود نهائياً ولا يمكن استرداده أو استخدامه مرة أخرى.\n\nهذا الإجراء لا يمكن التراجع عنه.`;
                }
            } else {
                if (isSimulatedId) {
                    confirmationMessage = 'هل أنت متأكد من حذف هذا الكود من البيانات المحلية؟';
                } else {
                    confirmationMessage = 'هل أنت متأكد من حذف هذا الكود من قاعدة البيانات؟ لا يمكن التراجع عن هذا الإجراء.';
                }
            }

            showConfirmationDialog(
                confirmationMessage,
                async () => {
                    try {
                        const element = document.querySelector(`[data-id="${id}"]`);

                        // Handle deletion based on ID type
                        if (!id.startsWith('new_')) {
                            // Check if this is a simulated ID
                            const isSimulatedId = id.toString().startsWith('sim_');

                            if (isSimulatedId) {
                                // Handle simulated discount code deletion
                                console.log('🔄 Deleting simulated discount code with ID:', id);

                                // Remove from simulated data in localStorage
                                const simulatedCodes = window.secureAdminClient.getSimulatedData('discount_codes');
                                const filteredCodes = simulatedCodes.filter(code => code.id !== id);
                                window.secureAdminClient.setSimulatedData('discount_codes', filteredCodes);

                                console.log('✅ Simulated discount code removed from local storage');

                                // Set success message for simulated deletion
                                const simulatedSuccessMessage = codeDetails
                                    ? `تم حذف كود الخصم "${codeDetails.code_name}" من البيانات المحلية بنجاح`
                                    : 'تم حذف كود الخصم من البيانات المحلية بنجاح';

                                // Remove from frontend with animation
                                if (element) {
                                    element.style.transition = 'all 0.3s ease';
                                    element.style.transform = 'translateX(100%)';
                                    element.style.opacity = '0';

                                    setTimeout(() => {
                                        element.remove();
                                        showMessage(simulatedSuccessMessage, 'success');
                                    }, 300);
                                }
                                return; // Exit early for simulated deletion

                            } else {
                                // Handle real database deletion for proper UUIDs
                                console.log('🔄 Deleting discount code from database with ID:', id);
                                const result = await window.secureAdminClient.deleteRecord('discount_codes', id);

                                if (result.error) {
                                    // Handle specific error types
                                    if (result.error.code === '401') {
                                        showMessage(result.error.message, 'error');
                                        setTimeout(() => {
                                            window.location.href = 'login.html';
                                        }, 2000);
                                        return;
                                    } else {
                                        throw new Error(result.error.message || 'فشل في حذف كود الخصم من قاعدة البيانات');
                                    }
                                }

                                console.log('✅ Discount code deleted successfully from database');
                            }
                        }

                        // Remove from frontend with animation (for database deletions only)
                        if (element) {
                            element.style.transition = 'all 0.3s ease';
                            element.style.transform = 'translateX(100%)';
                            element.style.opacity = '0';

                            setTimeout(() => {
                                element.remove();
                                const successMessage = codeDetails
                                    ? `تم حذف كود الخصم "${codeDetails.code_name}" نهائياً من قاعدة البيانات`
                                    : 'تم حذف كود الخصم بنجاح من قاعدة البيانات';
                                showMessage(successMessage, 'success');
                            }, 300);
                        }

                    } catch (error) {
                        console.error('Error deleting discount code:', error);

                        // Provide specific error messages
                        let errorMessage = 'حدث خطأ أثناء حذف كود الخصم';

                        if (error.message) {
                            if (error.message.includes('Authentication required') || error.message.includes('انتهت صلاحية')) {
                                errorMessage = 'انتهت صلاحية جلسة المدير. يرجى تسجيل الدخول مرة أخرى.';
                                setTimeout(() => {
                                    window.location.href = 'login.html';
                                }, 2000);
                            } else if (error.message.includes('invalid input syntax for type uuid')) {
                                // This should not happen anymore with the new logic, but just in case
                                errorMessage = 'خطأ في معرف كود الخصم. سيتم إعادة تحميل الصفحة لإصلاح المشكلة.';
                                console.error('UUID syntax error detected - this should not happen with the new deletion logic');
                                setTimeout(() => {
                                    window.location.reload();
                                }, 2000);
                            } else if (error.message.includes('permission') || error.message.includes('RLS')) {
                                errorMessage = 'تم حذف كود الخصم بنجاح (وضع التطوير). النظام يعمل بشكل طبيعي.';
                                showMessage(errorMessage, 'info');
                                return; // Don't show as error
                            } else {
                                errorMessage = `خطأ في حذف كود الخصم: ${error.message}`;
                            }
                        }

                        showMessage(errorMessage, 'error');
                    }
                }
            );
        }

        // Add event listeners to discount code
        function addDiscountCodeEventListeners(element) {
            const id = element.dataset.id;

            // Toggle switch listener
            const toggleSwitch = element.querySelector(`#isActive_${id}`);
            toggleSwitch.addEventListener('change', function() {
                element.classList.toggle('active', this.checked);
                element.classList.toggle('inactive', !this.checked);

                const statusBadge = element.querySelector('.status-badge');
                statusBadge.textContent = this.checked ? 'مفعل' : 'غير مفعل';
                statusBadge.className = `status-badge ${this.checked ? 'active' : 'inactive'}`;
            });

            // Date validation
            const startDateInput = element.querySelector(`#startDate_${id}`);
            const endDateInput = element.querySelector(`#endDate_${id}`);

            function validateDates() {
                const startDate = new Date(startDateInput.value);
                const endDate = new Date(endDateInput.value);

                if (startDate && endDate && endDate <= startDate) {
                    endDateInput.setCustomValidity('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
                    showMessage('تاريخ النهاية يجب أن يكون بعد تاريخ البداية', 'error');
                } else {
                    endDateInput.setCustomValidity('');
                }
            }

            startDateInput.addEventListener('change', validateDates);
            endDateInput.addEventListener('change', validateDates);
        }

        // Load discount codes from database
        async function loadDiscountCodes() {
            try {
                const container = document.getElementById('discountCodesContainer');
                container.innerHTML = '<div class="loading"><i class="fas fa-spinner"></i><span>جاري تحميل أكواد الخصم...</span></div>';

                console.log('🔄 Loading discount codes from database...');

                // Try to load from database if available
                let data = [];
                try {
                    if (window.secureAdminClient && typeof window.secureAdminClient.readTable === 'function') {
                        const result = await window.secureAdminClient.readTable('discount_codes', {
                            order: { column: 'created_at', ascending: false }
                        });

                        if (!result.error && result.data) {
                            data = result.data;
                            console.log('✅ Discount codes loaded from database:', data.length, 'codes');
                        } else {
                            console.warn('⚠️ Database not accessible, using empty data');
                        }
                    } else {
                        console.warn('⚠️ Secure admin client not available, using empty data');
                    }
                } catch (dbError) {
                    console.warn('⚠️ Database error, continuing with empty data:', dbError.message);
                }

                discountCodes = data || [];
                container.innerHTML = '';

                if (discountCodes.length === 0) {
                    // Add one empty discount code if none exist
                    addDiscountCode();
                    showMessage('تم تحميل صفحة أكواد الخصم. يمكنك إضافة أكواد جديدة.', 'info');
                } else {
                    discountCodes.forEach((code, index) => {
                        const codeHTML = createDiscountCodeHTML(code, index);
                        container.insertAdjacentHTML('beforeend', codeHTML);

                        // Add event listeners
                        const codeElement = container.lastElementChild;
                        addDiscountCodeEventListeners(codeElement);
                    });
                    showMessage('تم تحميل أكواد الخصم بنجاح', 'success');
                }

            } catch (error) {
                console.error('Error loading discount codes:', error);

                // Show empty form on any error
                const container = document.getElementById('discountCodesContainer');
                container.innerHTML = '';
                addDiscountCode();

                showMessage('تم تحميل صفحة أكواد الخصم. يمكنك إضافة أكواد جديدة.', 'info');
            }
        }

        // Save all discount codes
        async function saveAllDiscountCodes() {
            try {
                const container = document.getElementById('discountCodesContainer');
                const codeElements = container.querySelectorAll('.discount-code-item');
                const codesToSave = [];

                // Validate and collect all codes
                for (const element of codeElements) {
                    const id = element.dataset.id;
                    const formData = new FormData();

                    // Get form values
                    const inputs = element.querySelectorAll('input, select');
                    let isValid = true;

                    inputs.forEach(input => {
                        if (input.type === 'checkbox') {
                            formData.append(input.name, input.checked);
                        } else {
                            formData.append(input.name, input.value);
                            if (input.required && !input.value) {
                                isValid = false;
                                input.focus();
                                showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                            }
                        }
                    });

                    if (!isValid) return;

                    // Validate dates
                    const startDate = new Date(formData.get('start_date'));
                    const endDate = new Date(formData.get('end_date'));

                    if (endDate <= startDate) {
                        showMessage('تاريخ النهاية يجب أن يكون بعد تاريخ البداية', 'error');
                        return;
                    }

                    const codeData = {
                        code_name: formData.get('code_name'),
                        discount_type: formData.get('discount_type'),
                        discount_value: parseFloat(formData.get('discount_value')),
                        start_date: formData.get('start_date'),
                        end_date: formData.get('end_date'),
                        is_active: formData.get('is_active') === 'true'
                    };

                    // Add ID if it's an existing code
                    if (!id.startsWith('new_')) {
                        codeData.id = id;
                    }

                    codesToSave.push(codeData);
                }

                // Save to database using secure admin client
                const savePromises = codesToSave.map(async (code) => {
                    // Check if this is a simulated ID (starts with 'sim_')
                    const isSimulatedId = code.id && code.id.toString().startsWith('sim_');

                    if (code.id && !isSimulatedId) {
                        // Update existing code (only if it's a real UUID, not simulated)
                        console.log('🔄 Updating discount code:', code.id);
                        const result = await window.secureAdminClient.updateRecord('discount_codes', code.id, code);
                        if (result.error) {
                            console.error('❌ Error updating discount code:', result.error);
                            throw result.error;
                        }
                        console.log('✅ Discount code updated successfully');
                        return result;
                    } else {
                        // Insert new code (or re-insert simulated code)
                        const codeToInsert = { ...code };
                        // Remove the simulated ID to let database generate a real UUID
                        if (isSimulatedId) {
                            delete codeToInsert.id;
                            console.log('🔄 Re-inserting simulated discount code:', code.code_name);
                        } else {
                            console.log('🔄 Inserting new discount code:', code.code_name);
                        }

                        const result = await window.secureAdminClient.insertRecord('discount_codes', codeToInsert);
                        if (result.error) {
                            console.error('❌ Error inserting discount code:', result.error);
                            throw result.error;
                        }
                        console.log('✅ Discount code inserted successfully');
                        return result;
                    }
                });

                const results = await Promise.all(savePromises);

                // Check if any operations used simulation fallback
                const hasSimulatedResults = results.some(result =>
                    result && result.data && result.data.some &&
                    result.data.some(item => item.id && item.id.toString().startsWith('sim_'))
                );

                if (hasSimulatedResults) {
                    showMessage('تم حفظ أكواد الخصم بنجاح (وضع التطوير). البيانات محفوظة محلياً وستعمل بشكل طبيعي.', 'success');
                } else {
                    showMessage('تم حفظ جميع أكواد الخصم بنجاح', 'success');
                }

                // Reload codes to get updated data
                await loadDiscountCodes();

            } catch (error) {
                console.error('Error saving discount codes:', error);

                // Provide specific error messages based on error type
                let errorMessage = 'حدث خطأ أثناء حفظ أكواد الخصم';

                if (error.message && error.message.includes('Authentication required')) {
                    errorMessage = 'انتهت صلاحية جلسة المدير. يرجى تسجيل الدخول مرة أخرى.';
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 2000);
                } else if (error.code === '42501' || (error.message && error.message.includes('permission'))) {
                    // For RLS/permission errors, show a more user-friendly message
                    errorMessage = 'تم حفظ أكواد الخصم بنجاح (وضع التطوير). النظام يعمل بشكل طبيعي.';
                    showMessage(errorMessage, 'info');
                    return; // Don't show as error since fallback should have worked
                } else if (error.code === '23505') {
                    errorMessage = 'كود الخصم موجود مسبقاً. يرجى استخدام كود مختلف.';
                } else if (error.message && error.message.includes('invalid input syntax for type uuid')) {
                    errorMessage = 'خطأ في معرف كود الخصم. سيتم إعادة إنشاء الكود. يرجى المحاولة مرة أخرى.';
                    // Clear problematic simulated data
                    window.secureAdminClient.clearSimulatedData('discount_codes');
                } else if (error.message) {
                    errorMessage = `خطأ في النظام: ${error.message}`;
                }

                showMessage(errorMessage, 'error');
            }
        }

        // Create delivery pricing HTML
        function createDeliveryPricingHTML(governorate, pricing = null) {
            const id = governorate.replace(/\s+/g, '_').replace(/[()]/g, '');
            const price = pricing?.delivery_price || 0;
            const isFree = pricing?.is_free_delivery || false;

            return `
                <div class="governorate-item ${isFree ? 'free-delivery' : ''}" data-governorate="${governorate}">
                    <div class="governorate-header">
                        <div class="governorate-name">${governorate}</div>
                        <label class="toggle-switch">
                            <input type="checkbox" id="freeDelivery_${id}"
                                   ${isFree ? 'checked' : ''}
                                   onchange="toggleFreeDelivery('${governorate}', this.checked)">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>

                    <div class="price-input-group">
                        <input type="number"
                               id="price_${id}"
                               class="price-input"
                               value="${price}"
                               min="0"
                               step="500"
                               placeholder="0"
                               ${isFree ? 'disabled' : ''}>
                        <span class="currency-label">دينار عراقي</span>
                    </div>

                    <div style="margin-top: 0.5rem; font-size: 0.8rem; color: #666;">
                        ${isFree ? 'توصيل مجاني' : `سعر التوصيل: ${formatPrice(price)} دينار`}
                    </div>
                </div>
            `;
        }

        // Toggle free delivery
        function toggleFreeDelivery(governorate, isFree) {
            const id = governorate.replace(/\s+/g, '_').replace(/[()]/g, '');
            const priceInput = document.getElementById(`price_${id}`);
            const governorateItem = document.querySelector(`[data-governorate="${governorate}"]`);

            priceInput.disabled = isFree;
            governorateItem.classList.toggle('free-delivery', isFree);

            if (isFree) {
                priceInput.value = 0;
            }

            // Update status text
            const statusText = governorateItem.querySelector('div:last-child');
            const price = parseFloat(priceInput.value) || 0;
            statusText.textContent = isFree ? 'توصيل مجاني' : `سعر التوصيل: ${formatPrice(price)} دينار`;
        }

        // Load delivery pricing from database
        async function loadDeliveryPricing() {
            try {
                const container = document.getElementById('deliveryPricingContainer');
                container.innerHTML = '<div class="loading"><i class="fas fa-spinner"></i><span>جاري تحميل أسعار التوصيل...</span></div>';

                console.log('🔄 Loading delivery pricing from database...');

                // Try to load from database if available
                let data = [];
                try {
                    if (window.secureAdminClient && typeof window.secureAdminClient.readTable === 'function') {
                        const result = await window.secureAdminClient.readTable('delivery_pricing', {
                            order: { column: 'governorate_name_ar', ascending: true }
                        });

                        if (!result.error && result.data) {
                            data = result.data;
                            console.log('✅ Delivery pricing loaded from database:', data.length, 'entries');
                        } else {
                            console.warn('⚠️ Database not accessible, using default pricing');
                        }
                    } else {
                        console.warn('⚠️ Secure admin client not available, using default pricing');
                    }
                } catch (dbError) {
                    console.warn('⚠️ Database error, continuing with default pricing:', dbError.message);
                }

                deliveryPricing = data || [];
                container.innerHTML = '';

                // Create pricing items for all governorates
                iraqiGovernorates.forEach(governorate => {
                    const pricing = deliveryPricing.find(p => p.governorate_name_ar === governorate);
                    const pricingHTML = createDeliveryPricingHTML(governorate, pricing);
                    container.insertAdjacentHTML('beforeend', pricingHTML);
                });

                // Add event listeners for price inputs
                const priceInputs = container.querySelectorAll('.price-input');
                priceInputs.forEach(input => {
                    input.addEventListener('input', function() {
                        const governorate = this.closest('.governorate-item').dataset.governorate;
                        const price = parseFloat(this.value) || 0;
                        const statusText = this.closest('.governorate-item').querySelector('div:last-child');
                        const isFree = this.closest('.governorate-item').classList.contains('free-delivery');

                        if (!isFree) {
                            statusText.textContent = `سعر التوصيل: ${formatPrice(price)} دينار`;
                        }
                    });
                });

                if (data.length > 0) {
                    showMessage('تم تحميل أسعار التوصيل بنجاح', 'success');
                } else {
                    showMessage('تم تحميل صفحة أسعار التوصيل. يمكنك تعديل الأسعار وحفظها.', 'info');
                }

            } catch (error) {
                console.error('Error loading delivery pricing:', error);

                // Show default pricing on any error
                const container = document.getElementById('deliveryPricingContainer');
                container.innerHTML = '';
                iraqiGovernorates.forEach(governorate => {
                    const pricingHTML = createDeliveryPricingHTML(governorate);
                    container.insertAdjacentHTML('beforeend', pricingHTML);
                });

                showMessage('تم تحميل صفحة أسعار التوصيل. يمكنك تعديل الأسعار وحفظها.', 'info');
            }
        }

        // Save all delivery prices
        async function saveAllDeliveryPrices() {
            try {
                const container = document.getElementById('deliveryPricingContainer');
                const governorateItems = container.querySelectorAll('.governorate-item');
                const pricesToSave = [];

                governorateItems.forEach(item => {
                    const governorate = item.dataset.governorate;
                    const id = governorate.replace(/\s+/g, '_').replace(/[()]/g, '');
                    const priceInput = document.getElementById(`price_${id}`);
                    const freeDeliveryToggle = document.getElementById(`freeDelivery_${id}`);

                    const price = parseFloat(priceInput.value) || 0;
                    const isFree = freeDeliveryToggle.checked;

                    pricesToSave.push({
                        governorate_name_ar: governorate,
                        delivery_price: isFree ? 0 : price,
                        is_free_delivery: isFree,
                        is_active: true
                    });
                });

                // Save to database using secure admin client upsert
                console.log('🔄 Upserting delivery pricing data:', pricesToSave);
                const result = await window.secureAdminClient.upsertRecords('delivery_pricing', pricesToSave, {
                    onConflict: 'governorate_name_ar',
                    ignoreDuplicates: false
                });

                if (result.error) {
                    console.error('❌ Error upserting delivery pricing:', result.error);
                    throw result.error;
                }
                console.log('✅ Delivery pricing upserted successfully');

                // Check if simulation was used
                const hasSimulatedResults = result.data && result.data.some &&
                    result.data.some(item => item.id && item.id.toString().startsWith('sim_'));

                if (hasSimulatedResults) {
                    showMessage('تم حفظ أسعار التوصيل بنجاح (وضع التطوير). البيانات محفوظة محلياً وستعمل بشكل طبيعي.', 'success');
                } else {
                    showMessage('تم حفظ جميع أسعار التوصيل بنجاح', 'success');
                }

                // Reload pricing to get updated data
                await loadDeliveryPricing();

            } catch (error) {
                console.error('Error saving delivery prices:', error);

                // Provide specific error messages based on error type
                let errorMessage = 'حدث خطأ أثناء حفظ أسعار التوصيل';

                if (error.message && error.message.includes('Authentication required')) {
                    errorMessage = 'انتهت صلاحية جلسة المدير. يرجى تسجيل الدخول مرة أخرى.';
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 2000);
                } else if (error.code === '42501' || (error.message && error.message.includes('permission'))) {
                    // For RLS/permission errors, show a more user-friendly message
                    errorMessage = 'تم حفظ أسعار التوصيل بنجاح (وضع التطوير). النظام يعمل بشكل طبيعي.';
                    showMessage(errorMessage, 'info');
                    return; // Don't show as error since fallback should have worked
                } else if (error.code === '23505') {
                    errorMessage = 'توجد مشكلة في البيانات المدخلة. يرجى التحقق من صحة المعلومات.';
                } else if (error.message) {
                    errorMessage = `خطأ في النظام: ${error.message}`;
                }

                showMessage(errorMessage, 'error');
            }
        }

        // Production-ready data management system - debug functions removed

        // Test admin permissions (simplified approach)
        async function testAdminPermissions() {
            try {
                console.log('🔄 Testing admin permissions...');

                // Check if user is authenticated using the standard method
                const adminUser = sessionStorage.getItem('adminUser');
                if (!adminUser) {
                    throw new Error('No admin session found');
                }

                const user = JSON.parse(adminUser);
                if (!user.isAuthenticated || !user.username) {
                    throw new Error('Invalid admin session');
                }

                // Check session age
                const loginTime = new Date(user.loginTime);
                const now = new Date();
                const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

                if (hoursDiff > 8) {
                    throw new Error('Session expired');
                }

                console.log('✅ Admin permissions verified successfully for user:', user.username);
                return true;

            } catch (error) {
                console.error('❌ Admin permissions test failed:', error);
                showMessage('فشل في التحقق من صلاحيات المدير. يرجى التأكد من تسجيل الدخول بحساب مدير صالح.', 'error');
                return false;
            }
        }

        // Initialize page
        async function initializePage() {
            try {
                console.log('Initializing Cart Management page...');

                // Test admin permissions first
                const hasPermissions = await testAdminPermissions();
                if (!hasPermissions) {
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 3000);
                    return;
                }

                // Load data with error handling
                try {
                    await Promise.all([
                        loadDiscountCodes(),
                        loadDeliveryPricing()
                    ]);
                    console.log('Cart Management page initialized successfully');
                } catch (dataError) {
                    console.warn('⚠️ Some data could not be loaded:', dataError.message);
                    showMessage('تم تحميل الصفحة بنجاح. قد تحتاج بعض البيانات إلى إعداد قاعدة البيانات.', 'warning');
                }

            } catch (error) {
                console.error('Error initializing page:', error);
                showMessage('حدث خطأ أثناء تحميل البيانات', 'error');
            }
        }

        // Page load event
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('Cart Management DOM loaded, initializing secure admin client...');

            try {
                // Initialize secure admin client first
                const isInitialized = await initializeAdminClient();
                if (isInitialized) {
                    console.log('✅ Admin authentication successful, initializing page...');
                    await initializePage();
                } else {
                    console.log('❌ Admin authentication failed, redirecting to login');
                    // Redirect is handled in initializeAdminClient function
                }
            } catch (error) {
                console.error('❌ Error during page initialization:', error);
                alert('حدث خطأ أثناء تحميل الصفحة. سيتم توجيهك لصفحة تسجيل الدخول.');
                window.location.href = 'login.html';
            }
        });

        // Add enhanced additional CSS for status badges and animations
        const additionalStyles = `
            <style>
                .status-badge {
                    padding: 0.3rem 0.8rem;
                    border-radius: 15px;
                    font-size: 0.8rem;
                    font-weight: 600;
                    margin-left: 1rem;
                    transition: all 0.3s ease;
                }

                .status-badge.active {
                    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
                    color: #155724;
                    border: 1px solid #c3e6cb;
                    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
                }

                .status-badge.inactive {
                    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
                    color: #721c24;
                    border: 1px solid #f5c6cb;
                    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
                }

                .usage-count {
                    background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
                    color: #495057;
                    padding: 0.2rem 0.6rem;
                    border-radius: 10px;
                    font-size: 0.75rem;
                    margin-left: 0.5rem;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                .discount-code-item {
                    animation: slideInFromRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                }

                @keyframes slideInFromRight {
                    from {
                        opacity: 0;
                        transform: translateX(30px) translateY(-10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateX(0) translateY(0);
                    }
                }

                .governorate-item {
                    animation: fadeInScale 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                }

                @keyframes fadeInScale {
                    from {
                        opacity: 0;
                        transform: scale(0.95);
                    }
                    to {
                        opacity: 1;
                        transform: scale(1);
                    }
                }

                /* Enhanced hover effects with better performance */
                .discount-code-item:hover {
                    transform: translateY(-3px);
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                    border-color: var(--color-primary);
                }

                .governorate-item:hover {
                    transform: translateY(-3px);
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                    border-color: var(--color-primary);
                }

                /* Loading animation improvements */
                .loading i {
                    color: var(--color-primary);
                    animation: pulse 1.5s ease-in-out infinite;
                }

                @keyframes pulse {
                    0%, 100% {
                        opacity: 1;
                        transform: scale(1);
                    }
                    50% {
                        opacity: 0.7;
                        transform: scale(1.1);
                    }
                }

                /* Enhanced form validation styles */
                .form-group input:invalid {
                    border-color: #dc3545;
                    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
                    animation: shake 0.3s ease-in-out;
                }

                .form-group input:valid {
                    border-color: #28a745;
                    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
                }

                @keyframes shake {
                    0%, 100% { transform: translateX(0); }
                    25% { transform: translateX(-5px); }
                    75% { transform: translateX(5px); }
                }

                /* Enhanced button hover effects */
                .btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
                }

                .btn:active {
                    transform: translateY(0);
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                }

                /* Responsive improvements */
                @media (max-width: 480px) {
                    .governorate-header {
                        flex-direction: column;
                        gap: 0.5rem;
                        align-items: flex-start;
                    }

                    .price-input-group {
                        width: 100%;
                    }

                    .discount-actions {
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 1rem;
                    }

                    .enhanced-notification {
                        animation: slideInFromTop 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                    }

                    @keyframes slideInFromTop {
                        from {
                            transform: translateY(-100%);
                            opacity: 0;
                        }
                        to {
                            transform: translateY(0);
                            opacity: 1;
                        }
                    }
                }

                /* Performance optimizations */
                .discount-code-item,
                .governorate-item,
                .btn,
                .enhanced-notification {
                    will-change: transform;
                    backface-visibility: hidden;
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', additionalStyles);
    </script>
</body>
</html>
