<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1024">
    <title>إدارة الطلبات - Care Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/logout-modal.css">
    <link rel="stylesheet" href="css/enhanced-sidebar.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Removed conflicting sidebar scripts - using unified enhanced-sidebar.js -->
    
    <style>
        /* Enhanced Orders Management specific styles */
        .orders-content {
            padding: var(--spacing-xxl);
            background: linear-gradient(135deg, #fafbfc 0%, #ffffff 50%, #f8f9fa 100%);
            min-height: 100vh;
            position: relative;
        }

        .orders-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, var(--color-primary-alpha-10) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(74, 144, 164, 0.02) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
        }

        .orders-content > * {
            position: relative;
            z-index: 1;
        }

        /* Enhanced Welcome Section */
        .welcome-section {
            margin-bottom: var(--spacing-xxl);
        }

        .welcome-section .card {
            background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
            border: 1px solid rgba(130, 135, 122, 0.12);
            box-shadow: 0 4px 20px rgba(130, 135, 122, 0.08);
            border-radius: var(--radius-xxl);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .welcome-section .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            opacity: 0.8;
        }

        .welcome-section .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 16px 50px rgba(130, 135, 122, 0.12);
        }

        .welcome-section .card-body {
            padding: var(--spacing-xxl);
        }

        .welcome-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
            box-shadow: 0 8px 25px rgba(130, 135, 122, 0.3);
            position: relative;
        }

        .welcome-icon::after {
            content: '';
            position: absolute;
            inset: -2px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
            border-radius: var(--radius-xl);
            z-index: -1;
            opacity: 0.3;
            filter: blur(8px);
        }

        /* Orders Statistics */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .stat-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: 0 4px 20px rgba(130, 135, 122, 0.08);
            border: 1px solid rgba(130, 135, 122, 0.12);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(130, 135, 122, 0.15);
        }

        .stat-card:hover::before {
            transform: scaleX(1);
        }

        .stat-icon {
            width: 70px;
            height: 70px;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
            flex-shrink: 0;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            position: relative;
        }

        .stat-icon::after {
            content: '';
            position: absolute;
            inset: -3px;
            border-radius: var(--radius-xl);
            z-index: -1;
            opacity: 0.2;
            filter: blur(10px);
        }

        .stat-icon.total { background: linear-gradient(135deg, var(--color-info) 0%, var(--color-info-dark) 100%); }
        .stat-icon.pending { background: linear-gradient(135deg, var(--color-warning) 0%, var(--color-warning-dark) 100%); }
        .stat-icon.completed { background: linear-gradient(135deg, var(--color-success) 0%, var(--color-success-dark) 100%); }
        .stat-icon.cancelled { background: linear-gradient(135deg, var(--color-danger) 0%, var(--color-danger-dark) 100%); }
        .stat-icon.confirmed { background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%); }
        .stat-icon.shipped { background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark) 100%); }

        .stat-content {
            flex: 1;
        }

        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--color-text-primary);
            margin-bottom: 0.2rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--color-text-secondary);
            font-weight: 600;
        }

        /* Enhanced Orders Controls */
        .orders-controls {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid rgba(130, 135, 122, 0.12);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
            box-shadow: 0 4px 20px rgba(130, 135, 122, 0.08);
            position: relative;
            overflow: hidden;
        }

        .orders-controls::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
        }

        .controls-row {
            display: flex;
            gap: var(--spacing-md);
            align-items: center;
            flex-wrap: wrap;
        }

        .search-container {
            position: relative;
            flex: 1;
            min-width: 300px;
        }

        .search-container input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-xl) var(--spacing-md) var(--spacing-md);
            border: 2px solid rgba(130, 135, 122, 0.2);
            border-radius: var(--radius-md);
            font-size: 1rem;
            transition: var(--transition-base);
            font-family: 'Cairo', sans-serif;
        }

        .search-container input:focus {
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(130, 135, 122, 0.1);
            outline: none;
        }

        .search-icon {
            position: absolute;
            right: var(--spacing-md);
            top: 50%;
            transform: translateY(-50%);
            color: var(--color-text-muted);
            pointer-events: none;
        }

        .filter-container select {
            padding: var(--spacing-md);
            border: 2px solid rgba(130, 135, 122, 0.2);
            border-radius: var(--radius-md);
            background: var(--color-bg-primary);
            font-family: 'Cairo', sans-serif;
            font-size: 1rem;
            transition: var(--transition-base);
            min-width: 180px;
        }

        .filter-container select:focus {
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(130, 135, 122, 0.1);
            outline: none;
        }

        .filter-actions .btn {
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--radius-md);
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            transition: var(--transition-base);
        }

        /* Enhanced Orders Table */
        .table-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid rgba(130, 135, 122, 0.12);
            border-radius: var(--radius-xl);
            box-shadow: 0 8px 32px rgba(130, 135, 122, 0.1);
            overflow: hidden;
            position: relative;
        }

        .table-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
        }

        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: thin;
            scrollbar-color: rgba(130, 135, 122, 0.3) transparent;
        }

        .table-responsive::-webkit-scrollbar {
            height: 8px;
        }

        .table-responsive::-webkit-scrollbar-track {
            background: rgba(130, 135, 122, 0.1);
            border-radius: 4px;
        }

        .table-responsive::-webkit-scrollbar-thumb {
            background: rgba(130, 135, 122, 0.3);
            border-radius: 4px;
        }

        .table-responsive::-webkit-scrollbar-thumb:hover {
            background: rgba(130, 135, 122, 0.5);
        }

        .orders-table {
            width: 100%;
            border-collapse: collapse;
        }

        .orders-table th {
            background: linear-gradient(135deg, var(--color-secondary) 0%, #1a1c1c 50%, #2c3e50 100%);
            color: white;
            font-weight: 700;
            padding: var(--spacing-lg) var(--spacing-md);
            text-align: right;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
            font-size: 0.95rem;
        }

        .orders-table td {
            padding: var(--spacing-md);
            border-bottom: 1px solid #f0f0f0;
            vertical-align: middle;
        }

        .orders-table tr:hover {
            background: rgba(130, 135, 122, 0.05);
        }

        .order-id {
            font-weight: 700;
            color: #000000;
            font-size: 1rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .customer-info {
            display: flex;
            flex-direction: column;
            gap: 0.3rem;
        }

        .customer-name {
            font-weight: 600;
            color: #000000;
        }

        .customer-phone {
            font-size: 0.85rem;
            color: #444444;
            font-weight: 500;
            direction: ltr;
            text-align: right;
        }

        .products-summary {
            font-size: 0.9rem;
            color: #333333;
            font-weight: 500;
        }

        .products-count {
            background: rgba(130, 135, 122, 0.1);
            padding: 0.3rem 0.6rem;
            border-radius: var(--radius-sm);
            font-weight: 600;
            display: inline-block;
        }

        .order-date {
            font-size: 0.9rem;
            color: var(--color-text-secondary);
        }

        .order-total {
            font-weight: 700;
            color: #000000;
            font-size: 1.1rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* Status Select */
        .status-select {
            padding: 0.5rem 0.8rem;
            border: none;
            border-radius: 20px;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
        }

        .status-select.pending { background: #fff3cd; color: #856404; }
        .status-select.confirmed { background: #cce5ff; color: #004085; }
        .status-select.shipped { background: #d4edda; color: #155724; }
        .status-select.delivered { background: #d1ecf1; color: #0c5460; }
        .status-select.cancelled { background: #f8d7da; color: #721c24; }

        /* Status Badge for Modal */
        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-weight: 600;
            font-size: 0.85rem;
            display: inline-block;
        }

        .status-badge.pending { background: #fff3cd; color: #856404; }
        .status-badge.confirmed { background: #cce5ff; color: #004085; }
        .status-badge.shipped { background: #d4edda; color: #155724; }
        .status-badge.delivered { background: #d1ecf1; color: #0c5460; }
        .status-badge.cancelled { background: #f8d7da; color: #721c24; }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
        }

        .btn {
            padding: 0.5rem;
            min-width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .btn-view { background: var(--color-info); color: white; }
        .btn-view:hover { background: #138496; transform: translateY(-2px); }

        .btn-whatsapp { background: #25d366; color: white; }
        .btn-whatsapp:hover { background: #1da851; transform: translateY(-2px); }

        .btn-delete { background: var(--color-danger); color: white; }
        .btn-delete:hover { background: #c82333; transform: translateY(-2px); }

        /* Loading and Empty States */
        .loading-spinner {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 4rem 2rem;
            gap: 1rem;
            color: var(--color-primary);
        }

        .loading-spinner i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--color-text-secondary);
        }

        .empty-state i {
            font-size: 4rem;
            color: var(--color-text-muted);
            margin-bottom: 1rem;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.6);
            backdrop-filter: blur(8px);
            animation: modalFadeIn 0.3s ease-out;
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
                backdrop-filter: blur(0px);
            }
            to {
                opacity: 1;
                backdrop-filter: blur(8px);
            }
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
            border: 1px solid rgba(130, 135, 122, 0.12);
            border-radius: var(--radius-xxl);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            width: 90%;
            max-width: 900px;
            max-height: 90vh;
            overflow: hidden;
            direction: rtl;
            animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes modalSlideIn {
            from {
                transform: translateY(-50px) scale(0.9);
                opacity: 0;
            }
            to {
                transform: translateY(0) scale(1);
                opacity: 1;
            }
        }

        .modal-header {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            color: white;
            padding: var(--spacing-xl);
            border-radius: var(--radius-xxl) var(--radius-xxl) 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
            pointer-events: none;
        }

        .modal-header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            position: relative;
            z-index: 1;
        }

        .modal-header h3 {
            font-size: 1.4rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .modal-actions {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .modal-close {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 1.3rem;
            cursor: pointer;
            padding: 0.8rem;
            border-radius: 50%;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 2;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .modal-close:active {
            transform: scale(0.95);
        }

        .modal-close:focus {
            outline: 3px solid rgba(255, 255, 255, 0.5);
            outline-offset: 2px;
        }

        /* Print Button Styling */
        .modal-print-btn {
            background: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            font-size: 0.9rem;
            cursor: pointer;
            padding: 0.6rem 1rem;
            border-radius: var(--radius-lg);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
        }

        .modal-print-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .modal-print-btn:active {
            transform: translateY(0);
        }

        .modal-print-btn:focus {
            outline: 3px solid rgba(255, 255, 255, 0.5);
            outline-offset: 2px;
        }

        .modal-print-btn i {
            font-size: 1rem;
        }

        /* Accessibility improvements */
        .modal[aria-hidden="true"] {
            display: none;
        }

        .modal[aria-hidden="false"] {
            display: flex;
        }

        /* Print Styles */
        @media print {
            /* Hide all non-essential elements */
            .sidebar,
            .top-bar,
            .mobile-menu-btn,
            .sidebar-overlay,
            .enhanced-success-notification,
            .modal-header,
            .modal-close,
            .modal-print-btn,
            .btn,
            button,
            .action-buttons,
            .orders-controls,
            .stats-grid,
            .welcome-section,
            .orders-management-section .card-header {
                display: none !important;
            }

            /* Reset page styles for print */
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            html, body {
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                color: #000 !important;
                font-family: 'Cairo', sans-serif !important;
                font-size: 12pt !important;
                line-height: 1.4 !important;
            }

            /* Page setup for A4 */
            @page {
                size: A4;
                margin: 2cm 1.5cm;
            }

            /* Modal content for print */
            .modal {
                position: static !important;
                display: block !important;
                background: white !important;
                backdrop-filter: none !important;
            }

            .modal-content {
                position: static !important;
                width: 100% !important;
                max-width: none !important;
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                border: none !important;
                border-radius: 0 !important;
                box-shadow: none !important;
                direction: rtl !important;
            }

            .modal-body {
                padding: 0 !important;
                background: white !important;
                max-height: none !important;
                overflow: visible !important;
            }

            /* Print header */
            .modal-body::before {
                content: "Care - تفاصيل الطلب";
                display: block;
                text-align: center;
                font-size: 18pt;
                font-weight: 700;
                color: #82877a;
                margin-bottom: 20pt;
                padding-bottom: 10pt;
                border-bottom: 2pt solid #82877a;
            }

            /* Detail sections */
            .detail-section {
                background: white !important;
                border: 1pt solid #ddd !important;
                border-radius: 4pt !important;
                padding: 12pt !important;
                margin-bottom: 15pt !important;
                box-shadow: none !important;
                page-break-inside: avoid;
            }

            .detail-section::before {
                display: none !important;
            }

            .detail-section h4 {
                color: #82877a !important;
                font-size: 14pt !important;
                font-weight: 700 !important;
                margin-bottom: 10pt !important;
                border-bottom: 1pt solid #82877a !important;
                padding-bottom: 5pt !important;
                text-shadow: none !important;
            }

            .detail-item {
                display: flex !important;
                justify-content: space-between !important;
                align-items: flex-start !important;
                padding: 4pt 0 !important;
                border-bottom: 0.5pt solid #eee !important;
                background: transparent !important;
                margin: 0 !important;
                border-radius: 0 !important;
            }

            .detail-item:last-child {
                border-bottom: none !important;
            }

            .detail-label {
                font-weight: 600 !important;
                color: #333 !important;
                font-size: 11pt !important;
                min-width: auto !important;
            }

            .detail-value {
                font-weight: 500 !important;
                color: #000 !important;
                font-size: 11pt !important;
                text-align: right !important;
                direction: rtl !important;
            }

            /* Products list */
            .products-list {
                background: white !important;
                border: 1pt solid #ddd !important;
                border-radius: 0 !important;
                box-shadow: none !important;
            }

            .product-item {
                padding: 8pt !important;
                border-bottom: 0.5pt solid #eee !important;
                background: white !important;
                display: flex !important;
                justify-content: space-between !important;
                align-items: flex-start !important;
            }

            .product-item:hover {
                background: white !important;
            }

            .product-name {
                font-weight: 600 !important;
                color: #000 !important;
                font-size: 11pt !important;
                text-shadow: none !important;
            }

            .product-number {
                background: #82877a !important;
                color: white !important;
                width: 16pt !important;
                height: 16pt !important;
                font-size: 8pt !important;
                box-shadow: none !important;
            }

            .product-details {
                color: #333 !important;
                font-size: 10pt !important;
                font-weight: 500 !important;
            }

            .product-details span {
                background: #f5f5f5 !important;
                color: #000 !important;
                padding: 2pt 6pt !important;
                border-radius: 2pt !important;
                font-weight: 500 !important;
            }

            /* Order summary */
            .order-summary {
                background: #f8f9fa !important;
                border: 1pt solid #ddd !important;
                border-radius: 4pt !important;
                padding: 10pt !important;
                margin-top: 10pt !important;
                text-align: center !important;
                box-shadow: none !important;
            }

            .order-summary strong {
                color: #000 !important;
                font-size: 12pt !important;
                text-shadow: none !important;
            }

            /* Status badges */
            .status-badge {
                padding: 3pt 6pt !important;
                border-radius: 2pt !important;
                font-weight: 600 !important;
                font-size: 9pt !important;
            }

            .status-badge.pending {
                background: #fff3cd !important;
                color: #856404 !important;
                border: 0.5pt solid #ffeaa7 !important;
            }

            .status-badge.confirmed {
                background: #d1ecf1 !important;
                color: #0c5460 !important;
                border: 0.5pt solid #bee5eb !important;
            }

            .status-badge.shipped {
                background: #e2e3f1 !important;
                color: #383d41 !important;
                border: 0.5pt solid #d6d8e5 !important;
            }

            .status-badge.delivered {
                background: #d4edda !important;
                color: #155724 !important;
                border: 0.5pt solid #c3e6cb !important;
            }

            .status-badge.cancelled {
                background: #f8d7da !important;
                color: #721c24 !important;
                border: 0.5pt solid #f5c6cb !important;
            }

            /* Print footer */
            .modal-body::after {
                content: "تم طباعة هذا المستند في: " attr(data-print-date) " - Care نظام إدارة الطلبات";
                display: block;
                text-align: center;
                font-size: 9pt;
                color: #666;
                margin-top: 20pt;
                padding-top: 10pt;
                border-top: 0.5pt solid #ddd;
            }

            /* Page breaks */
            .order-details-grid {
                page-break-inside: avoid;
            }

            .detail-section {
                page-break-inside: avoid;
            }

            .products-list {
                page-break-inside: avoid;
            }
        }

        .modal-body {
            padding: var(--spacing-xxl);
            overflow-y: auto;
            max-height: calc(90vh - 120px);
            background: #ffffff;
        }

        .order-details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
        }

        .detail-section {
            background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
            border: 1px solid rgba(130, 135, 122, 0.12);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            border-right: 4px solid var(--color-primary);
            box-shadow: 0 4px 20px rgba(130, 135, 122, 0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .detail-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            opacity: 0.8;
        }

        .detail-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(130, 135, 122, 0.12);
        }

        .detail-section h4 {
            color: #000000;
            margin-bottom: var(--spacing-lg);
            font-size: 1.2rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            padding-bottom: var(--spacing-md);
            border-bottom: 2px solid rgba(130, 135, 122, 0.1);
        }

        .detail-section h4 i {
            color: var(--color-primary);
            font-size: 1.1rem;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md) 0;
            border-bottom: 1px solid rgba(130, 135, 122, 0.08);
            transition: all 0.2s ease;
        }

        .detail-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .detail-item:hover {
            background: rgba(130, 135, 122, 0.03);
            margin: 0 calc(-1 * var(--spacing-md));
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
        }

        .detail-label {
            font-weight: 600;
            color: #333333;
            font-size: 0.95rem;
            min-width: 120px;
        }

        .detail-value {
            font-weight: 600;
            color: #000000;
            font-size: 0.95rem;
            text-align: left;
            direction: ltr;
        }

        .detail-value.rtl {
            direction: rtl;
            text-align: right;
        }

        .products-list {
            background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
            border-radius: var(--radius-xl);
            border: 1px solid rgba(130, 135, 122, 0.12);
            box-shadow: 0 2px 10px rgba(130, 135, 122, 0.05);
            overflow: hidden;
        }

        .product-item {
            padding: var(--spacing-lg);
            border-bottom: 1px solid rgba(130, 135, 122, 0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.2s ease;
            position: relative;
        }

        .product-item:hover {
            background: rgba(130, 135, 122, 0.03);
        }

        .product-item:last-child {
            border-bottom: none;
        }

        .product-name {
            font-weight: 700;
            color: #000000;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            font-size: 1rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .product-number {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 700;
            box-shadow: 0 2px 8px rgba(130, 135, 122, 0.3);
        }

        .product-details {
            font-size: 0.9rem;
            color: #333333;
            display: flex;
            gap: var(--spacing-lg);
            flex-wrap: wrap;
            font-weight: 500;
        }

        .product-details span {
            background: rgba(130, 135, 122, 0.1);
            padding: 0.3rem 0.8rem;
            border-radius: var(--radius-md);
            font-weight: 600;
            color: #000000;
        }

        /* Status Badge Styling in Modal */
        .modal .status-badge {
            padding: 0.4rem 0.8rem;
            border-radius: var(--radius-md);
            font-weight: 600;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .modal .status-badge.pending {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .modal .status-badge.confirmed {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .modal .status-badge.shipped {
            background: linear-gradient(135deg, #e2e3f1 0%, #d6d8e5 100%);
            color: #383d41;
            border: 1px solid #d6d8e5;
        }

        .modal .status-badge.delivered {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .modal .status-badge.cancelled {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .quantity-info, .price-info {
            background: var(--color-primary-alpha-10);
            padding: 0.2rem 0.5rem;
            border-radius: var(--radius-sm);
            font-size: 0.8rem;
        }

        .product-total {
            font-weight: 700;
            color: var(--color-primary);
            font-size: 1.1rem;
        }

        /* Delete Modal Styles */
        .delete-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .delete-modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .delete-modal-content {
            background: var(--color-bg-primary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            width: 85%;
            max-width: 420px;
            direction: rtl;
        }

        .delete-modal-header {
            background: linear-gradient(135deg, var(--color-danger) 0%, #c82333 100%);
            color: white;
            padding: var(--spacing-md);
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
            text-align: center;
        }

        .delete-icon {
            font-size: 2.2rem;
            margin-bottom: var(--spacing-sm);
        }

        .delete-modal-body {
            padding: var(--spacing-lg);
            text-align: center;
        }

        .warning-text {
            color: var(--color-danger);
            font-weight: 600;
            margin-top: var(--spacing-sm);
        }

        .order-info {
            background: rgba(130, 135, 122, 0.05);
            border-radius: var(--radius-md);
            padding: var(--spacing-sm);
            margin-top: var(--spacing-sm);
            text-align: right;
            font-size: 0.9rem;
        }

        .delete-modal-footer {
            padding: var(--spacing-md);
            display: flex;
            gap: var(--spacing-sm);
            justify-content: center;
        }

        .cancel-btn {
            background: var(--color-secondary);
            color: white;
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            font-size: 0.9rem;
            transition: var(--transition-base);
        }

        .cancel-btn:hover {
            background: #5a6268;
        }

        .delete-btn {
            background: var(--color-danger);
            color: white;
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            font-size: 0.9rem;
            transition: var(--transition-base);
        }

        .delete-btn:hover {
            background: #c82333;
        }

        /* Enhanced Success Notification Styles */
        .enhanced-success-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
        }

        .enhanced-success-notification .notification-content {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1.2rem 1.5rem;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 12px;
            position: relative;
        }

        .enhanced-success-notification .notification-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.4rem;
            flex-shrink: 0;
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
            animation: successIconBounce 0.6s ease-out;
        }

        .enhanced-success-notification .notification-text {
            flex: 1;
            color: #121414;
        }

        .enhanced-success-notification .notification-title {
            font-size: 1.1rem;
            font-weight: 700;
            margin-bottom: 0.3rem;
            color: #28a745;
        }

        .enhanced-success-notification .notification-message {
            font-size: 0.95rem;
            color: #555;
            line-height: 1.4;
        }

        .enhanced-success-notification .notification-close {
            position: absolute;
            top: 0.8rem;
            left: 0.8rem;
            background: none;
            border: none;
            color: #999;
            font-size: 1.1rem;
            cursor: pointer;
            padding: 0.3rem;
            border-radius: 50%;
            transition: all 0.3s ease;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .enhanced-success-notification .notification-close:hover {
            background: rgba(0,0,0,0.1);
            color: #666;
        }

        .enhanced-success-notification .notification-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 0 0 12px 12px;
            animation: progressCountdown 5s linear;
        }

        @keyframes enhancedSlideIn {
            0% {
                transform: translateX(100%) scale(0.8);
                opacity: 0;
            }
            50% {
                transform: translateX(-10px) scale(1.02);
                opacity: 0.8;
            }
            100% {
                transform: translateX(0) scale(1);
                opacity: 1;
            }
        }

        @keyframes enhancedSlideOut {
            0% {
                transform: translateX(0) scale(1);
                opacity: 1;
            }
            100% {
                transform: translateX(100%) scale(0.8);
                opacity: 0;
            }
        }

        @keyframes successIconBounce {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes progressCountdown {
            from {
                width: 100%;
            }
            to {
                width: 0%;
            }
        }

        /* ===== MOBILE SIDEBAR - USING UNIFIED SYSTEM ===== */
        /* All mobile sidebar styling is now handled by enhanced-sidebar.css */
        /* Removed page-specific overrides to ensure consistency */

        /* Responsive Design */
        @media (max-width: 768px) {
            .enhanced-success-notification {
                top: 80px;
                right: 10px;
                left: 10px;
                max-width: none;
                width: calc(100% - 20px);
            }

            .enhanced-success-notification .notification-content {
                padding: 1rem;
                border-radius: 10px;
            }

            .enhanced-success-notification .notification-icon {
                width: 45px;
                height: 45px;
                font-size: 1.2rem;
            }

            .enhanced-success-notification .notification-title {
                font-size: 1rem;
            }

            .enhanced-success-notification .notification-message {
                font-size: 0.9rem;
            }

            /* Mobile sidebar styling removed - using unified system */

            .controls-row {
                flex-direction: column;
                align-items: stretch;
            }

            .search-container {
                min-width: auto;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            }

            .orders-table {
                font-size: 0.85rem;
                min-width: 800px;
            }

            .orders-table th,
            .orders-table td {
                padding: 0.5rem;
                white-space: nowrap;
            }

            /* Hide some columns on mobile for better readability */
            .orders-table th:nth-child(3),
            .orders-table td:nth-child(3) {
                display: none;
            }

            .orders-table th:nth-child(6),
            .orders-table td:nth-child(6) {
                display: none;
            }

            .table-responsive {
                border-radius: var(--radius-lg);
                margin: 0 -1rem;
                padding: 0 1rem;
            }

            .customer-info {
                font-size: 0.8rem;
            }

            .customer-phone {
                font-size: 0.75rem;
            }

            .action-buttons {
                flex-direction: column;
                gap: 0.3rem;
            }

            .btn {
                min-width: 30px;
                height: 30px;
                font-size: 0.8rem;
            }

            .order-details-grid {
                grid-template-columns: 1fr;
            }

            .modal-content {
                width: 95%;
                margin: 0.5rem;
                max-height: 95vh;
                border-radius: var(--radius-lg);
            }

            .modal-header {
                padding: var(--spacing-lg);
                border-radius: var(--radius-lg) var(--radius-lg) 0 0;
            }

            .modal-header h3 {
                font-size: 1.2rem;
            }

            .modal-close {
                width: 40px;
                height: 40px;
                font-size: 1.1rem;
                padding: 0.6rem;
            }

            .modal-print-btn {
                padding: 0.5rem 0.8rem;
                font-size: 0.85rem;
                gap: 0.4rem;
            }

            .modal-print-btn span {
                display: none;
            }

            .modal-print-btn i {
                font-size: 1.1rem;
            }

            .modal-actions {
                gap: var(--spacing-sm);
            }

            .modal-body {
                padding: var(--spacing-lg);
                max-height: calc(95vh - 100px);
            }

            .detail-section {
                padding: var(--spacing-lg);
                margin-bottom: var(--spacing-md);
            }

            .detail-section h4 {
                font-size: 1.1rem;
                margin-bottom: var(--spacing-md);
            }

            .detail-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
                padding: var(--spacing-sm) 0;
            }

            .detail-item:hover {
                margin: 0;
                padding: var(--spacing-sm) 0;
                background: transparent;
            }

            .detail-label {
                font-size: 0.9rem;
                min-width: auto;
            }

            .detail-value {
                font-size: 0.9rem;
            }

            .product-item {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-md);
                padding: var(--spacing-md);
            }

            .product-name {
                font-size: 0.95rem;
            }

            .product-details {
                gap: var(--spacing-md);
            }

            .product-details span {
                padding: 0.2rem 0.6rem;
                font-size: 0.8rem;
            }

            .product-details {
                flex-direction: column;
                gap: 0.3rem;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: var(--spacing-md);
            }

            .stat-card {
                padding: 1rem;
            }

            .orders-table th:nth-child(6),
            .orders-table td:nth-child(6) {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- REMOVED: Duplicate hamburger button - now in top-bar-actions -->

    <!-- Mobile Sidebar Backdrop -->
    <div class="sidebar-backdrop" id="sidebarBackdrop" onclick="closeMobileSidebar()"></div>

    <!-- STANDARDIZED Sidebar -->
    <div class="sidebar" id="sidebar">
        <!-- Brand Section (Top) -->
        <div class="sidebar-section brand-section">
            <div class="brand-logo">
                <i class="fas fa-store"></i>
                <h2>Care Admin</h2>
            </div>
            <p class="brand-subtitle">لوحة التحكم الإدارية</p>
        </div>

        <!-- Unified Navigation Section -->
        <div class="sidebar-section unified-navigation">
            <!-- Seamless Navigation List -->
            <nav class="sidebar-nav">
                <!-- Dashboard Navigation Links -->
                <a href="dashboard.html" class="sidebar-link">
                    <i class="fas fa-tachometer-alt"></i>
                    الرئيسية
                </a>
                <a href="orders.html" class="sidebar-link active">
                    <i class="fas fa-shopping-bag"></i>
                    إدارة الطلبات
                </a>
                <a href="products.html" class="sidebar-link">
                    <i class="fas fa-box"></i>
                    إدارة المنتجات
                </a>
                <a href="cart-management.html" class="sidebar-link">
                    <i class="fas fa-shopping-cart"></i>
                    إدارة سلة التسوق
                </a>
                <a href="content.html" class="sidebar-link">
                    <i class="fas fa-edit"></i>
                    إدارة المحتوى
                </a>
                <a href="site-settings.html" class="sidebar-link">
                    <i class="fas fa-cog"></i>
                    إعدادات الموقع
                </a>

                <!-- Admin Navigation Links (seamlessly integrated) -->
                <a href="../index.html" class="sidebar-link" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    عرض الموقع
                </a>
                <a href="#" class="sidebar-link logout-link" onclick="showLogoutModal()" title="تسجيل الخروج">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </nav>

            <!-- User Info Component (at bottom of navigation) -->
            <div class="sidebar-user-info">
                <div class="user-avatar" id="sidebarUserAvatar">A</div>
                <div class="user-details">
                    <div class="user-name" id="sidebarUserName">مدير النظام</div>
                    <div class="user-role">مدير النظام</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content" id="mainContent">
        <!-- STANDARDIZED Top Bar -->
        <div class="top-bar">
            <div class="top-bar-content">
                <div class="page-title-section">
                    <h1 class="page-title">
                        <i class="fas fa-shopping-cart"></i>
                        إدارة الطلبات
                    </h1>
                </div>
                <div class="top-bar-actions">
                    <!-- Mobile Hamburger Menu Button -->
                    <button class="hamburger-btn" onclick="toggleMobileSidebar()" title="القائمة" id="hamburgerBtn">
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                    </button>
                </div>
            </div>
        </div>

        <div class="orders-content">
            <!-- Welcome Section -->
            <div class="welcome-section mb-lg">
                <div class="card card-elevated">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-lg">
                            <div class="welcome-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div>
                                <h2 class="text-xl font-bold mb-sm">إدارة الطلبات</h2>
                                <p class="text-secondary">متابعة وإدارة جميع طلبات العملاء</p>
                            </div>
                        </div>
                        <div class="welcome-actions flex gap-sm">
                            <button class="btn btn-outline-primary btn-sm" onclick="refreshOrders()">
                                <i class="fas fa-sync-alt"></i>
                                تحديث
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Messages Container -->
            <div class="messages-container">
                <div class="message success" id="successMessage" style="display: none;"></div>
                <div class="message error" id="errorMessage" style="display: none;"></div>
            </div>

            <!-- Enhanced Orders Statistics -->
            <div class="stats-grid">
                <div class="stat-card" data-stat="total">
                    <div class="stat-header">
                        <div class="stat-icon total">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-change neutral">
                            <i class="fas fa-chart-line"></i>
                            <span>الكل</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="totalOrders">
                            <span class="loading-placeholder">0</span>
                        </div>
                        <div class="stat-label">إجمالي الطلبات</div>
                        <div class="stat-description">جميع الطلبات المسجلة</div>
                    </div>
                </div>

                <div class="stat-card" data-stat="pending">
                    <div class="stat-header">
                        <div class="stat-icon pending">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-change neutral">
                            <i class="fas fa-hourglass-half"></i>
                            <span>انتظار</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="pendingOrders">
                            <span class="loading-placeholder">0</span>
                        </div>
                        <div class="stat-label">طلبات معلقة</div>
                        <div class="stat-description">في انتظار المراجعة</div>
                    </div>
                </div>

                <div class="stat-card" data-stat="confirmed">
                    <div class="stat-header">
                        <div class="stat-icon confirmed">
                            <i class="fas fa-check-double"></i>
                        </div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>مؤكد</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="confirmedOrders">
                            <span class="loading-placeholder">0</span>
                        </div>
                        <div class="stat-label">الطلبات المؤكدة</div>
                        <div class="stat-description">تم تأكيدها من العميل</div>
                    </div>
                </div>

                <div class="stat-card" data-stat="shipped">
                    <div class="stat-header">
                        <div class="stat-icon shipped">
                            <i class="fas fa-shipping-fast"></i>
                        </div>
                        <div class="stat-change positive">
                            <i class="fas fa-truck"></i>
                            <span>شحن</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="shippedOrders">
                            <span class="loading-placeholder">0</span>
                        </div>
                        <div class="stat-label">الطلبات المشحونة</div>
                        <div class="stat-description">تم شحنها للعميل</div>
                    </div>
                </div>

                <div class="stat-card" data-stat="completed">
                    <div class="stat-header">
                        <div class="stat-icon completed">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-change positive">
                            <i class="fas fa-thumbs-up"></i>
                            <span>مكتمل</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="completedOrders">
                            <span class="loading-placeholder">0</span>
                        </div>
                        <div class="stat-label">طلبات مكتملة</div>
                        <div class="stat-description">تم تسليمها بنجاح</div>
                    </div>
                </div>

                <div class="stat-card" data-stat="cancelled">
                    <div class="stat-header">
                        <div class="stat-icon cancelled">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-change negative">
                            <i class="fas fa-arrow-down"></i>
                            <span>ملغي</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="cancelledOrders">
                            <span class="loading-placeholder">0</span>
                        </div>
                        <div class="stat-label">الطلبات الملغية</div>
                        <div class="stat-description">تم إلغاؤها من العميل</div>
                    </div>
                </div>
            </div>

            <!-- Orders Management Section -->
            <div class="orders-management-section card">
                <div class="card-header">
                    <div class="flex items-center gap-md">
                        <div class="section-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div>
                            <h3>قائمة الطلبات</h3>
                            <p class="text-sm text-secondary">إدارة ومتابعة جميع الطلبات</p>
                        </div>
                    </div>
                </div>

                <!-- Search and Filter Controls -->
                <div class="orders-controls">
                    <div class="controls-row">
                        <div class="search-container">
                            <input type="text" id="searchInput" class="form-control" placeholder="البحث في الطلبات (اسم العميل، رقم الهاتف، رقم الطلب)...">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                        <div class="filter-container">
                            <select id="statusFilter" class="form-control">
                                <option value="">جميع الحالات</option>
                                <option value="pending">معلق</option>
                                <option value="confirmed">مؤكد</option>
                                <option value="shipped">تم الشحن</option>
                                <option value="delivered">تم التسليم</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>
                        <div class="filter-actions">
                            <button class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                                <i class="fas fa-times"></i>
                                مسح الفلاتر
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Orders Table -->
                <div class="table-container">
                    <div class="loading-spinner" id="loadingSpinner" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>جاري تحميل الطلبات...</span>
                    </div>
                    <div id="ordersTableContainer">
                        <!-- Orders table will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Order Details Modal -->
    <div class="modal" id="orderModal" style="display: none;" role="dialog" aria-labelledby="orderModalTitle" aria-hidden="true">
        <div class="modal-content" role="document">
            <div class="modal-header">
                <div class="modal-header-content">
                    <h3 id="orderModalTitle">تفاصيل الطلب</h3>
                    <div class="modal-actions">
                        <button class="modal-print-btn" onclick="printOrderDetails()" aria-label="طباعة تفاصيل الطلب" type="button">
                            <i class="fas fa-print" aria-hidden="true"></i>
                            <span>طباعة</span>
                        </button>
                        <button class="modal-close" onclick="closeOrderModal()" aria-label="إغلاق النافذة المنبثقة" type="button">
                            <i class="fas fa-times" aria-hidden="true"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-body" id="orderModalBody">
                <!-- Order details will be populated here -->
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="delete-modal" id="deleteModal" style="display: none;">
        <div class="delete-modal-content">
            <div class="delete-modal-header">
                <div class="delete-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3>تأكيد حذف الطلب</h3>
            </div>
            <div class="delete-modal-body">
                <p style="font-size: 1rem; margin-bottom: 0.8rem;">هل أنت متأكد من رغبتك في حذف هذا الطلب نهائياً؟</p>
                <p class="warning-text" style="font-size: 0.9rem; margin-bottom: 1rem;">
                    <i class="fas fa-exclamation-triangle" style="margin-left: 0.5rem;"></i>
                    تحذير: لا يمكن التراجع عن هذا الإجراء! سيتم حذف الطلب وجميع بياناته نهائياً.
                </p>
                <div class="order-info" id="deleteOrderInfo">
                    <!-- Order info will be populated here -->
                </div>
            </div>
            <div class="delete-modal-footer">
                <button class="btn cancel-btn" onclick="hideDeleteModal()">إلغاء</button>
                <button class="btn delete-btn" id="confirmDeleteBtn" onclick="confirmDeleteOrder()">
                    <i class="fas fa-trash"></i>
                    حذف الطلب
                </button>
            </div>
        </div>
    </div>

    <!-- Logout Modal -->
    <div class="logout-modal" id="logoutModal" style="display: none;">
        <div class="logout-modal-content">
            <div class="logout-modal-header">
                <h3>تأكيد تسجيل الخروج</h3>
            </div>
            <div class="logout-modal-body">
                <p>هل أنت متأكد من رغبتك في تسجيل الخروج من لوحة التحكم؟</p>
            </div>
            <div class="logout-modal-footer">
                <button class="btn btn-secondary" onclick="hideLogoutModal()">إلغاء</button>
                <button class="btn btn-danger" onclick="confirmLogout()">تسجيل الخروج</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/unified-sidebar-manager.js"></script>
    <script src="js/unified-notification-system.js"></script>
    <script src="js/logout-system.js"></script>
    <script src="js/orders-management.js"></script>

    <script>
        // Initialize orders manager when page loads
        let ordersManager;

        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!checkAuth()) return;

            // Initialize orders manager
            ordersManager = new OrdersManager();
        });

        // Authentication check function
        function checkAuth() {
            const adminUser = sessionStorage.getItem('adminUser');
            if (!adminUser) {
                window.location.href = 'login.html';
                return false;
            }

            try {
                const user = JSON.parse(adminUser);
                const loginTime = new Date(user.loginTime);
                const now = new Date();
                const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

                if (hoursDiff >= 8) {
                    sessionStorage.removeItem('adminUser');
                    window.location.href = 'login.html';
                    return false;
                }

                return true;
            } catch (error) {
                sessionStorage.removeItem('adminUser');
                window.location.href = 'login.html';
                return false;
            }
        }

        // Logout functions
        function showLogoutModal() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                backdrop-filter: blur(5px);
                animation: fadeIn 0.3s ease;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 15px;
                    padding: 2rem;
                    max-width: 400px;
                    width: 90%;
                    text-align: center;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    animation: slideIn 0.3s ease;
                ">
                    <div style="color: #dc3545; font-size: 3rem; margin-bottom: 1rem;">
                        <i class="fas fa-sign-out-alt"></i>
                    </div>
                    <h3 style="color: #333; margin-bottom: 1rem;">تسجيل الخروج</h3>
                    <p style="color: #666; margin-bottom: 2rem;">هل أنت متأكد من تسجيل الخروج من لوحة التحكم؟</p>
                    <div style="display: flex; gap: 1rem; justify-content: center;">
                        <button onclick="confirmLogout()" style="
                            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                            color: white;
                            border: none;
                            padding: 0.8rem 1.5rem;
                            border-radius: 8px;
                            font-family: 'Cairo', sans-serif;
                            font-weight: 600;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        ">
                            <i class="fas fa-check"></i> نعم، تسجيل الخروج
                        </button>
                        <button onclick="closeLogoutModal()" style="
                            background: #6c757d;
                            color: white;
                            border: none;
                            padding: 0.8rem 1.5rem;
                            border-radius: 8px;
                            font-family: 'Cairo', sans-serif;
                            font-weight: 600;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        ">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            window.currentLogoutModal = modal;
        }

        function confirmLogout() {
            sessionStorage.removeItem('adminUser');
            localStorage.removeItem('adminLoggedIn');
            localStorage.removeItem('adminUsername');
            window.location.href = 'login.html';
        }

        function closeLogoutModal() {
            if (window.currentLogoutModal) {
                window.currentLogoutModal.remove();
                window.currentLogoutModal = null;
            }
        }

        function hideLogoutModal() {
            closeLogoutModal();
        }

        // ===== UNIFIED MOBILE NAVIGATION FUNCTIONS =====
        // Functions are now handled by unified-sidebar-manager.js
        // Legacy functions maintained for backward compatibility

        // Mobile navigation is now handled by unified-sidebar-manager.js
        // No additional initialization needed
    </script>
</body>
</html>
