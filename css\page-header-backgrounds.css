/* Standardized Page Header Component System
 * Professional and consistent page header design across all pages
 * Maintains design system compliance and RTL Arabic layout
 * Author: Care Admin Dashboard
 * Version: 2.0 - Standardized
 */

/* Standardized Page Header - Professional Design System Compliant */
.page-header {
    /* Background and Visual Foundation */
    background: linear-gradient(135deg, var(--primary-dark, #2c3e50) 0%, var(--primary-color, #4a90a4) 100%);
    color: white;
    text-align: center;

    /* Standardized Dimensions and Spacing */
    padding: 6rem 0 4rem 0;

    /* Layout and Positioning */
    position: relative;
    overflow: hidden;

    /* Professional Visual Effects */
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);

    /* Background image properties - will be set dynamically */
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    background-attachment: fixed;

    /* Smooth transition for background changes */
    transition: background-image 0.5s ease-in-out;
}

/* Standardized Decorative Elements and Visual Effects */
.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.page-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
    z-index: 1;
}

/* Background Image Overlay for Text Readability */
.page-header.has-background-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(44, 62, 80, 0.7) 0%,
        rgba(74, 144, 164, 0.6) 50%,
        rgba(44, 62, 80, 0.8) 100%
    );
    z-index: 2;
    pointer-events: none;
}

/* Standardized Container and Content Structure */
.page-header .container {
    position: relative;
    z-index: 3;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.page-header-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

/* Ensure content stays above overlay */
.page-header.has-background-image .container {
    position: relative;
    z-index: 4;
}

/* Standardized Typography - Professional Design System */
.page-header h1 {
    font-family: 'Cairo', sans-serif;
    font-size: var(--font-size-5xl, 3.5rem);
    font-weight: 700;
    color: white;
    margin-bottom: 2rem;
    position: relative;
    letter-spacing: 1px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    line-height: 1.2;
}

.page-header h1::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 4px;
    background: linear-gradient(90deg, transparent, white, transparent);
    border-radius: 2px;
    box-shadow: 0 2px 8px rgba(255,255,255,0.3);
}

.page-header p {
    font-family: 'Cairo', sans-serif;
    font-size: var(--font-size-xl, 1.25rem);
    font-weight: 500;
    color: rgba(255,255,255,0.95);
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.7;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

/* Enhanced text shadow for better readability with background images */
.page-header.has-background-image h1 {
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.5),
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(0, 0, 0, 0.2);
}

.page-header.has-background-image p {
    text-shadow:
        0 1px 3px rgba(0, 0, 0, 0.5),
        0 2px 6px rgba(0, 0, 0, 0.3);
}

/* Standardized Breadcrumb Navigation - RTL Arabic Compliant */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    color: rgba(255,255,255,0.8);
    margin-bottom: 1.5rem;
    direction: rtl;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
}

.breadcrumb a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: color 0.3s ease;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.breadcrumb a:hover {
    color: white;
    background: rgba(255,255,255,0.1);
}

.breadcrumb .separator {
    color: rgba(255,255,255,0.6);
    font-size: 0.875rem;
    margin: 0 0.25rem;
}

.breadcrumb .current {
    color: white;
    font-weight: 600;
}

/* Standardized Decorative Elements */
.page-header-decoration {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    border: 2px solid rgba(255,255,255,0.1);
    border-radius: 50%;
    z-index: 1;
}

.page-header-decoration::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 150px;
    height: 150px;
    border: 1px solid rgba(255,255,255,0.05);
    border-radius: 50%;
}

/* Specific Page Background Classes */
.page-header.products-bg {
    background-image: var(--products-bg-url, linear-gradient(135deg, var(--primary-dark, #2c3e50) 0%, var(--primary-color, #4a90a4) 100%));
}

.page-header.contact-bg {
    background-image: var(--contact-bg-url, linear-gradient(135deg, var(--primary-dark, #2c3e50) 0%, var(--primary-color, #4a90a4) 100%));
}

.page-header.faq-bg {
    background-image: var(--faq-bg-url, linear-gradient(135deg, var(--primary-dark, #2c3e50) 0%, var(--primary-color, #4a90a4) 100%));
}

.page-header.guidelines-bg {
    background-image: var(--guidelines-bg-url, linear-gradient(135deg, var(--primary-dark, #2c3e50) 0%, var(--primary-color, #4a90a4) 100%));
}

.page-header.terms-bg {
    background-image: var(--terms-bg-url, linear-gradient(135deg, var(--primary-dark, #2c3e50) 0%, var(--primary-color, #4a90a4) 100%));
}

.page-header.offers-bg {
    background-image: var(--offers-bg-url, linear-gradient(135deg, var(--primary-dark, #2c3e50) 0%, var(--primary-color, #4a90a4) 100%));
}

/* Background Size Options */
.page-header.bg-cover {
    background-size: cover;
}

.page-header.bg-contain {
    background-size: contain;
}

.page-header.bg-auto {
    background-size: auto;
}

/* Background Position Options */
.page-header.bg-center {
    background-position: center center;
}

.page-header.bg-top {
    background-position: center top;
}

.page-header.bg-bottom {
    background-position: center bottom;
}

.page-header.bg-left {
    background-position: left center;
}

.page-header.bg-right {
    background-position: right center;
}

/* Background Attachment Options */
.page-header.bg-fixed {
    background-attachment: fixed;
}

.page-header.bg-scroll {
    background-attachment: scroll;
}

/* Overlay Intensity Options */
.page-header.overlay-light::after {
    background: linear-gradient(
        135deg,
        rgba(44, 62, 80, 0.3) 0%,
        rgba(74, 144, 164, 0.2) 50%,
        rgba(44, 62, 80, 0.4) 100%
    );
}

.page-header.overlay-medium::after {
    background: linear-gradient(
        135deg,
        rgba(44, 62, 80, 0.5) 0%,
        rgba(74, 144, 164, 0.4) 50%,
        rgba(44, 62, 80, 0.6) 100%
    );
}

.page-header.overlay-dark::after {
    background: linear-gradient(
        135deg,
        rgba(44, 62, 80, 0.8) 0%,
        rgba(74, 144, 164, 0.7) 50%,
        rgba(44, 62, 80, 0.9) 100%
    );
}

/* Loading State for Background Images */
.page-header.bg-loading {
    background-image: linear-gradient(135deg, var(--primary-dark, #2c3e50) 0%, var(--primary-color, #4a90a4) 100%);
    position: relative;
}

.page-header.bg-loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 3;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Error State for Failed Background Images */
.page-header.bg-error {
    background-image: linear-gradient(135deg, var(--primary-dark, #2c3e50) 0%, var(--primary-color, #4a90a4) 100%);
}

/* Desktop Page Header Styles (Base Styles) */
.page-header {
    background-attachment: fixed;
}

.page-header h1 {
    font-size: var(--font-size-5xl, 3.5rem);
}

.page-header p {
    font-size: var(--font-size-xl, 1.25rem);
}
