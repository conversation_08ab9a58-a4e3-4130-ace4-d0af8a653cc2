// Offers Page JavaScript - Enhanced Professional Implementation

// Get shared Supabase client
function getSupabaseClient() {
    // Use shared configuration (singleton pattern)
    if (window.SupabaseConfig) {
        return window.SupabaseConfig.getClient();
    }

    // Fallback: use global client if available
    if (window.globalSupabaseClient) {
        return window.globalSupabaseClient;
    }

    // Last resort: create client directly (should not happen if supabase-config.js is loaded)
    if (window.supabase && typeof window.supabase.createClient === 'function') {
        console.warn('⚠️ Creating Supabase client directly in offers.js - supabase-config.js may not be loaded');
        const SUPABASE_URL = 'https://krqijjttwllohulmdwgs.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70';

        window.globalSupabaseClient = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        return window.globalSupabaseClient;
    }

    return null;
}

// Global variables
let allProducts = [];
let filteredProducts = [];
let cart = JSON.parse(localStorage.getItem('cart')) || [];

// Enhanced filter state
let activeFilters = {
    featured: false,
    new: false,
    bigSavings: false,
    available: false
};

// Utility Functions
function updateCartCount() {
    const cartCount = document.getElementById('cartCount');
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    cartCount.textContent = totalItems;
}

function formatPrice(price) {
    return new Intl.NumberFormat('ar-IQ', {
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(price) + ' د.ع';
}

function calculateDiscount(originalPrice, offerPrice) {
    return Math.round(((originalPrice - offerPrice) / originalPrice) * 100);
}

function getCategoryName(type) {
    const categories = {
        'skincare': 'العناية بالبشرة',
        'haircare': 'العناية بالشعر',
        'makeup': 'المكياج',
        'fragrance': 'العطور'
    };
    return categories[type] || 'منتجات العناية';
}

function isNewProduct(product) {
    const createdDate = new Date(product.created_at);
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    return createdDate > thirtyDaysAgo;
}

// Data Loading Functions
async function loadOffers() {
    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            console.error('Supabase client not available');
            document.getElementById('loading').innerHTML = 'خطأ في الاتصال بقاعدة البيانات';
            return;
        }

        const { data: products, error } = await supabase
            .from('products')
            .select('*')
            .eq('is_active', true)
            .eq('is_on_offer', true)
            .not('offer_price', 'is', null)
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Database error:', error);
            throw error;
        }

        allProducts = products || [];
        filteredProducts = [...allProducts];

        displayProducts();
        updateProductsCount();

        console.log('Offers loaded successfully:', allProducts.length, 'offers');

    } catch (error) {
        console.error('Error loading offers:', error);
        document.getElementById('loading').innerHTML = 'حدث خطأ أثناء تحميل العروض';
    }
}

// Display Functions
function displayProducts() {
    const container = document.getElementById('productsGrid');
    const loading = document.getElementById('loading');
    const noProducts = document.getElementById('noProducts');

    loading.style.display = 'none';

    if (filteredProducts.length === 0) {
        container.style.display = 'none';
        noProducts.style.display = 'block';
        return;
    }

    container.style.display = 'grid';
    noProducts.style.display = 'none';

    container.innerHTML = filteredProducts.map(product => {
        const discountPercent = calculateDiscount(product.price, product.offer_price);
        const savings = product.price - product.offer_price;

        return `
            <div class="product-card" onclick="window.location.href='product-details.html?id=${product.id}'">
                <div class="product-image">
                    ${product.image_url ?
                        `<img src="${product.image_url}" alt="${product.name}" style="width: 100%; height: 100%; object-fit: cover;">` :
                        '<i class="fas fa-image"></i>'
                    }
                </div>
                <div class="product-badges-container">
                    <div class="discount-badge pulse">${discountPercent}% خصم</div>
                    ${product.is_featured ? '<div class="featured-badge">مميز</div>' : ''}
                    ${savings > 50000 ? '<div class="offer-timer">وفر كبير</div>' : ''}
                </div>
                <div class="product-info">
                    <div class="product-category">${getCategoryName(product.product_type)}</div>
                    <div class="product-name">${product.name}</div>
                    ${product.description ? `<div class="product-description">${product.description.substring(0, 100)}${product.description.length > 100 ? '...' : ''}</div>` : ''}
                    <div class="price-section">
                        <div class="current-price">${formatPrice(product.offer_price)}</div>
                        <div class="original-price">${formatPrice(product.price)}</div>
                        <div class="savings">وفر ${formatPrice(savings)}</div>
                    </div>
                    <button class="add-to-cart" onclick="event.stopPropagation(); addToCart(${JSON.stringify(product).replace(/"/g, '&quot;')})"
                            ${!product.is_available ? 'disabled' : ''}>
                        ${product.is_available ? 'إضافة للسلة' : 'غير متوفر'}
                    </button>
                </div>
            </div>
        `;
    }).join('');
}

function updateProductsCount() {
    const count = filteredProducts.length;
    document.getElementById('productsCount').textContent = `${count} عرض`;
}

// Filter Functions
function filterProducts() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const categoryFilter = document.getElementById('categoryFilter').value;
    const discountFilter = parseInt(document.getElementById('discountFilter').value) || 0;
    const priceFilter = document.getElementById('priceFilter').value;

    filteredProducts = allProducts.filter(product => {
        const matchesSearch = product.name.toLowerCase().includes(searchTerm) ||
                            (product.description && product.description.toLowerCase().includes(searchTerm));

        const matchesCategory = !categoryFilter || product.product_type === categoryFilter;

        const discountPercent = calculateDiscount(product.price, product.offer_price);
        const matchesDiscount = discountPercent >= discountFilter;

        // Price filter
        let matchesPrice = true;
        if (priceFilter) {
            const price = product.offer_price || product.price;
            if (priceFilter === '0-25000') {
                matchesPrice = price < 25000;
            } else if (priceFilter === '25000-50000') {
                matchesPrice = price >= 25000 && price <= 50000;
            } else if (priceFilter === '50000-100000') {
                matchesPrice = price >= 50000 && price <= 100000;
            } else if (priceFilter === '100000+') {
                matchesPrice = price > 100000;
            }
        }

        // Quick filters
        const matchesFeatured = !activeFilters.featured || product.is_featured;
        const matchesNew = !activeFilters.new || isNewProduct(product);
        const matchesBigSavings = !activeFilters.bigSavings || (product.price - product.offer_price) > 50000;
        const matchesAvailable = !activeFilters.available || product.is_available;

        return matchesSearch && matchesCategory && matchesDiscount && matchesPrice &&
               matchesFeatured && matchesNew && matchesBigSavings && matchesAvailable;
    });

    sortProducts();
    displayProducts();
    updateProductsCount();
}

function toggleFilter(filterType) {
    activeFilters[filterType] = !activeFilters[filterType];

    const button = document.getElementById(filterType + 'Filter');
    if (activeFilters[filterType]) {
        button.classList.add('active');
    } else {
        button.classList.remove('active');
    }

    filterProducts();
}

function clearAllFilters() {
    // Clear search
    document.getElementById('searchInput').value = '';

    // Clear selects
    document.getElementById('categoryFilter').value = '';
    document.getElementById('discountFilter').value = '';
    document.getElementById('priceFilter').value = '';

    // Clear quick filters
    Object.keys(activeFilters).forEach(key => {
        activeFilters[key] = false;
        document.getElementById(key + 'Filter').classList.remove('active');
    });

    // Reset to all products
    filteredProducts = [...allProducts];
    sortProducts();
    displayProducts();
    updateProductsCount();
}

// Sort Functions
function sortProducts() {
    const sortBy = document.getElementById('sortSelect').value;

    filteredProducts.sort((a, b) => {
        switch(sortBy) {
            case 'discount':
                const discountA = calculateDiscount(a.price, a.offer_price);
                const discountB = calculateDiscount(b.price, b.offer_price);
                return discountB - discountA;
            case 'name':
                return a.name.localeCompare(b.name, 'ar');
            case 'price_low':
                return a.offer_price - b.offer_price;
            case 'price_high':
                return b.offer_price - a.offer_price;
            default:
                return 0;
        }
    });
}

// Cart Functions
function addToCart(product) {
    const existingItem = cart.find(item => item.id === product.id);

    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        // Get the first available image from any of the three image fields
        const firstImage = product.image_url || product.image_url_2 || product.image_url_3;

        cart.push({
            id: product.id,
            name: product.name,
            price: product.offer_price || product.price,
            image_url: firstImage,
            quantity: 1,
            product_type: product.product_type
        });
    }

    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartCount();

    // Show success notification
    showNotification('تم إضافة المنتج للسلة بنجاح!', 'success');
}

function showNotification(message, type = 'success') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `cart-notification show`;
    notification.innerHTML = `
        <div class="notification-card">
            <div class="notification-icon">
                <i class="fas fa-check"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">نجح!</div>
                <div class="notification-message">${message}</div>
                <div class="notification-actions">
                    <a href="cart.html" class="notification-btn primary">
                        <i class="fas fa-shopping-cart"></i>
                        عرض السلة
                    </a>
                    <button class="notification-btn secondary" onclick="this.closest('.cart-notification').remove()">
                        متابعة التسوق
                    </button>
                </div>
            </div>
            <button class="notification-close" onclick="this.closest('.cart-notification').remove()">
                <i class="fas fa-times"></i>
            </button>
            <div class="notification-progress"></div>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.classList.add('hide');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 400);
        }
    }, 5000);
}

// Initialization Functions
function initializePage() {
    updateCartCount();

    // Check if Supabase is available
    if (window.supabase && typeof window.supabase.createClient === 'function') {
        console.log('Supabase available, loading offers...');
        loadOffers();
    } else {
        console.log('Waiting for Supabase to load...');
        setTimeout(initializePage, 100);
    }
}

// Event Listeners Setup
function setupEventListeners() {
    // Enhanced search and filter event listeners
    document.getElementById('searchInput').addEventListener('input', filterProducts);
    document.getElementById('categoryFilter').addEventListener('change', filterProducts);
    document.getElementById('discountFilter').addEventListener('change', filterProducts);
    document.getElementById('priceFilter').addEventListener('change', filterProducts);
    document.getElementById('sortSelect').addEventListener('change', function() {
        sortProducts();
        displayProducts();
    });
}

// Main initialization
document.addEventListener('DOMContentLoaded', function() {
    // Setup event listeners
    setupEventListeners();

    // Wait for site settings to load before loading offers
    if (window.siteSettingsManager) {
        // Site settings already loaded
        setTimeout(initializePage, 100);
    } else {
        // Wait for site settings to load
        window.addEventListener('siteSettingsLoaded', function() {
            setTimeout(initializePage, 100);
        });

        // Fallback: load offers after a delay if site settings don't load
        setTimeout(() => {
            if (allProducts.length === 0) {
                console.log('Loading offers as fallback...');
                initializePage();
            }
        }, 3000);
    }
});
