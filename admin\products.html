<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1024">
    <title>إدارة المنتجات - Care Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/logout-modal.css">
    <link rel="stylesheet" href="css/enhanced-sidebar.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script></script>

    <style>
        /* Enhanced Products-specific styles - base styles are in enhanced-sidebar.css */

        /* Welcome Section */
        .welcome-section .card {
            background: linear-gradient(135deg, var(--color-bg-primary) 0%, #f8f9fa 100%);
            border: 1px solid var(--color-primary-alpha-10);
        }

        .welcome-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: var(--shadow-md);
        }

        /* Enhanced stat icon gradients */
        .stat-icon.total {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
        }
        .stat-icon.active {
            background: linear-gradient(135deg, var(--color-success) 0%, #229954 100%);
        }
        .stat-icon.offers {
            background: linear-gradient(135deg, var(--color-warning) 0%, #e67e22 100%);
        }
        .stat-icon.featured {
            background: linear-gradient(135deg, var(--color-danger) 0%, #c0392b 100%);
        }

        /* Enhanced Messages */
        .messages-container {
            margin-bottom: var(--spacing-lg);
        }

        .message {
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-weight: 600;
            transition: var(--transition-base);
        }

        .message.success {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
            color: var(--color-success);
            border: 1px solid rgba(40, 167, 69, 0.2);
        }

        .message.error {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%);
            color: var(--color-danger);
            border: 1px solid rgba(220, 53, 69, 0.2);
        }

        .message::before {
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        .message.success::before {
            content: '\f058'; /* fa-check-circle */
        }

        .message.error::before {
            content: '\f06a'; /* fa-exclamation-circle */
        }

        /* Enhanced Add Product Section */
        .add-product-section {
            margin-bottom: var(--spacing-xl);
        }

        .section-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .toggle-btn {
            background: transparent;
            border: 1px solid rgba(130, 135, 122, 0.2);
            border-radius: var(--radius-md);
            padding: var(--spacing-sm);
            cursor: pointer;
            transition: var(--transition-base);
            color: var(--color-primary);
        }

        .toggle-btn:hover {
            background: rgba(130, 135, 122, 0.1);
            border-color: var(--color-primary);
        }

        .toggle-icon {
            transition: var(--transition-base);
        }

        .toggle-btn.active .toggle-icon {
            transform: rotate(180deg);
        }

        /* Enhanced Form Styling */
        .enhanced-form {
            padding: var(--spacing-xl);
        }

        .form-section {
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background: rgba(130, 135, 122, 0.02);
            border-radius: var(--radius-md);
            border: 1px solid rgba(130, 135, 122, 0.1);
        }

        .form-section-title {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--color-primary);
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-sm);
            border-bottom: 2px solid rgba(130, 135, 122, 0.1);
        }



        .checkbox-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
        }

        .checkbox-group.enhanced {
            background: var(--color-bg-primary);
            border: 1px solid rgba(130, 135, 122, 0.1);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            transition: var(--transition-base);
        }

        .checkbox-group.enhanced:hover {
            border-color: var(--color-primary);
            box-shadow: var(--shadow-sm);
        }

        .checkbox-group.enhanced label {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-weight: 600;
            cursor: pointer;
            margin-bottom: var(--spacing-xs);
        }

        .checkbox-help {
            font-size: 0.75rem;
            color: var(--color-text-muted);
            margin-top: var(--spacing-xs);
        }

        /* Enhanced Search and Filter Section */
        .search-filter-section {
            padding: var(--spacing-lg);
            background: rgba(130, 135, 122, 0.02);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-lg);
        }

        .search-filter-container {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-md);
            align-items: center;
            margin-bottom: var(--spacing-md);
        }

        .search-box.enhanced {
            position: relative;
            flex: 1;
            min-width: 300px;
        }

        .search-box.enhanced input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-xl) var(--spacing-md) var(--spacing-md);
            border: 2px solid rgba(130, 135, 122, 0.2);
            border-radius: var(--radius-md);
            font-size: 1rem;
            transition: var(--transition-base);
        }

        .search-box.enhanced input:focus {
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(130, 135, 122, 0.1);
        }

        .search-icon {
            position: absolute;
            right: var(--spacing-md);
            top: 50%;
            transform: translateY(-50%);
            color: var(--color-text-muted);
            pointer-events: none;
        }

        .clear-search {
            position: absolute;
            left: var(--spacing-md);
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--color-text-muted);
            cursor: pointer;
            padding: var(--spacing-xs);
            border-radius: 50%;
            transition: var(--transition-base);
        }

        .clear-search:hover {
            background: rgba(220, 53, 69, 0.1);
            color: var(--color-danger);
        }

        .filter-group {
            display: flex;
            gap: var(--spacing-sm);
            flex-wrap: wrap;
        }

        .filter-select {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid rgba(130, 135, 122, 0.2);
            border-radius: var(--radius-sm);
            background: var(--color-bg-primary);
            font-family: 'Cairo', sans-serif;
            font-size: 0.9rem;
            transition: var(--transition-base);
            min-width: 150px;
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(130, 135, 122, 0.1);
        }

        /* Advanced Filters */
        .advanced-filters {
            background: rgba(130, 135, 122, 0.05);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-top: var(--spacing-md);
            border: 1px solid rgba(130, 135, 122, 0.1);
            transition: var(--transition-base);
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            align-items: end;
        }

        .filter-input {
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid #ddd;
            border-radius: var(--radius-sm);
            font-size: 0.9rem;
            transition: var(--transition-base);
        }

        .filter-input:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(130, 135, 122, 0.1);
        }

        .filter-group label {
            display: block;
            margin-bottom: var(--spacing-xs);
            font-weight: 600;
            color: #000000 !important;
            font-size: 0.85rem;
        }

        /* Additional high contrast improvements */
        .advanced-filters {
            border: 2px solid #ddd;
        }

        .filter-info {
            color: #000000 !important;
            font-weight: 500;
        }

        /* Message styling improvements */
        .message.success {
            background-color: #d4edda !important;
            color: #000000 !important;
            border: 2px solid #c3e6cb;
        }

        .message.error {
            background-color: #f8d7da !important;
            color: #000000 !important;
            border: 2px solid #f5c6cb;
        }

        /* Loading state text */
        .loading-state p {
            color: #000000 !important;
            font-weight: 500;
        }

        /* Enhanced form section titles */
        .form-section h4 {
            color: #000000 !important;
            font-weight: 700;
        }

        /* Card headers */
        .card-header {
            border-bottom: 2px solid #e9ecef;
        }

        /* Improve checkbox labels */
        .checkbox-group label {
            color: #000000 !important;
            font-weight: 600;
        }

        /* Old table header CSS integrated into main table styling */

        .filter-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        .results-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: var(--spacing-md);
            border-top: 1px solid rgba(130, 135, 122, 0.1);
            font-size: 0.9rem;
            color: var(--color-text-secondary);
        }

        .filter-info {
            color: var(--color-primary);
            font-weight: 600;
        }

        /* High Contrast Improvements for Better Readability */

        /* Override text colors for better contrast */
        .products-content h2,
        .products-content h3,
        .products-content h4,
        .card-header h3,
        .stat-label,
        .product-name,
        .form-section-title,
        .results-info {
            color: #000000 !important;
            font-weight: 600;
        }

        /* Old product table text CSS integrated into main table styling */

        .product-price {
            color: #000000 !important;
            font-weight: 600;
        }

        /* Status badges with high contrast */
        .status-badge {
            font-weight: 600;
            border: 1px solid;
        }

        .status-badge.active {
            background: #d4edda !important;
            color: #000000 !important;
            border-color: #c3e6cb;
        }

        .status-badge.inactive {
            background: #f8d7da !important;
            color: #000000 !important;
            border-color: #f5c6cb;
        }

        .status-badge.featured {
            background: #fff3cd !important;
            color: #000000 !important;
            border-color: #ffeaa7;
        }

        .status-badge.offer {
            background: #cce5ff !important;
            color: #000000 !important;
            border-color: #b3d9ff;
        }

        /* Form labels and inputs */
        .form-group label,
        .filter-group label {
            color: #000000 !important;
            font-weight: 600;
        }

        .checkbox-help {
            color: #333333 !important;
        }

        /* Statistics cards */
        .stat-value {
            color: #000000 !important;
            font-weight: 700;
        }

        .stat-description {
            color: #333333 !important;
        }

        /* Search and filter elements */
        .search-input,
        .filter-select,
        .filter-input {
            color: #000000 !important;
            border: 2px solid #ddd;
        }

        .search-input::placeholder,
        .filter-input::placeholder {
            color: #666666 !important;
        }

        /* Button text improvements */
        .btn {
            font-weight: 600;
        }

        .btn-primary {
            background-color: #0056b3 !important;
            border-color: #0056b3 !important;
            color: #ffffff !important;
        }

        .btn-outline-primary {
            color: #0056b3 !important;
            border-color: #0056b3 !important;
        }

        .btn-outline-primary:hover {
            background-color: #0056b3 !important;
            color: #ffffff !important;
        }

        /* Action buttons in table */
        .action-btn {
            font-weight: 600;
        }

        /* Welcome section text */
        .text-secondary {
            color: #333333 !important;
        }

        /* Results summary */
        .pagination-info {
            color: #000000 !important;
            font-weight: 500;
        }

        /* Category and type text integrated into main table styling */

        /* Products Container */
        .products-container {
            margin-bottom: var(--spacing-lg);
        }

        .loading-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-xxl);
            gap: var(--spacing-md);
            color: var(--color-text-secondary);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(130, 135, 122, 0.2);
            border-top-color: var(--color-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* Grid View CSS removed since only table view is used */

        .product-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: rgba(130, 135, 122, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--color-text-muted);
            font-size: 2rem;
        }

        .product-info {
            padding: var(--spacing-lg);
        }

        .product-name {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .product-category {
            font-size: 0.9rem;
            color: var(--color-text-secondary);
            margin-bottom: var(--spacing-md);
        }

        .product-price {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .price-current {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--color-primary);
        }

        .price-original {
            font-size: 1rem;
            color: var(--color-text-muted);
            text-decoration: line-through;
        }

        .product-status {
            display: flex;
            gap: var(--spacing-xs);
            margin-bottom: var(--spacing-md);
        }

        .status-badge {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-full);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-badge.active {
            background: rgba(40, 167, 69, 0.1);
            color: var(--color-success);
        }

        .status-badge.featured {
            background: rgba(255, 193, 7, 0.1);
            color: var(--color-warning);
        }

        .status-badge.offer {
            background: rgba(220, 53, 69, 0.1);
            color: var(--color-danger);
        }

        .product-actions {
            display: flex;
            gap: var(--spacing-xs);
        }

        .action-btn {
            flex: 1;
            padding: var(--spacing-sm);
            border: none;
            border-radius: var(--radius-sm);
            cursor: pointer;
            transition: var(--transition-base);
            font-size: 0.9rem;
            font-weight: 600;
        }

        .action-btn.edit {
            background: rgba(23, 162, 184, 0.1);
            color: var(--color-info);
        }

        .action-btn.edit:hover {
            background: var(--color-info);
            color: white;
        }

        .action-btn.delete {
            background: rgba(220, 53, 69, 0.1);
            color: var(--color-danger);
        }

        .action-btn.delete:hover {
            background: var(--color-danger);
            color: white;
        }

        /* REMOVED: Custom sidebar styling - now uses standardized enhanced-sidebar.css */

        /* REMOVED: Custom top bar styling to use standardized design from enhanced-sidebar.css */

        /* REMOVED: Custom brand section styling - now uses standardized enhanced-sidebar.css */



        /* REMOVED: Custom page title and top bar actions styling to use standardized design */

        /* REMOVED: Custom hamburger styling - now uses standardized enhanced-sidebar.css */

        /* REMOVED: Custom backdrop styling - now uses standardized enhanced-sidebar.css */













        .notification-item:nth-child(1) { animation-delay: 0.1s; }
        .notification-item:nth-child(2) { animation-delay: 0.2s; }
        .notification-item:nth-child(3) { animation-delay: 0.3s; }
        .notification-item:nth-child(4) { animation-delay: 0.4s; }
        .notification-item:nth-child(5) { animation-delay: 0.5s; }

        /* Pulse animation for unread notifications */
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(130, 135, 122, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(130, 135, 122, 0); }
            100% { box-shadow: 0 0 0 0 rgba(130, 135, 122, 0); }
        }



        /* Products page uses unified sidebar styles from enhanced-sidebar.css */

        /* ===== RESPONSIVE DESIGN FOR ENHANCED NOTIFICATIONS ===== */
        @media (max-width: 768px) {
            .enhanced-success-notification {
                top: 80px;
                right: 10px;
                left: 10px;
                max-width: none;
                width: calc(100% - 20px);
            }

            .enhanced-success-notification .notification-content {
                padding: 1rem;
                border-radius: 10px;
            }

            .enhanced-success-notification .notification-icon {
                width: 45px;
                height: 45px;
                font-size: 1.2rem;
            }

            .enhanced-success-notification .notification-title {
                font-size: 1rem;
            }

            .enhanced-success-notification .notification-message {
                font-size: 0.9rem;
            }
        }

        /* ===== RESPONSIVE LAYOUT FIXES ===== */

        /* Ensure proper box-sizing for all elements */
        *, *::before, *::after {
            box-sizing: border-box;
        }

        /* Prevent horizontal overflow */
        html, body {
            overflow-x: hidden;
        }

        /* Statistics cards responsive improvements */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
            width: 100%;
        }

        .stat-card {
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        /* Enhanced welcome section */
        .welcome-section {
            margin-bottom: 2rem;
        }

        .welcome-section .flex {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        /* Search and filter improvements */
        .search-filter-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .search-box.enhanced {
            min-width: 300px;
            flex: 1;
        }

        .filter-group {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .filter-select {
            min-width: 150px;
        }















        /* Main Content Layout - Adjusted for Right Sidebar */
        .main-content {
            margin-right: 280px;
            width: calc(100% - 280px);
            min-height: 100vh;
            background: var(--color-bg-secondary);
            transition: all 0.3s ease;
        }

        /* Products Content Container */
        .products-content {
            padding: var(--spacing-xl);
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
            box-sizing: border-box;
        }

        /* Enhanced Products Table Styling */
        .table-view {
            background: white;
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-md);
            border: 1px solid #e9ecef;
        }

        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
            font-size: 0.95rem;
            background: var(--color-bg-primary);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--color-primary-alpha-10);
        }

        .products-table thead {
            background: linear-gradient(135deg, var(--color-bg-secondary) 0%, #e9ecef 100%);
            border-bottom: 2px solid var(--color-bg-tertiary);
        }

        .products-table th {
            padding: 1.2rem 1rem;
            text-align: right;
            font-weight: 700;
            color: #000000 !important;
            background: transparent;
            border-bottom: 2px solid var(--color-bg-tertiary);
            border-right: 1px solid rgba(130, 135, 122, 0.1);
            position: relative;
            white-space: nowrap;
            font-family: 'Cairo', sans-serif;
        }

        .products-table th:first-child {
            border-right: none;
        }

        .products-table tbody tr {
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(130, 135, 122, 0.08);
            background: var(--color-bg-primary);
        }

        .products-table tbody tr:hover {
            background: linear-gradient(135deg, var(--color-bg-secondary) 0%, #fafbfc 100%) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(130, 135, 122, 0.1);
        }

        .products-table td {
            padding: 1rem;
            text-align: right;
            vertical-align: middle;
            color: #000000 !important;
            border-right: 1px solid rgba(130, 135, 122, 0.08);
            line-height: 1.5;
            font-family: 'Cairo', sans-serif;
        }

        .products-table td:first-child {
            border-right: none;
        }

        .products-table tbody tr:last-child {
            border-bottom: none;
        }

        /* Specific Column Styling - Updated for 6-column layout */
        .products-table th:nth-child(1),
        .products-table td:nth-child(1) {
            width: 80px;
            text-align: center;
        }

        .products-table th:nth-child(2),
        .products-table td:nth-child(2) {
            width: 30%;
            min-width: 200px;
        }

        .products-table th:nth-child(3),
        .products-table td:nth-child(3) {
            width: 15%;
            min-width: 120px;
        }

        .products-table th:nth-child(4),
        .products-table td:nth-child(4) {
            width: 20%;
            min-width: 140px;
            text-align: center;
        }

        .products-table th:nth-child(5),
        .products-table td:nth-child(5) {
            width: 15%;
            min-width: 120px;
            text-align: center;
        }

        .products-table th:nth-child(6),
        .products-table td:nth-child(6) {
            width: 120px;
            text-align: center;
        }

        /* Enhanced Product Image Styling */
        .product-image {
            width: 50px;
            height: 50px;
            border-radius: var(--radius-md);
            object-fit: cover;
            border: 2px solid rgba(130, 135, 122, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            background: linear-gradient(135deg, var(--color-bg-secondary) 0%, #fafbfc 100%);
            transition: var(--transition-base);
            box-shadow: 0 2px 4px rgba(130, 135, 122, 0.1);
        }

        .product-image:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 8px rgba(130, 135, 122, 0.2);
        }

        .product-image i {
            color: var(--color-primary);
            font-size: 1.2rem;
        }

        /* Enhanced Product Name and Type Styling */
        .product-name {
            font-weight: 600;
            color: #000000 !important;
            margin-bottom: 0.25rem;
            line-height: 1.3;
            font-family: 'Cairo', sans-serif;
        }

        .product-type {
            font-size: 0.8rem;
            color: var(--color-text-secondary) !important;
            font-weight: 400;
            background: rgba(130, 135, 122, 0.1);
            padding: 0.2rem 0.5rem;
            border-radius: var(--radius-sm);
            display: inline-block;
            font-family: 'Cairo', sans-serif;
        }

        /* Enhanced Price Display Styling */
        .product-price-cell {
            text-align: center;
        }

        .price-display {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
        }

        .current-price {
            font-weight: 700;
            color: #000000 !important;
            font-size: 1rem;
        }

        .original-price {
            font-size: 0.85rem;
            color: #666666 !important;
            text-decoration: line-through;
            font-weight: 400;
        }

        .sale-price {
            font-weight: 700;
            color: #dc3545 !important;
            font-size: 1.1rem;
        }

        .discount-badge {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: #ffffff !important;
            font-size: 0.7rem;
            font-weight: 600;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            white-space: nowrap;
            box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
        }

        /* Legacy price styling for compatibility */
        .product-price {
            font-weight: 600;
            color: #000000 !important;
            font-size: 0.95rem;
        }

        /* Status Badges Container */
        .status-badges {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            align-items: center;
        }

        /* Action Buttons Styling */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            align-items: center;
        }

        .action-buttons .btn {
            padding: 0.4rem 0.8rem;
            font-size: 0.85rem;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-edit {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
        }

        .btn-edit:hover {
            background-color: #0056b3;
            border-color: #0056b3;
            transform: translateY(-1px);
        }

        .btn-delete {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }

        .btn-delete:hover {
            background-color: #c82333;
            border-color: #bd2130;
            transform: translateY(-1px);
        }

        .product-image-cell {
            width: 80px;
            height: 80px;
            border-radius: var(--radius-md);
            object-fit: cover;
        }

        .product-name-cell {
            font-weight: 600;
            color: var(--color-text-primary);
        }

        .product-category-cell {
            color: var(--color-text-secondary);
            font-size: 0.9rem;
        }

        /* Pagination */
        .pagination-container {
            display: flex;
            justify-content: center;
            margin-top: var(--spacing-lg);
        }

        .pagination {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            background: var(--color-bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-md);
        }

        .pagination-btn {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid rgba(130, 135, 122, 0.2);
            border-radius: var(--radius-sm);
            background: transparent;
            color: var(--color-text-secondary);
            cursor: pointer;
            transition: var(--transition-base);
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
        }

        .pagination-btn:hover:not(:disabled) {
            background: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-numbers {
            display: flex;
            gap: var(--spacing-xs);
        }

        .pagination-number {
            width: 40px;
            height: 40px;
            border: 1px solid rgba(130, 135, 122, 0.2);
            border-radius: var(--radius-sm);
            background: transparent;
            color: var(--color-text-secondary);
            cursor: pointer;
            transition: var(--transition-base);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .pagination-number.active {
            background: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
        }

        .pagination-number:hover:not(.active) {
            background: rgba(130, 135, 122, 0.1);
        }

        /* ===== COMPREHENSIVE RESPONSIVE DESIGN SYSTEM ===== */

        /* Desktop Large (>1200px) - Default styles above */

        /* Desktop Medium (1025px - 1200px) */
        @media (max-width: 1200px) and (min-width: 1025px) {
            .main-content {
                margin-right: 260px;
                width: calc(100% - 260px);
            }

            .products-content {
                padding: 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 1.5rem;
            }

            .products-table {
                font-size: 0.9rem;
            }

            .products-table th,
            .products-table td {
                padding: 0.9rem 0.7rem;
            }

            .product-image {
                width: 45px;
                height: 45px;
            }

            .action-buttons .btn {
                padding: 0.4rem 0.7rem;
                font-size: 0.8rem;
                min-width: 65px;
            }
        }

        /* Tablet Responsive Design (769px - 1024px) */
        @media (max-width: 1024px) and (min-width: 769px) {
            .main-content {
                margin-right: 0;
                width: 100%;
                padding: 1rem;
            }

            .products-content {
                padding: 1.5rem;
                max-width: none;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.2rem;
            }

            .search-filter-container {
                flex-direction: column;
                align-items: stretch;
                gap: 1rem;
            }

            .search-box.enhanced {
                min-width: auto;
                width: 100%;
            }

            .filter-group {
                justify-content: flex-start;
                flex-wrap: wrap;
            }

            .products-table {
                font-size: 0.85rem;
            }

            .products-table th,
            .products-table td {
                padding: 0.8rem 0.6rem;
            }

            .product-image {
                width: 40px;
                height: 40px;
            }

            .product-name {
                font-size: 0.9rem;
            }

            .action-buttons .btn {
                padding: 0.3rem 0.6rem;
                font-size: 0.75rem;
                min-width: 60px;
            }

            .status-badge {
                font-size: 0.7rem;
                padding: 0.2rem 0.4rem;
            }

            .price-display {
                gap: 0.2rem;
            }

            .current-price,
            .sale-price {
                font-size: 0.9rem;
            }

            .original-price {
                font-size: 0.75rem;
            }

            .discount-badge {
                font-size: 0.65rem;
                padding: 0.15rem 0.4rem;
            }

            /* Enhanced table container for tablet */
            .table-view {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                border-radius: var(--radius-md);
                box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            }

            .products-table {
                min-width: 650px;
            }
        }

        /* REMOVED: Custom tablet responsive overrides - now uses standardized enhanced-sidebar.css */

        /* Mobile and Tablet Responsive Design */
        @media (max-width: 1199px) {
            /* REMOVED: Custom hamburger display - now uses standardized enhanced-sidebar.css */

            /* REMOVED: Custom sidebar-right styling - now uses standardized .sidebar */





            /* REMOVED: Custom responsive overrides - now uses standardized enhanced-sidebar.css */
        }

        /* Mobile Responsive Design (320px - 768px) */
        @media (max-width: 768px) {
            .main-content {
                margin-right: 0;
                width: 100%;
                padding: 0;
                min-height: 100vh;
            }

            .products-content {
                padding: 1rem;
                max-width: none;
            }

            /* Enhanced mobile table container */
            .table-view {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                border-radius: var(--radius-md);
                margin: 0 -0.5rem;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }

            .products-table {
                min-width: 700px;
                font-size: 0.8rem;
            }

            .products-table th,
            .products-table td {
                padding: 0.7rem 0.5rem;
                white-space: nowrap;
            }

            /* Specific column widths for mobile */
            .products-table th:nth-child(1),
            .products-table td:nth-child(1) {
                width: 60px;
                min-width: 60px;
            }

            .products-table th:nth-child(2),
            .products-table td:nth-child(2) {
                min-width: 140px;
                white-space: normal;
            }

            .products-table th:nth-child(3),
            .products-table td:nth-child(3) {
                min-width: 90px;
            }

            .products-table th:nth-child(4),
            .products-table td:nth-child(4) {
                min-width: 110px;
            }

            .products-table th:nth-child(5),
            .products-table td:nth-child(5) {
                min-width: 80px;
            }

            .products-table th:nth-child(6),
            .products-table td:nth-child(6) {
                min-width: 90px;
            }

            .products-table th:nth-child(7),
            .products-table td:nth-child(7) {
                min-width: 120px;
            }

            .product-image {
                width: 35px;
                height: 35px;
            }

            .product-name {
                font-size: 0.8rem;
                line-height: 1.3;
            }

            .action-buttons {
                display: flex;
                gap: 0.3rem;
                flex-wrap: wrap;
            }

            .action-buttons .btn {
                padding: 0.25rem 0.4rem;
                font-size: 0.7rem;
                min-width: 50px;
            }

            .status-badge {
                font-size: 0.65rem;
                padding: 0.15rem 0.3rem;
            }

            .price-display {
                gap: 0.15rem;
                flex-direction: column;
                align-items: flex-start;
            }

            .current-price,
            .sale-price {
                font-size: 0.8rem;
            }

            .original-price {
                font-size: 0.7rem;
            }

            .discount-badge {
                font-size: 0.6rem;
                padding: 0.1rem 0.3rem;
            }
        }

        /* Extra Small Mobile Screens (320px - 480px) */
        @media (max-width: 480px) {
            .products-content {
                padding: 0.75rem;
            }

            /* Statistics cards mobile optimization */
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
                margin-bottom: 1.5rem;
            }

            .stat-card {
                padding: 1rem;
                min-height: auto;
            }

            .stat-header {
                flex-direction: row;
                align-items: center;
                gap: 0.75rem;
            }

            .stat-icon {
                width: 40px;
                height: 40px;
                font-size: 1.1rem;
            }

            .stat-value {
                font-size: 1.5rem;
            }

            .stat-label {
                font-size: 0.9rem;
            }

            .stat-description {
                font-size: 0.8rem;
            }

            .welcome-section .flex {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .welcome-actions {
                width: 100%;
                justify-content: stretch;
            }

            .welcome-actions .btn {
                flex: 1;
                min-height: 44px;
                font-size: 0.9rem;
            }

            .search-filter-container {
                flex-direction: column;
                align-items: stretch;
                gap: 0.75rem;
            }

            .search-box.enhanced {
                min-width: auto;
            }

            .filter-group {
                justify-content: stretch;
                gap: 0.5rem;
            }

            .filter-select {
                flex: 1;
                min-width: auto;
                min-height: 44px;
            }

            .results-summary {
                flex-direction: column;
                gap: 0.5rem;
                text-align: center;
            }

            .card-header .flex {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }

            .card-actions {
                width: 100%;
                justify-content: stretch;
            }

            .card-actions .btn {
                flex: 1;
                min-height: 44px;
            }

            .form-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .checkbox-grid {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .pagination {
                flex-wrap: wrap;
                justify-content: center;
                gap: 0.5rem;
            }

            .pagination-numbers {
                order: -1;
                width: 100%;
                justify-content: center;
                margin-bottom: 0.75rem;
            }

            /* Enhanced mobile table improvements */
            .table-view {
                margin: 0 -0.75rem;
                border-radius: var(--radius-sm);
            }

            .products-table {
                min-width: 650px;
                font-size: 0.75rem;
            }

            .products-table th,
            .products-table td {
                padding: 0.6rem 0.4rem;
            }

            .product-image {
                width: 30px;
                height: 30px;
            }

            .action-buttons .btn {
                padding: 0.2rem 0.3rem;
                font-size: 0.65rem;
                min-width: 45px;
            }
        }

        @media (max-width: 480px) {
            .enhanced-form {
                padding: var(--spacing-md);
            }

            .form-section {
                padding: var(--spacing-md);
            }

            .search-filter-section {
                padding: var(--spacing-md);
            }

            /* Product card CSS removed */

            .filter-row {
                grid-template-columns: 1fr;
                gap: var(--spacing-sm);
            }

            .advanced-filters {
                padding: var(--spacing-md);
            }

            /* Enhanced Mobile Table Responsiveness */
            .table-view {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                border-radius: var(--radius-md);
                margin: 0 -1rem;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }

            .products-table {
                min-width: 700px;
                font-size: 0.85rem;
            }

            .products-table th,
            .products-table td {
                padding: 0.75rem 0.5rem;
                white-space: nowrap;
            }

            .products-table th:nth-child(1),
            .products-table td:nth-child(1) {
                width: 60px;
            }

            .products-table th:nth-child(4),
            .products-table td:nth-child(4) {
                min-width: 120px;
            }

            .products-table th:nth-child(2),
            .products-table td:nth-child(2) {
                min-width: 150px;
                white-space: normal;
            }

            .products-table th:nth-child(3),
            .products-table td:nth-child(3) {
                min-width: 100px;
            }

            .products-table th:nth-child(4),
            .products-table td:nth-child(4),
            .products-table th:nth-child(5),
            .products-table td:nth-child(5) {
                min-width: 90px;
            }

            .products-table th:nth-child(6),
            .products-table td:nth-child(6) {
                min-width: 100px;
            }

            .products-table th:nth-child(7),
            .products-table td:nth-child(7) {
                min-width: 100px;
            }

            .product-image {
                width: 40px;
                height: 40px;
            }

            .product-name {
                font-size: 0.9rem;
                line-height: 1.2;
            }

            .product-type {
                font-size: 0.75rem;
            }

            .action-buttons {
                flex-direction: column;
                gap: 0.25rem;
            }

            .action-buttons .btn {
                padding: 0.3rem 0.6rem;
                font-size: 0.75rem;
                min-width: 60px;
            }

            .status-badges {
                gap: 0.15rem;
            }

            .status-badge {
                font-size: 0.7rem;
                padding: 0.2rem 0.4rem;
            }
        }

        /* Extra Small Screens */
        @media (max-width: 480px) {
            .table-view {
                margin: 0 -0.5rem;
            }

            .products-table {
                min-width: 600px;
                font-size: 0.8rem;
            }

            .products-table th,
            .products-table td {
                padding: 0.5rem 0.3rem;
            }

            .product-image {
                width: 35px;
                height: 35px;
            }

            .product-name {
                font-size: 0.85rem;
            }

            .price-display {
                gap: 0.15rem;
            }

            .current-price,
            .sale-price {
                font-size: 0.9rem;
            }

            .original-price {
                font-size: 0.75rem;
            }

            .discount-badge {
                font-size: 0.6rem;
                padding: 0.15rem 0.4rem;
            }

            .action-buttons .btn {
                padding: 0.25rem 0.4rem;
                font-size: 0.7rem;
                min-width: 50px;
            }

            .action-buttons .btn i {
                font-size: 0.8rem;
            }

            .table th,
            .table td {
                padding: var(--spacing-sm);
                font-size: 0.9rem;
            }

            .pagination-numbers {
                gap: var(--spacing-xs);
            }

            .pagination-number {
                width: 35px;
                height: 35px;
                font-size: 0.9rem;
            }
        }

        /* Enhanced animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stat-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .stat-card:nth-child(1) { animation-delay: 0.1s; }
        .stat-card:nth-child(2) { animation-delay: 0.2s; }
        .stat-card:nth-child(3) { animation-delay: 0.3s; }
        .stat-card:nth-child(4) { animation-delay: 0.4s; }

        /* Product card animation removed */

        .form-container {
            transition: var(--transition-base);
            overflow: hidden;
        }

        .form-container.show {
            animation: slideDown 0.3s ease forwards;
        }

        @keyframes slideDown {
            from {
                max-height: 0;
                opacity: 0;
            }
            to {
                max-height: 2000px;
                opacity: 1;
            }
        }

        /* Print styles */
        @media print {
            .welcome-actions,
            .card-actions,
            .search-filter-section,
            .pagination-container,
            .action-btn {
                display: none !important;
            }

            .stat-card {
                break-inside: avoid;
                box-shadow: none !important;
                border: 1px solid #ddd !important;
            }

            .table {
                font-size: 0.8rem !important;
            }
        }

        /* Add Product Section */
        .add-product-section {
            background: white;
            border-radius: 15px;
            margin-bottom: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: background 0.3s;
        }

        .section-header:hover {
            background: #f8f9fa;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .toggle-icon {
            transition: transform 0.3s ease;
            font-size: 1rem;
        }

        .toggle-icon.open {
            transform: rotate(90deg);
        }

        .toggle-btn {
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: background-color 0.2s ease;
        }

        .toggle-btn:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }

        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 15px 15px 0 0;
        }

        .section-header:hover {
            background: #f8f9fa;
        }

        .form-container {
            padding: 0;
            max-height: 0;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            opacity: 0;
            visibility: hidden;
            background: white;
            border-radius: 0 0 var(--radius-lg) var(--radius-lg);
        }

        .form-container.open {
            max-height: 4000px;
            opacity: 1;
            padding: var(--spacing-xl);
            visibility: visible;
        }

        /* Ensure form is properly hidden initially */
        .form-container:not(.open) {
            max-height: 0 !important;
            opacity: 0 !important;
            visibility: hidden !important;
            padding: 0 !important;
            overflow: hidden !important;
        }

        /* Force initial state for addProductForm */
        #addProductForm {
            max-height: 0;
            opacity: 0;
            visibility: hidden;
            overflow: hidden;
            padding: 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        #addProductForm.open {
            max-height: 4000px;
            opacity: 1;
            visibility: visible;
            padding: var(--spacing-xl);
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        .form-group.required label::after {
            content: ' *';
            color: #e74c3c;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #82877a;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #eee;
        }

        .btn {
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: #82877a;
            color: white;
        }

        .btn-primary:hover {
            background: #6b7062;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        /* Products List */
        .products-list-section {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .list-header {
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .search-filter {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .search-box {
            position: relative;
        }

        .search-box input {
            padding: 0.5rem 1rem 0.5rem 2.5rem;
            border: 1px solid #ddd;
            border-radius: 20px;
            width: 250px;
        }

        .search-box i {
            position: absolute;
            right: 0.8rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        .filter-select {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }

        /* Old duplicate CSS rules removed - using enhanced table styling */

        .product-name {
            font-weight: 600;
            color: #333;
        }

        .product-price {
            font-weight: 600;
            color: #82877a;
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: var(--radius-full);
            font-size: 0.75rem;
            font-weight: 600;
            text-align: center;
            min-width: 60px;
            white-space: nowrap;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-family: 'Cairo', sans-serif;
            transition: var(--transition-base);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .status-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .status-badge.active {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #000000 !important;
            border: 1px solid #c3e6cb;
        }

        .status-badge.inactive {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #000000 !important;
            border: 1px solid #f5c6cb;
        }

        .status-badge.featured {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #000000 !important;
            border: 1px solid #ffeaa7;
        }

        .status-badge.offer {
            background: linear-gradient(135deg, #cce5ff 0%, #b3d9ff 100%);
            color: #000000 !important;
            border: 1px solid #b3d9ff;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
        }

        .btn-sm {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
            border-radius: var(--radius-md);
            border: none;
            cursor: pointer;
            transition: var(--transition-base);
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            min-width: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-edit {
            background: linear-gradient(135deg, var(--color-info) 0%, #138496 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
        }

        .btn-edit:hover {
            background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(23, 162, 184, 0.4);
        }

        .btn-delete {
            background: linear-gradient(135deg, var(--color-danger) 0%, #c82333 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
        }

        .btn-delete:hover {
            background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4);
        }

        .btn-delete:hover {
            background: #c82333;
        }

        /* Loading */
        .loading {
            text-align: center;
            padding: 2rem;
            color: #82877a;
        }

        .loading i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Enhanced Success Notification System */
        .enhanced-success-notification {
            position: fixed;
            top: 80px;
            right: 20px;
            z-index: 10000;
            max-width: 450px;
            width: calc(100% - 40px);
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 15px;
            box-shadow:
                0 20px 40px rgba(40, 167, 69, 0.15),
                0 8px 25px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(40, 167, 69, 0.1);
            transform: translateX(500px) scale(0.8);
            opacity: 0;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            overflow: hidden;
        }

        .enhanced-success-notification.show {
            transform: translateX(0) scale(1);
            opacity: 1;
        }

        .enhanced-success-notification.hide {
            transform: translateX(500px) scale(0.8);
            opacity: 0;
        }

        .notification-content {
            padding: 1.5rem;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .notification-icon {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            width: 55px;
            height: 55px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            flex-shrink: 0;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            animation: successPulse 0.8s ease-out;
        }

        @keyframes successPulse {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .notification-text {
            flex: 1;
            color: #2d3436;
        }

        .notification-title {
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #28a745;
            line-height: 1.3;
        }

        .notification-message {
            font-size: 1rem;
            color: #636e72;
            line-height: 1.5;
            margin-bottom: 0;
        }

        .notification-close {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: none;
            border: none;
            color: #636e72;
            font-size: 1.2rem;
            cursor: pointer;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            opacity: 0.7;
        }

        .notification-close:hover {
            background: rgba(0, 0, 0, 0.1);
            opacity: 1;
            transform: scale(1.1);
        }

        .notification-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
            border-radius: 0 0 15px 15px;
            animation: progressBar 5s linear forwards;
        }

        @keyframes progressBar {
            from { width: 100%; }
            to { width: 0%; }
        }

        /* Legacy Messages (kept for compatibility) */
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: none;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Additional Mobile Responsive Styles */
        @media (max-width: 768px) {
            /* Form and search improvements */
            .form-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .search-filter {
                flex-direction: column;
                align-items: stretch;
                gap: 1rem;
            }

            .search-box input {
                width: 100%;
                min-height: 44px; /* Touch-friendly */
            }

            /* Welcome section mobile improvements */
            .welcome-section .flex {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .welcome-actions {
                width: 100%;
                justify-content: stretch;
            }

            .welcome-actions .btn {
                flex: 1;
                min-height: 44px;
            }

            /* Search and filter improvements */
            .search-filter-container {
                flex-direction: column;
                align-items: stretch;
                gap: 1rem;
            }

            .filter-group {
                justify-content: stretch;
            }

            .filter-select {
                flex: 1;
                min-width: auto;
                min-height: 44px;
            }

            /* Delete modal responsive */
            .delete-modal-content {
                width: 95%;
                margin: 1rem;
                max-width: 400px;
            }

            .delete-modal-header {
                padding: 1rem;
                flex-direction: column;
                text-align: center;
                gap: 0.5rem;
            }

            .delete-modal-icon {
                font-size: 1.5rem;
            }

            .delete-modal-title {
                font-size: 1.1rem;
            }

            .delete-modal-body {
                padding: 1rem;
            }

            .delete-modal-footer {
                padding: 1rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .delete-btn,
            .cancel-btn {
                width: 100%;
                justify-content: center;
                padding: 1rem;
                min-height: 44px;
            }

            .product-info-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.3rem;
            }

            /* Results summary mobile */
            .results-summary {
                flex-direction: column;
                gap: 0.5rem;
                text-align: center;
            }

            /* Pagination mobile */
            .pagination {
                flex-wrap: wrap;
                justify-content: center;
                gap: 0.5rem;
            }

            .pagination-numbers {
                order: -1;
                width: 100%;
                justify-content: center;
                margin-bottom: 0.5rem;
            }
        }

        /* Mobile menu button styles are now in css/enhanced-sidebar.css */

        /* Delete Confirmation Modal */
        .delete-modal {
            display: none;
            position: fixed;
            z-index: 3000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.6);
            backdrop-filter: blur(8px);
            animation: fadeIn 0.3s ease;
        }

        .delete-modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .delete-modal-content {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 25px 80px rgba(231, 76, 60, 0.3);
            width: 90%;
            max-width: 500px;
            animation: slideInDelete 0.4s ease;
            direction: rtl;
            border: 3px solid #e74c3c;
        }

        .delete-modal-header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 12px 12px 0 0;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .delete-modal-icon {
            font-size: 2rem;
            animation: pulse 2s infinite;
        }

        .delete-modal-title {
            font-size: 1.3rem;
            font-weight: 700;
        }

        .delete-modal-body {
            padding: 2rem;
            text-align: center;
        }

        .delete-warning {
            background: #fff5f5;
            border: 2px solid #fed7d7;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            color: #742a2a;
        }

        .delete-warning h4 {
            color: #e53e3e;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .product-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            border-right: 4px solid #e74c3c;
        }

        .product-info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.3rem 0;
        }

        .product-info-label {
            font-weight: 600;
            color: #333;
        }

        .product-info-value {
            color: #666;
            font-weight: 500;
        }

        .delete-modal-footer {
            padding: 1.5rem;
            display: flex;
            gap: 1rem;
            justify-content: center;
            border-top: 1px solid #eee;
        }

        .delete-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 0.8rem 2rem;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1rem;
        }

        .delete-btn:hover {
            background: #c0392b;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        }

        .cancel-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 0.8rem 2rem;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1rem;
        }

        .cancel-btn:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        @keyframes slideInDelete {
            from {
                opacity: 0;
                transform: translateY(-100px) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
            20%, 40%, 60%, 80% { transform: translateX(2px); }
        }

        .delete-modal-content.shake {
            animation: shake 0.5s ease-in-out;
        }

        /* Loading state for delete button */
        .delete-btn.loading {
            background: #95a5a6;
            cursor: not-allowed;
            pointer-events: none;
        }

        .delete-btn.loading::after {
            content: '';
            width: 16px;
            height: 16px;
            border: 2px solid #fff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>


    <!-- STANDARDIZED Sidebar -->
    <div class="sidebar" id="sidebar">
        <!-- Brand Section (Top) -->
        <div class="sidebar-section brand-section">
            <div class="brand-logo">
                <i class="fas fa-store"></i>
                <h2>Care Admin</h2>
            </div>
            <p class="brand-subtitle">لوحة التحكم الإدارية</p>
        </div>

        <!-- Unified Navigation Section -->
        <div class="sidebar-section unified-navigation">
            <!-- Seamless Navigation List -->
            <nav class="sidebar-nav">
                <!-- Dashboard Navigation Links -->
                <a href="dashboard.html" class="sidebar-link">
                    <i class="fas fa-tachometer-alt"></i>
                    الرئيسية
                </a>
                <a href="orders.html" class="sidebar-link">
                    <i class="fas fa-shopping-bag"></i>
                    إدارة الطلبات
                </a>
                <a href="products.html" class="sidebar-link active">
                    <i class="fas fa-box"></i>
                    إدارة المنتجات
                </a>
                <a href="cart-management.html" class="sidebar-link">
                    <i class="fas fa-shopping-cart"></i>
                    إدارة سلة التسوق
                </a>
                <a href="content.html" class="sidebar-link">
                    <i class="fas fa-edit"></i>
                    إدارة المحتوى
                </a>
                <a href="site-settings.html" class="sidebar-link">
                    <i class="fas fa-cog"></i>
                    إعدادات الموقع
                </a>

                <!-- Admin Navigation Links (seamlessly integrated) -->
                <a href="../index.html" class="sidebar-link" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    عرض الموقع
                </a>
                <a href="#" class="sidebar-link logout-link" onclick="showLogoutModal()" title="تسجيل الخروج">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </nav>

            <!-- User Info Component (at bottom of navigation) -->
            <div class="sidebar-user-info">
                <div class="user-avatar" id="sidebarUserAvatar">A</div>
                <div class="user-details">
                    <div class="user-name" id="sidebarUserName">مدير النظام</div>
                    <div class="user-role">مدير النظام</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Backdrop -->
    <div class="sidebar-backdrop" id="sidebarBackdrop" onclick="closeMobileSidebar()"></div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="top-bar">
            <div class="top-bar-content">
                <div class="page-title-section">
                    <h1 class="page-title">
                        <i class="fas fa-box"></i>
                        إدارة المنتجات
                    </h1>
                </div>

                <div class="top-bar-actions">
                    <!-- Mobile Hamburger Menu Button -->
                    <button class="hamburger-btn" onclick="toggleMobileSidebar()" title="القائمة" id="hamburgerBtn">
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                    </button>


                </div>
            </div>
        </div>

        <div class="products-content">
            <!-- Enhanced Welcome Section -->
            <div class="welcome-section mb-lg">
                <div class="card card-elevated">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-lg">
                            <div class="welcome-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <div>
                                <h2 class="text-xl font-bold mb-sm">إدارة المنتجات</h2>
                                <p class="text-secondary">إدارة شاملة لجميع منتجات المتجر</p>
                            </div>
                        </div>
                        <div class="welcome-actions flex gap-sm">
                            <button class="btn btn-outline-primary btn-sm" onclick="refreshProducts()">
                                <i class="fas fa-sync-alt"></i>
                                تحديث
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="toggleAddForm()">
                                <i class="fas fa-plus"></i>
                                إضافة منتج
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card" data-stat="total">
                    <div class="stat-header">
                        <div class="stat-icon total">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-change neutral">
                            <i class="fas fa-boxes"></i>
                            <span>المجموع</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="totalProducts">
                            <span class="loading-placeholder">0</span>
                        </div>
                        <div class="stat-label">إجمالي المنتجات</div>
                        <div class="stat-description">جميع المنتجات في النظام</div>
                    </div>
                </div>

                <div class="stat-card" data-stat="active">
                    <div class="stat-header">
                        <div class="stat-icon active">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-change positive" id="activeChange">
                            <i class="fas fa-arrow-up"></i>
                            <span>+5%</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="activeProducts">
                            <span class="loading-placeholder">0</span>
                        </div>
                        <div class="stat-label">المنتجات النشطة</div>
                        <div class="stat-description">متاحة للعملاء</div>
                    </div>
                </div>

                <div class="stat-card" data-stat="offers">
                    <div class="stat-header">
                        <div class="stat-icon offers">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="stat-change positive" id="offersChange">
                            <i class="fas fa-arrow-up"></i>
                            <span>+12%</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="offerProducts">
                            <span class="loading-placeholder">0</span>
                        </div>
                        <div class="stat-label">منتجات العروض</div>
                        <div class="stat-description">بأسعار مخفضة</div>
                    </div>
                </div>

                <div class="stat-card" data-stat="featured">
                    <div class="stat-header">
                        <div class="stat-icon featured">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="stat-change positive" id="featuredChange">
                            <i class="fas fa-arrow-up"></i>
                            <span>+8%</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="featuredProducts">
                            <span class="loading-placeholder">0</span>
                        </div>
                        <div class="stat-label">المنتجات المميزة</div>
                        <div class="stat-description">منتجات مختارة</div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Messages -->
            <div class="messages-container">
                <div class="message success" id="successMessage" style="display: none;"></div>
                <div class="message error" id="errorMessage" style="display: none;"></div>
            </div>

            <!-- Enhanced Add Product Section -->
            <div class="add-product-section card" id="addProductSection">
                <div class="card-header" onclick="toggleAddForm()">
                    <div class="flex items-center gap-md">
                        <div class="section-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div>
                            <h3 id="formTitle">إضافة منتج جديد</h3>
                            <p class="text-sm text-secondary">املأ النموذج لإضافة منتج جديد</p>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button type="button" class="toggle-btn" id="toggleBtn" onclick="toggleAddForm()">
                            <i class="fas fa-chevron-right toggle-icon" id="toggleIcon"></i>
                        </button>
                    </div>
                </div>

                <div class="form-container" id="addProductForm">
                    <form id="productForm" class="enhanced-form">
                        <!-- Basic Information Section -->
                        <div class="form-section">
                            <h4 class="form-section-title">
                                <i class="fas fa-info-circle"></i>
                                المعلومات الأساسية
                            </h4>
                            <div class="form-grid">
                                <div class="form-group required">
                                    <label for="productName" class="required">اسم المنتج</label>
                                    <input type="text" id="productName" name="name" required
                                           placeholder="أدخل اسم المنتج">
                                </div>

                                <div class="form-group required">
                                    <label for="productCategory" class="required">الفئة</label>
                                    <select id="productCategory" name="category_id" required>
                                        <option value="">اختر الفئة</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="productType">نوع المنتج</label>
                                    <select id="productType" name="product_type">
                                        <option value="general">عام</option>
                                        <option value="skincare">العناية بالبشرة</option>
                                        <option value="haircare">العناية بالشعر</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Description Section -->
                        <div class="form-section">
                            <h4 class="form-section-title">
                                <i class="fas fa-align-left"></i>
                                الوصف والتفاصيل
                            </h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="productDescription">الوصف المختصر</label>
                                    <textarea id="productDescription" name="description"
                                              placeholder="وصف مختصر وجذاب للمنتج" rows="3"></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="productDetails">التفاصيل الكاملة</label>
                                    <textarea id="productDetails" name="details"
                                              placeholder="تفاصيل مفصلة عن المنتج وفوائده" rows="4"></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="productIngredients">المكونات</label>
                                    <textarea id="productIngredients" name="ingredients"
                                              placeholder="قائمة بمكونات المنتج" rows="3"></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="productUsageInstructions">طريقة الاستخدام</label>
                                    <textarea id="productUsageInstructions" name="usage_instructions"
                                              placeholder="تعليمات استخدام المنتج" rows="3"></textarea>
                                </div> 
                        </div>

                        <!-- Pricing Section -->
                        <div class="form-section">
                            <h4 class="form-section-title">
                                <i class="fas fa-dollar-sign"></i>
                                الأسعار
                            </h4>
                            <div class="form-grid">
                                <div class="form-group required">
                                    <label for="productPrice" class="required">السعر الأساسي (د.ع)</label>
                                    <input type="number" id="productPrice" name="price" min="0" step="any" required
                                           placeholder="0.00">
                                </div>

                                <div class="form-group">
                                    <label for="productOfferPrice">سعر العرض (د.ع)</label>
                                    <input type="number" id="productOfferPrice" name="offer_price" min="0" step="any"
                                           placeholder="0.00">
                                </div>
                            </div>
                        </div>

                        <!-- Images Section -->
                        <div class="form-section">
                            <h4 class="form-section-title">
                                <i class="fas fa-images"></i>
                                صور المنتج
                            </h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="productImage">الصورة الأساسية</label>
                                    <input type="url" id="productImage" name="image_url"
                                           placeholder="https://example.com/image.jpg">
                                </div>

                                <div class="form-group">
                                    <label for="productImage2">الصورة الثانية</label>
                                    <input type="url" id="productImage2" name="image_url_2"
                                           placeholder="https://example.com/image2.jpg">
                                </div>

                                <div class="form-group">
                                    <label for="productImage3">الصورة الثالثة</label>
                                    <input type="url" id="productImage3" name="image_url_3"
                                           placeholder="https://example.com/image3.jpg">
                                </div>
                            </div>
                        </div>

                        <!-- Status Section -->
                        <div class="form-section">
                            <h4 class="form-section-title">
                                <i class="fas fa-toggle-on"></i>
                                حالة المنتج
                            </h4>
                            <div class="checkbox-grid">
                                <div class="checkbox-group enhanced">
                                    <input type="checkbox" id="isAvailable" name="is_available" checked>
                                    <label for="isAvailable">
                                        <i class="fas fa-check-circle"></i>
                                        متوفر للبيع
                                    </label>
                                    <div class="checkbox-help">المنتج متاح للشراء</div>
                                </div>

                                <div class="checkbox-group enhanced">
                                    <input type="checkbox" id="isFeatured" name="is_featured">
                                    <label for="isFeatured">
                                        <i class="fas fa-star"></i>
                                        منتج مميز
                                    </label>
                                    <div class="checkbox-help">يظهر في المنتجات المميزة</div>
                                </div>

                                <div class="checkbox-group enhanced">
                                    <input type="checkbox" id="isActive" name="is_active" checked>
                                    <label for="isActive">
                                        <i class="fas fa-eye"></i>
                                        نشط ومرئي
                                    </label>
                                    <div class="checkbox-help">المنتج مرئي في الموقع</div>
                                </div>

                                <div class="checkbox-group enhanced">
                                    <input type="checkbox" id="isOnOffer" name="is_on_offer">
                                    <label for="isOnOffer">
                                        <i class="fas fa-tags"></i>
                                        في العروض
                                    </label>
                                    <div class="checkbox-help">يظهر في صفحة العروض</div>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary" onclick="cancelForm()">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="resetForm()">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين
                            </button>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-save"></i>
                                حفظ المنتج
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Enhanced Products List Section -->
            <div class="products-list-section card">
                <div class="card-header">
                    <div class="flex items-center gap-md">
                        <div class="section-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div>
                            <h3>قائمة المنتجات</h3>
                            <p class="text-sm text-secondary">إدارة وتحرير المنتجات الموجودة</p>
                        </div>
                    </div>
                    <div class="card-actions">
                        <!-- View toggle buttons removed as requested -->
                    </div>
                </div>

                <!-- Enhanced Search and Filter Section -->
                <div class="search-filter-section">
                    <div class="search-filter-container">
                        <div class="search-box enhanced">
                            <input type="text" id="searchInput" placeholder="البحث في المنتجات..."
                                   onkeyup="filterProducts()">
                            <i class="fas fa-search search-icon"></i>
                            <button class="clear-search" onclick="clearSearch()" style="display: none;">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="filter-group">
                            <select id="categoryFilter" class="filter-select" onchange="filterProducts()">
                                <option value="">جميع الفئات</option>
                            </select>

                            <select id="statusFilter" class="filter-select" onchange="filterProducts()">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="featured">مميز</option>
                                <option value="offers">في العروض</option>
                            </select>

                            <select id="sortFilter" class="filter-select" onchange="sortProducts()">
                                <option value="name_asc">الاسم (أ-ي)</option>
                                <option value="name_desc">الاسم (ي-أ)</option>
                                <option value="price_asc">السعر (منخفض-مرتفع)</option>
                                <option value="price_desc">السعر (مرتفع-منخفض)</option>
                                <option value="date_desc">الأحدث أولاً</option>
                                <option value="date_asc">الأقدم أولاً</option>
                            </select>
                        </div>

                        <!-- Advanced Filters Section -->
                        <div class="advanced-filters" id="advancedFilters" style="display: none;">
                            <div class="filter-row">
                                <div class="filter-group">
                                    <label for="minPrice">السعر الأدنى</label>
                                    <input type="number" id="minPrice" class="filter-input" placeholder="0" min="0" onchange="filterProducts()">
                                </div>
                                <div class="filter-group">
                                    <label for="maxPrice">السعر الأعلى</label>
                                    <input type="number" id="maxPrice" class="filter-input" placeholder="1000000" min="0" onchange="filterProducts()">
                                </div>
                                <div class="filter-group">
                                    <label for="productTypeFilter">نوع المنتج</label>
                                    <select id="productTypeFilter" class="filter-select" onchange="filterProducts()">
                                        <option value="">جميع الأنواع</option>
                                        <option value="skincare">العناية بالبشرة</option>
                                        <option value="haircare">العناية بالشعر</option>
                                        <option value="general">عام</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label for="availability">التوفر</label>
                                    <select id="availability" class="filter-select" onchange="filterProducts()">
                                        <option value="">جميع المنتجات</option>
                                        <option value="available">متوفر</option>
                                        <option value="unavailable">غير متوفر</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="filter-actions">
                            <button class="btn btn-sm btn-outline-secondary" onclick="clearAllFilters()">
                                <i class="fas fa-filter"></i>
                                مسح الفلاتر
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="showAdvancedFilters()">
                                <i class="fas fa-sliders-h"></i>
                                فلاتر متقدمة
                            </button>
                        </div>
                    </div>

                    <!-- Results Summary -->
                    <div class="results-summary">
                        <div class="results-info">
                            <span id="resultsCount">0</span> منتج
                            <span id="filterInfo" class="filter-info"></span>
                        </div>
                        <div class="pagination-info">
                            <span id="paginationInfo">صفحة 1 من 1</span>
                        </div>
                    </div>
                </div>

                <!-- Products Container -->
                <div class="products-container">
                    <div id="productsTableContainer" class="table-view">
                        <div class="loading-state">
                            <div class="loading-spinner"></div>
                            <p>جاري تحميل المنتجات...</p>
                        </div>
                    </div>

                    <!-- Grid view container removed since only table view is used -->
                </div>

                <!-- Pagination -->
                <div class="pagination-container" id="paginationContainer" style="display: none;">
                    <div class="pagination">
                        <button class="pagination-btn" id="prevBtn" onclick="changePage(-1)">
                            <i class="fas fa-chevron-right"></i>
                            السابق
                        </button>
                        <div class="pagination-numbers" id="paginationNumbers">
                            <!-- Page numbers will be populated by JavaScript -->
                        </div>
                        <button class="pagination-btn" id="nextBtn" onclick="changePage(1)">
                            التالي
                            <i class="fas fa-chevron-left"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="delete-modal" id="deleteModal">
        <div class="delete-modal-content">
            <div class="delete-modal-header">
                <i class="fas fa-exclamation-triangle delete-modal-icon"></i>
                <div class="delete-modal-title">تأكيد حذف المنتج</div>
            </div>

            <div class="delete-modal-body">
                <div class="delete-warning">
                    <h4><i class="fas fa-warning"></i> تحذير مهم</h4>
                    <p>أنت على وشك حذف هذا المنتج نهائياً. هذا الإجراء لا يمكن التراجع عنه!</p>
                </div>

                <div class="product-info">
                    <div class="product-info-item">
                        <span class="product-info-label">اسم المنتج:</span>
                        <span class="product-info-value" id="deleteProductName">-</span>
                    </div>
                    <div class="product-info-item">
                        <span class="product-info-label">الفئة:</span>
                        <span class="product-info-value" id="deleteProductCategory">-</span>
                    </div>
                    <div class="product-info-item">
                        <span class="product-info-label">السعر:</span>
                        <span class="product-info-value" id="deleteProductPrice">-</span>
                    </div>
                    <div class="product-info-item">
                        <span class="product-info-label">الحالة:</span>
                        <span class="product-info-value" id="deleteProductStatus">-</span>
                    </div>
                </div>

                <p style="color: #e74c3c; font-weight: 600; margin-top: 1rem;">
                    هل أنت متأكد من رغبتك في حذف هذا المنتج؟
                </p>
            </div>

            <div class="delete-modal-footer">
                <button class="cancel-btn" onclick="closeDeleteModal()">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button class="delete-btn" id="confirmDeleteBtn" onclick="confirmDelete()">
                    <i class="fas fa-trash"></i>
                    تأكيد الحذف
                </button>
            </div>
        </div>
    </div>

    <script>
        // Supabase configuration
        const SUPABASE_URL = 'https://krqijjttwllohulmdwgs.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70';

        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

        // Global variables
        let allProducts = [];
        let allCategories = [];
        let editingProductId = null;

        // Check authentication
        function checkAuth() {
            const adminUser = sessionStorage.getItem('adminUser');
            if (!adminUser) {
                window.location.href = 'login.html';
                return false;
            }

            try {
                const user = JSON.parse(adminUser);
                const loginTime = new Date(user.loginTime);
                const now = new Date();
                const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

                if (hoursDiff >= 8) {
                    sessionStorage.removeItem('adminUser');
                    window.location.href = 'login.html';
                    return false;
                }

                // Update user info in sidebar - always use standardized values
                const sidebarUserName = document.getElementById('sidebarUserName');
                const sidebarUserAvatar = document.getElementById('sidebarUserAvatar');

                if (sidebarUserName) {
                    sidebarUserName.textContent = 'مدير النظام';
                }
                if (sidebarUserAvatar) {
                    sidebarUserAvatar.textContent = 'A';
                    sidebarUserAvatar.setAttribute('title', 'مدير النظام');
                }

                return true;
            } catch (error) {
                sessionStorage.removeItem('adminUser');
                window.location.href = 'login.html';
                return false;
            }
        }

        // Enhanced logout function with modal
        function showLogoutModal() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                backdrop-filter: blur(5px);
                animation: fadeIn 0.3s ease;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 15px;
                    padding: 2rem;
                    max-width: 400px;
                    width: 90%;
                    text-align: center;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    animation: slideIn 0.3s ease;
                ">
                    <div style="color: #dc3545; font-size: 3rem; margin-bottom: 1rem;">
                        <i class="fas fa-sign-out-alt"></i>
                    </div>
                    <h3 style="color: #333; margin-bottom: 1rem;">تسجيل الخروج</h3>
                    <p style="color: #666; margin-bottom: 2rem;">هل أنت متأكد من تسجيل الخروج من لوحة التحكم؟</p>
                    <div style="display: flex; gap: 1rem; justify-content: center;">
                        <button onclick="confirmLogout()" style="
                            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                            color: white;
                            border: none;
                            padding: 0.8rem 1.5rem;
                            border-radius: 8px;
                            font-family: 'Cairo', sans-serif;
                            font-weight: 600;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        ">
                            <i class="fas fa-check"></i> نعم، تسجيل الخروج
                        </button>
                        <button onclick="closeLogoutModal()" style="
                            background: #6c757d;
                            color: white;
                            border: none;
                            padding: 0.8rem 1.5rem;
                            border-radius: 8px;
                            font-family: 'Cairo', sans-serif;
                            font-weight: 600;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        ">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            window.currentLogoutModal = modal;
        }

        function confirmLogout() {
            localStorage.removeItem('adminLoggedIn');
            localStorage.removeItem('adminUsername');
            window.location.href = 'login.html';
        }

        function closeLogoutModal() {
            if (window.currentLogoutModal) {
                window.currentLogoutModal.remove();
                window.currentLogoutModal = null;
            }
        }

        // Logout function - now uses professional modal system
        function logout() {
            showLogoutModal();
        }

        // Sidebar toggle function is now in js/enhanced-sidebar.js

        // Toggle add form
        function toggleAddForm() {
            const form = document.getElementById('addProductForm');
            const icon = document.getElementById('toggleIcon');
            const formTitle = document.getElementById('formTitle');

            if (!form || !icon) {
                console.error('Form or icon element not found');
                return;
            }

            const isOpen = form.classList.contains('open');
            console.log('Toggle form - Current state:', isOpen ? 'open' : 'closed');

            if (isOpen) {
                // Closing the form
                form.classList.remove('open');
                icon.classList.remove('open');
                icon.className = 'fas fa-chevron-right toggle-icon';
                if (formTitle) formTitle.textContent = 'إضافة منتج جديد';
                console.log('Form closed');

                // Clear any inline styles
                form.style.removeProperty('max-height');
                form.style.removeProperty('opacity');
                form.style.removeProperty('visibility');
                form.style.removeProperty('padding');
            } else {
                // Opening the form
                form.classList.add('open');
                icon.classList.add('open');
                icon.className = 'fas fa-chevron-down toggle-icon open';
                if (formTitle) formTitle.textContent = 'إضافة منتج جديد';
                console.log('Form opened');

                // Scroll to form after opening
                setTimeout(() => {
                    const addProductSection = document.querySelector('.add-product-section');
                    if (addProductSection) {
                        addProductSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                }, 300);
            }

            // Save state to localStorage
            localStorage.setItem('addFormOpen', !isOpen);
        }

        // Production-ready form system - debug functions removed

        // Alternative toggle function using display property
        function toggleAddFormAlternative() {
            const form = document.getElementById('addProductForm');
            const icon = document.getElementById('toggleIcon');

            if (!form || !icon) {
                console.error('Form or icon element not found');
                return;
            }

            const currentDisplay = window.getComputedStyle(form).display;
            const isHidden = currentDisplay === 'none' || form.style.display === 'none';

            if (isHidden) {
                // Show the form
                form.style.display = 'block';
                form.style.maxHeight = 'none';
                form.style.opacity = '1';
                form.style.visibility = 'visible';
                form.style.padding = '1.5rem';
                form.classList.add('open');
                icon.classList.add('open');
                icon.className = 'fas fa-chevron-down toggle-icon open';
                localStorage.setItem('addFormOpen', true);
                console.log('Form shown (alternative method)');
            } else {
                // Hide the form
                form.style.display = 'none';
                form.classList.remove('open');
                icon.classList.remove('open');
                icon.className = 'fas fa-chevron-right toggle-icon';
                localStorage.setItem('addFormOpen', false);
                console.log('Form hidden (alternative method)');
            }
        }

        // Make alternative function available globally
        window.toggleAddFormAlternative = toggleAddFormAlternative;

        // Enhanced Success Notification System
        function showEnhancedSuccessNotification(title, message) {
            // Remove any existing notification
            const existingNotification = document.querySelector('.enhanced-success-notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            const notification = document.createElement('div');
            notification.className = 'enhanced-success-notification';
            notification.innerHTML = `
                <div class="notification-content">
                    <div class="notification-icon">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="notification-text">
                        <div class="notification-title">${title}</div>
                        <div class="notification-message">${message}</div>
                    </div>
                    <button class="notification-close" onclick="closeEnhancedNotification()">
                        <i class="fas fa-times"></i>
                    </button>
                    <div class="notification-progress"></div>
                </div>
            `;

            document.body.appendChild(notification);

            // Trigger show animation
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);

            // Auto close after 5 seconds
            setTimeout(() => {
                closeEnhancedNotification();
            }, 5000);
        }

        function closeEnhancedNotification() {
            const notification = document.querySelector('.enhanced-success-notification');
            if (notification) {
                notification.classList.add('hide');
                setTimeout(() => {
                    if (notification.parentElement) {
                        document.body.removeChild(notification);
                    }
                }, 500);
            }
        }

        // Legacy message function (kept for compatibility)
        function showMessage(message, type = 'success') {
            if (type === 'success') {
                // Use enhanced notification for success messages
                showEnhancedSuccessNotification('تم بنجاح!', message);
                return;
            }

            // Keep legacy error handling
            const successDiv = document.getElementById('successMessage');
            const errorDiv = document.getElementById('errorMessage');

            if (type === 'success') {
                successDiv.textContent = message;
                successDiv.style.display = 'block';
                errorDiv.style.display = 'none';
            } else {
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
                successDiv.style.display = 'none';
            }

            // Hide message after 5 seconds
            setTimeout(() => {
                successDiv.style.display = 'none';
                errorDiv.style.display = 'none';
            }, 5000);
        }

        // Format price
        function formatPrice(price) {
            return new Intl.NumberFormat('ar-IQ', {
                style: 'decimal',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(price) + ' د.ع';
        }

        // Enhanced price display with original and sale price
        function formatPriceDisplay(product) {
            const hasOffer = product.offer_price && product.offer_price < product.price;

            if (hasOffer) {
                return `
                    <div class="price-display">
                        <div class="original-price">${formatPrice(product.price)}</div>
                        <div class="sale-price">${formatPrice(product.offer_price)}</div>
                        <div class="discount-badge">
                            ${Math.round(((product.price - product.offer_price) / product.price) * 100)}% خصم
                        </div>
                    </div>
                `;
            } else {
                return `
                    <div class="price-display">
                        <div class="current-price">${formatPrice(product.price)}</div>
                    </div>
                `;
            }
        }

        // Load categories
        async function loadCategories() {
            try {
                const { data: categories, error } = await supabase
                    .from('categories')
                    .select('*')
                    .eq('is_active', true)
                    .order('name');

                if (error) throw error;

                // Remove duplicates based on name and id
                const uniqueCategories = [];
                const seenIds = new Set();
                const seenNames = new Set();

                (categories || []).forEach(category => {
                    if (!seenIds.has(category.id) && !seenNames.has(category.name.toLowerCase())) {
                        uniqueCategories.push(category);
                        seenIds.add(category.id);
                        seenNames.add(category.name.toLowerCase());
                    }
                });

                allCategories = uniqueCategories;

                // Populate category selects
                const categorySelect = document.getElementById('productCategory');
                const categoryFilter = document.getElementById('categoryFilter');

                categorySelect.innerHTML = '<option value="">اختر الفئة</option>';
                categoryFilter.innerHTML = '<option value="">جميع الفئات</option>';

                allCategories.forEach(category => {
                    const option1 = new Option(category.name, category.id);
                    const option2 = new Option(category.name, category.id);
                    categorySelect.add(option1);
                    categoryFilter.add(option2);
                });

            } catch (error) {
                console.error('Error loading categories:', error);
                showMessage('خطأ في تحميل الفئات', 'error');
            }
        }

        // Load products statistics
        async function loadProductsStats() {
            try {
                const { data: products, error } = await supabase
                    .from('products')
                    .select('*');

                if (error) throw error;

                const total = products.length;
                const active = products.filter(p => p.is_active).length;
                const offers = products.filter(p => p.is_on_offer).length;
                const featured = products.filter(p => p.is_featured).length;

                document.getElementById('totalProducts').textContent = total;
                document.getElementById('activeProducts').textContent = active;
                document.getElementById('offerProducts').textContent = offers;
                document.getElementById('featuredProducts').textContent = featured;

            } catch (error) {
                console.error('Error loading products stats:', error);
            }
        }

        // Load products
        async function loadProducts() {
            try {
                const { data: products, error } = await supabase
                    .from('products')
                    .select(`
                        *,
                        categories (
                            name
                        )
                    `)
                    .order('created_at', { ascending: false });

                if (error) throw error;

                allProducts = products || [];
                displayProducts(allProducts);

            } catch (error) {
                console.error('Error loading products:', error);
                document.getElementById('productsTableContainer').innerHTML =
                    '<p style="text-align: center; color: #e74c3c; padding: 2rem;">خطأ في تحميل المنتجات</p>';
                updateResultsCount(0, 0);
            }
        }

        // Display products
        function displayProducts(products) {
            const container = document.getElementById('productsTableContainer');

            if (products.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; padding: 2rem;">لا توجد منتجات</p>';
                updateResultsCount(0, allProducts.length);
                return;
            }

            const table = `
                <table class="products-table" role="table" aria-label="جدول إدارة المنتجات">
                    <thead>
                        <tr role="row">
                            <th role="columnheader" scope="col" aria-label="صورة المنتج">الصورة</th>
                            <th role="columnheader" scope="col" aria-label="اسم المنتج">اسم المنتج</th>
                            <th role="columnheader" scope="col" aria-label="فئة المنتج">الفئة</th>
                            <th role="columnheader" scope="col" aria-label="سعر المنتج">السعر</th>
                            <th role="columnheader" scope="col" aria-label="حالة المنتج">الحالة</th>
                            <th role="columnheader" scope="col" aria-label="إجراءات المنتج">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${products.map(product => {
                            // Get the first available image from any of the three image fields
                            const firstImage = product.image_url || product.image_url_2 || product.image_url_3;

                            return `
                            <tr>
                                <td>
                                    ${firstImage ?
                                        `<img src="${firstImage}" alt="${product.name}" class="product-image">` :
                                        '<div class="product-image"><i class="fas fa-image"></i></div>'
                                    }
                                </td>
                                <td>
                                    <div class="product-name">${product.name}</div>
                                    <div class="product-type">${product.product_type || 'عام'}</div>
                                </td>
                                <td>${product.categories?.name || 'غير محدد'}</td>
                                <td class="product-price-cell">
                                    ${formatPriceDisplay(product)}
                                </td>
                                <td>
                                    <div class="status-badges">
                                        <span class="status-badge ${product.is_active ? 'active' : 'inactive'}">
                                            ${product.is_active ? 'نشط' : 'غير نشط'}
                                        </span>
                                        ${product.is_featured ? '<span class="status-badge featured">مميز</span>' : ''}
                                        ${product.is_on_offer ? '<span class="status-badge offer">عرض</span>' : ''}
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-edit btn-sm" onclick="editProduct('${product.id}')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-delete btn-sm" onclick="deleteProduct('${product.id}', '${product.name}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            `;

            container.innerHTML = table;

            // Update results count
            updateResultsCount(products.length, allProducts.length);
        }

        // Filter products
        function filterProducts() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const categoryFilter = document.getElementById('categoryFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            const minPrice = parseFloat(document.getElementById('minPrice').value) || 0;
            const maxPrice = parseFloat(document.getElementById('maxPrice').value) || Infinity;
            const productType = document.getElementById('productTypeFilter').value;
            const availability = document.getElementById('availability').value;

            // Show/hide clear search button
            const clearBtn = document.querySelector('.clear-search');
            if (searchTerm) {
                clearBtn.style.display = 'block';
            } else {
                clearBtn.style.display = 'none';
            }

            let filtered = allProducts.filter(product => {
                // Search filter
                const matchesSearch = product.name.toLowerCase().includes(searchTerm) ||
                                    (product.description && product.description.toLowerCase().includes(searchTerm));

                // Category filter
                const matchesCategory = !categoryFilter || product.category_id === categoryFilter;

                // Status filter
                let matchesStatus = true;
                switch(statusFilter) {
                    case 'active':
                        matchesStatus = product.is_active;
                        break;
                    case 'inactive':
                        matchesStatus = !product.is_active;
                        break;
                    case 'featured':
                        matchesStatus = product.is_featured;
                        break;
                    case 'offers':
                        matchesStatus = product.is_on_offer;
                        break;
                }

                // Price range filter
                const productPrice = product.offer_price || product.price;
                const matchesPrice = productPrice >= minPrice && productPrice <= maxPrice;

                // Product type filter
                const matchesType = !productType || product.product_type === productType;

                // Availability filter
                let matchesAvailability = true;
                switch(availability) {
                    case 'available':
                        matchesAvailability = product.is_available;
                        break;
                    case 'unavailable':
                        matchesAvailability = !product.is_available;
                        break;
                }

                return matchesSearch && matchesCategory && matchesStatus &&
                       matchesPrice && matchesType && matchesAvailability;
            });

            displayProducts(filtered);
        }

        // Reset form
        function resetForm() {
            document.getElementById('productForm').reset();
            editingProductId = null;
            document.getElementById('submitBtn').innerHTML = '<i class="fas fa-save"></i> حفظ المنتج';

            // Clear any error states
            const inputs = document.querySelectorAll('#productForm input, #productForm textarea, #productForm select');
            inputs.forEach(input => {
                input.style.borderColor = '#ddd';
            });
        }

        // Cancel form (reset and close)
        function cancelForm() {
            // Reset the form
            resetForm();

            // Close the form
            const form = document.getElementById('addProductForm');
            const icon = document.getElementById('toggleIcon');

            form.classList.remove('open');
            icon.classList.remove('open');
            icon.className = 'fas fa-chevron-right toggle-icon';
            localStorage.setItem('addFormOpen', false);

            // Show confirmation message
            showMessage('تم إلغاء العملية', 'success');
        }

        // Handle form submission
        async function handleFormSubmit(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const submitBtn = document.getElementById('submitBtn');

            // Validate required fields
            const name = formData.get('name').trim();
            const price = parseFloat(formData.get('price'));
            const offerPrice = formData.get('offer_price') ? parseFloat(formData.get('offer_price')) : null;
            const categoryId = formData.get('category_id');

            if (!name || !price || !categoryId) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            // Validate price values
            if (price <= 0) {
                showMessage('السعر يجب أن يكون أكبر من صفر', 'error');
                return;
            }

            if (offerPrice && offerPrice >= price) {
                showMessage('سعر العرض يجب أن يكون أقل من السعر الأساسي', 'error');
                return;
            }

            if (offerPrice && offerPrice <= 0) {
                showMessage('سعر العرض يجب أن يكون أكبر من صفر', 'error');
                return;
            }

            // Show loading
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

            try {
                const productData = {
                    name: name,
                    description: formData.get('description') || null,
                    details: formData.get('details') || null,
                    ingredients: formData.get('ingredients') || null,
                    usage_instructions: formData.get('usage_instructions') || null,
                    warnings: formData.get('warnings') || null,
                    price: price,
                    offer_price: offerPrice,
                    image_url: formData.get('image_url') || null,
                    image_url_2: formData.get('image_url_2') || null,
                    image_url_3: formData.get('image_url_3') || null,
                    category_id: formData.get('category_id'),
                    product_type: formData.get('product_type') || 'general',
                    is_available: formData.has('is_available'),
                    is_featured: formData.has('is_featured'),
                    is_active: formData.has('is_active'),
                    is_on_offer: formData.has('is_on_offer') && offerPrice && offerPrice < price
                };

                let result;
                if (editingProductId) {
                    // Update existing product
                    result = await supabase
                        .from('products')
                        .update(productData)
                        .eq('id', editingProductId);
                } else {
                    // Create new product
                    result = await supabase
                        .from('products')
                        .insert([productData]);
                }

                if (result.error) throw result.error;

                showMessage(editingProductId ? 'تم تحديث المنتج بنجاح!' : 'تم إضافة المنتج بنجاح!', 'success');

                // Reset form and close it
                resetForm();
                const form = document.getElementById('addProductForm');
                const icon = document.getElementById('toggleIcon');
                form.classList.remove('open');
                icon.classList.remove('open');
                icon.className = 'fas fa-chevron-right toggle-icon';
                localStorage.setItem('addFormOpen', false);

                loadProducts();
                loadProductsStats();

            } catch (error) {
                console.error('Error saving product:', error);
                showMessage('حدث خطأ أثناء حفظ المنتج', 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ المنتج';
            }
        }

        // Edit product
        async function editProduct(productId) {
            try {
                const product = allProducts.find(p => p.id === productId);
                if (!product) {
                    showMessage('المنتج غير موجود', 'error');
                    return;
                }

                // Fill form with product data
                document.getElementById('productName').value = product.name || '';
                document.getElementById('productCategory').value = product.category_id || '';
                document.getElementById('productDescription').value = product.description || '';
                document.getElementById('productDetails').value = product.details || '';
                document.getElementById('productIngredients').value = product.ingredients || '';
                document.getElementById('productUsageInstructions').value = product.usage_instructions || '';
                document.getElementById('productWarnings').value = product.warnings || '';
                document.getElementById('productPrice').value = product.price || '';
                document.getElementById('productOfferPrice').value = product.offer_price || '';
                document.getElementById('productType').value = product.product_type || 'general';
                document.getElementById('productImage').value = product.image_url || '';
                document.getElementById('productImage2').value = product.image_url_2 || '';
                document.getElementById('productImage3').value = product.image_url_3 || '';

                // Set checkboxes
                document.getElementById('isAvailable').checked = product.is_available;
                document.getElementById('isFeatured').checked = product.is_featured;
                document.getElementById('isActive').checked = product.is_active;
                document.getElementById('isOnOffer').checked = product.is_on_offer;

                // Update form state
                editingProductId = productId;
                document.getElementById('submitBtn').innerHTML = '<i class="fas fa-save"></i> تحديث المنتج';

                // Open form if closed
                const form = document.getElementById('addProductForm');
                const icon = document.getElementById('toggleIcon');
                if (!form.classList.contains('open')) {
                    form.classList.add('open');
                    icon.classList.add('open');
                    icon.className = 'fas fa-chevron-down toggle-icon open';
                    localStorage.setItem('addFormOpen', true);
                }

                // Scroll to form
                document.querySelector('.add-product-section').scrollIntoView({ behavior: 'smooth' });

            } catch (error) {
                console.error('Error editing product:', error);
                showMessage('حدث خطأ أثناء تحميل بيانات المنتج', 'error');
            }
        }

        // Show delete confirmation modal
        function deleteProduct(productId, productName) {
            // Find the product to get full details
            const product = allProducts.find(p => p.id === productId);
            if (!product) {
                showMessage('المنتج غير موجود', 'error');
                return;
            }

            // Store product data for deletion
            window.productToDelete = product;

            // Update modal content
            document.getElementById('deleteProductName').textContent = product.name;
            document.getElementById('deleteProductCategory').textContent =
                allCategories.find(c => c.id === product.category_id)?.name || 'غير محدد';
            document.getElementById('deleteProductPrice').textContent = formatPrice(product.price);

            // Product status
            let status = [];
            if (product.is_active) status.push('نشط');
            if (product.is_featured) status.push('مميز');
            if (product.is_on_offer) status.push('في العروض');
            if (!product.is_available) status.push('غير متوفر');
            document.getElementById('deleteProductStatus').textContent =
                status.length > 0 ? status.join(', ') : 'عادي';

            // Show delete modal
            showDeleteModal();

            // Play warning sound (optional)
            try {
                // Create a simple warning beep sound
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.3);
            } catch (e) {
                // Fallback: try to play a simple beep
                try {
                    const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                    audio.volume = 0.2;
                    audio.play().catch(() => {}); // Ignore errors if audio fails
                } catch (e2) {}
            }
        }

        // Format price
        function formatPrice(price) {
            return new Intl.NumberFormat('ar-IQ', {
                style: 'decimal',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(price) + ' د.ع';
        }

        // Show delete modal
        function showDeleteModal() {
            const modal = document.getElementById('deleteModal');
            const modalContent = modal.querySelector('.delete-modal-content');

            modal.classList.add('show');
            document.body.style.overflow = 'hidden';

            // Add shake effect after a short delay
            setTimeout(() => {
                modalContent.classList.add('shake');
                setTimeout(() => {
                    modalContent.classList.remove('shake');
                }, 500);
            }, 200);

            // Add vibration for mobile devices (if supported)
            if (navigator.vibrate) {
                navigator.vibrate([100, 50, 100]);
            }
        }

        // Close delete modal
        function closeDeleteModal() {
            const modal = document.getElementById('deleteModal');
            modal.classList.remove('show');
            document.body.style.overflow = 'auto';
            window.productToDelete = null;

            // Reset delete button state
            const deleteBtn = document.getElementById('confirmDeleteBtn');
            deleteBtn.classList.remove('loading');
            deleteBtn.innerHTML = '<i class="fas fa-trash"></i> تأكيد الحذف';
        }

        // Confirm and execute delete
        async function confirmDelete() {
            if (!window.productToDelete) return;

            const deleteBtn = document.getElementById('confirmDeleteBtn');
            const product = window.productToDelete;

            // Show loading state
            deleteBtn.classList.add('loading');
            deleteBtn.innerHTML = 'جاري الحذف...';

            try {
                const { error } = await supabase
                    .from('products')
                    .delete()
                    .eq('id', product.id);

                if (error) throw error;

                // Close delete modal
                closeDeleteModal();

                // Show success message
                showMessage(`تم حذف المنتج "${product.name}" بنجاح!`, 'success');

                // Update the products list and stats
                loadProducts();
                loadProductsStats();

            } catch (error) {
                console.error('Error deleting product:', error);

                // Reset button state
                deleteBtn.classList.remove('loading');
                deleteBtn.innerHTML = '<i class="fas fa-trash"></i> تأكيد الحذف';

                // Show error message
                const errorMessage = error.message || 'حدث خطأ أثناء حذف المنتج. يرجى المحاولة مرة أخرى.';
                showMessage(errorMessage, 'error');
            }
        }

        // Refresh products function
        function refreshProducts() {
            loadProducts();
            loadProductsStats();
            showEnhancedSuccessNotification(
                'تم تحديث البيانات بنجاح!',
                'تم تحديث قائمة المنتجات والإحصائيات بنجاح'
            );
        }

        // Refresh products list (alias for refreshProducts)
        function refreshProductsList() {
            refreshProducts();
        }

        // Export function removed as requested

        // Grid view functions removed since only table view is used

        // Update results count
        function updateResultsCount(count, total) {
            const resultsCount = document.getElementById('resultsCount');
            const filterInfo = document.getElementById('filterInfo');

            if (resultsCount) {
                resultsCount.textContent = count || 0;
            }

            if (filterInfo && count !== total) {
                filterInfo.textContent = ` (من أصل ${total})`;
            } else if (filterInfo) {
                filterInfo.textContent = '';
            }
        }

        // Show/hide advanced filters
        function showAdvancedFilters() {
            const advancedFilters = document.getElementById('advancedFilters');
            const button = event.target.closest('button');

            if (advancedFilters.style.display === 'none' || !advancedFilters.style.display) {
                advancedFilters.style.display = 'block';
                button.innerHTML = '<i class="fas fa-sliders-h"></i> إخفاء الفلاتر المتقدمة';
                button.classList.add('active');
            } else {
                advancedFilters.style.display = 'none';
                button.innerHTML = '<i class="fas fa-sliders-h"></i> فلاتر متقدمة';
                button.classList.remove('active');
            }
        }

        // Clear all filters
        function clearAllFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('minPrice').value = '';
            document.getElementById('maxPrice').value = '';
            document.getElementById('productTypeFilter').value = '';
            document.getElementById('availability').value = '';

            // Hide clear search button
            document.querySelector('.clear-search').style.display = 'none';

            // Reset to show all products
            displayProducts(allProducts);

            showMessage('تم مسح جميع الفلاتر', 'success');
        }

        // Clear search
        function clearSearch() {
            document.getElementById('searchInput').value = '';
            document.querySelector('.clear-search').style.display = 'none';
            filterProducts();
        }

        // Sort products
        function sortProducts() {
            const sortValue = document.getElementById('sortFilter').value;
            let sortedProducts = [...allProducts];

            switch(sortValue) {
                case 'name_asc':
                    sortedProducts.sort((a, b) => a.name.localeCompare(b.name, 'ar'));
                    break;
                case 'name_desc':
                    sortedProducts.sort((a, b) => b.name.localeCompare(a.name, 'ar'));
                    break;
                case 'price_asc':
                    sortedProducts.sort((a, b) => (a.offer_price || a.price) - (b.offer_price || b.price));
                    break;
                case 'price_desc':
                    sortedProducts.sort((a, b) => (b.offer_price || b.price) - (a.offer_price || a.price));
                    break;
                case 'date_desc':
                    sortedProducts.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
                    break;
                case 'date_asc':
                    sortedProducts.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
                    break;
            }

            allProducts = sortedProducts;
            filterProducts(); // Apply current filters to sorted products
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuth()) {
                loadCategories();
                loadProducts();
                loadProductsStats();

                // Restore form state
                const formOpen = localStorage.getItem('addFormOpen') === 'true';
                const form = document.getElementById('addProductForm');
                const icon = document.getElementById('toggleIcon');

                console.log('Form element found:', !!form);
                console.log('Icon element found:', !!icon);
                console.log('Form should be open:', formOpen);

                if (form && icon) {
                    if (formOpen) {
                        form.classList.add('open');
                        icon.classList.add('open');
                        icon.className = 'fas fa-chevron-down toggle-icon open';
                        console.log('Form initialized as open');
                    } else {
                        form.classList.remove('open');
                        icon.classList.remove('open');
                        icon.className = 'fas fa-chevron-right toggle-icon';
                        console.log('Form initialized as closed');
                    }
                } else {
                    console.error('Form or icon element not found during initialization');
                }

                // View preference code removed since only table view is used

                // Event listeners
                document.getElementById('productForm').addEventListener('submit', handleFormSubmit);
                document.getElementById('searchInput').addEventListener('input', filterProducts);
                document.getElementById('categoryFilter').addEventListener('change', filterProducts);
                document.getElementById('statusFilter').addEventListener('change', filterProducts);

                // Add click event listener to the add product button for debugging
                const addProductBtn = document.querySelector('button[onclick="toggleAddForm()"]');
                if (addProductBtn) {
                    console.log('Add product button found');
                    addProductBtn.addEventListener('click', function(e) {
                        console.log('Add product button clicked');
                        e.preventDefault();
                        toggleAddForm();
                    });
                } else {
                    console.error('Add product button not found');
                }

                // Auto-check offer checkbox when offer price is entered
                function checkOfferStatus() {
                    const offerPriceInput = document.getElementById('productOfferPrice');
                    const regularPriceInput = document.getElementById('productPrice');
                    const isOnOfferCheckbox = document.getElementById('isOnOffer');

                    const offerPrice = parseFloat(offerPriceInput.value);
                    const regularPrice = parseFloat(regularPriceInput.value);

                    // Reset border colors
                    offerPriceInput.style.borderColor = '#ddd';
                    regularPriceInput.style.borderColor = '#ddd';

                    if (offerPrice && regularPrice) {
                        if (offerPrice < regularPrice && offerPrice > 0) {
                            isOnOfferCheckbox.checked = true;
                            offerPriceInput.style.borderColor = '#27ae60'; // Green for valid offer
                        } else if (offerPrice >= regularPrice) {
                            isOnOfferCheckbox.checked = false;
                            offerPriceInput.style.borderColor = '#e74c3c'; // Red for invalid offer
                        } else {
                            isOnOfferCheckbox.checked = false;
                        }
                    } else if (!offerPrice || offerPrice <= 0) {
                        isOnOfferCheckbox.checked = false;
                    }
                }

                // Add price validation
                function validatePrices() {
                    const offerPrice = parseFloat(document.getElementById('productOfferPrice').value);
                    const regularPrice = parseFloat(document.getElementById('productPrice').value);

                    if (offerPrice && regularPrice && offerPrice >= regularPrice) {
                        showMessage('سعر العرض يجب أن يكون أقل من السعر الأساسي', 'error');
                        return false;
                    }
                    return true;
                }

                document.getElementById('productOfferPrice').addEventListener('input', checkOfferStatus);
                document.getElementById('productPrice').addEventListener('input', checkOfferStatus);
                document.getElementById('productOfferPrice').addEventListener('blur', validatePrices);

                // Format numbers on input
                function formatNumberInput(input) {
                    input.addEventListener('input', function() {
                        // Allow only numbers and decimal point
                        this.value = this.value.replace(/[^0-9.]/g, '');

                        // Ensure only one decimal point
                        const parts = this.value.split('.');
                        if (parts.length > 2) {
                            this.value = parts[0] + '.' + parts.slice(1).join('');
                        }
                    });
                }

                formatNumberInput(document.getElementById('productPrice'));
                formatNumberInput(document.getElementById('productOfferPrice'));

                // Delete modal event listeners
                const deleteModal = document.getElementById('deleteModal');

                // Close delete modal when clicking outside
                deleteModal.addEventListener('click', function(e) {
                    if (e.target === deleteModal) {
                        closeDeleteModal();
                    }
                });

                // Close delete modal with Escape key
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && deleteModal.classList.contains('show')) {
                        closeDeleteModal();
                    }
                });
            }
        });
    </script>

    <!-- Legacy mobile sidebar code removed - now using unified-sidebar-manager.js -->

    <!-- Unified Sidebar System -->
    <script src="js/unified-sidebar-manager.js"></script>

    <!-- Unified Notification System -->
    <script src="js/unified-notification-system.js"></script>

    <!-- Professional Logout System -->
    <script src="js/logout-system.js"></script>
</body>
</html>