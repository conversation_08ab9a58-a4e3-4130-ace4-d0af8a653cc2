/**
 * Shared Supabase Configuration
 * This file provides a singleton pattern for Supabase client initialization
 * to prevent multiple declarations and GoTrueClient warnings
 */

// Global configuration object to prevent duplicate declarations
if (!window.SupabaseConfig) {
    window.SupabaseConfig = {
        // Supabase configuration constants
        SUPABASE_URL: 'https://krqijjttwllohulmdwgs.supabase.co',
        SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70',
        
        // Global Supabase client instance
        globalClient: null,
        
        // Initialization flag
        initialized: false,
        
        /**
         * Get or create Supabase client using singleton pattern
         * @returns {Object|null} Supabase client instance or null if not available
         */
        getClient: function() {
            // Return existing client if available
            if (this.globalClient) {
                return this.globalClient;
            }
            
            // Check if Supabase library is loaded
            if (typeof window !== 'undefined' && window.supabase && typeof window.supabase.createClient === 'function') {
                try {
                    // Create client with proper configuration
                    this.globalClient = window.supabase.createClient(this.SUPABASE_URL, this.SUPABASE_ANON_KEY, {
                        global: {
                            headers: {
                                'Accept': 'application/json',
                                'Content-Type': 'application/json'
                            }
                        }
                    });
                    
                    this.initialized = true;
                    
                    // Store globally for backward compatibility
                    window.globalSupabaseClient = this.globalClient;
                    
                    console.log('✅ Supabase client initialized successfully (singleton pattern)');
                    return this.globalClient;
                } catch (error) {
                    console.error('❌ Failed to create Supabase client:', error);
                    return null;
                }
            }
            
            console.warn('⚠️ Supabase library not loaded yet');
            return null;
        },
        
        /**
         * Initialize client if not already initialized
         * @returns {boolean} True if successful, false otherwise
         */
        init: function() {
            if (this.initialized && this.globalClient) {
                return true;
            }
            
            const client = this.getClient();
            return client !== null;
        },
        
        /**
         * Reset the client (useful for testing or re-initialization)
         */
        reset: function() {
            this.globalClient = null;
            this.initialized = false;
            if (window.globalSupabaseClient) {
                delete window.globalSupabaseClient;
            }
            console.log('🔄 Supabase client reset');
        }
    };
}

// Backward compatibility: Expose constants globally if not already defined
if (typeof SUPABASE_URL === 'undefined') {
    window.SUPABASE_URL = window.SupabaseConfig.SUPABASE_URL;
}

if (typeof SUPABASE_ANON_KEY === 'undefined') {
    window.SUPABASE_ANON_KEY = window.SupabaseConfig.SUPABASE_ANON_KEY;
}

// Convenience function for getting Supabase client
window.getSupabaseClient = function() {
    return window.SupabaseConfig.getClient();
};

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        window.SupabaseConfig.init();
    });
} else {
    // DOM is already ready
    window.SupabaseConfig.init();
}
