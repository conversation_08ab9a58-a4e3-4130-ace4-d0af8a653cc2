-- إنشاء جداول إدارة سلة التسوق
-- Shopping Cart Management Tables Creation

-- 1. جدول أكواد الخصم (Discount Codes)
CREATE TABLE IF NOT EXISTS discount_codes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    code_name TEXT NOT NULL UNIQUE,
    discount_type TEXT NOT NULL CHECK (discount_type IN ('percentage', 'fixed')),
    discount_value DECIMAL(10,2) NOT NULL CHECK (discount_value > 0),
    currency TEXT DEFAULT 'IQD',
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    usage_count INTEGER DEFAULT 0,
    max_usage INTEGER DEFAULT NULL,
    min_order_amount DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT valid_date_range CHECK (end_date >= start_date)
);

-- 2. جدول أسعار التوصيل للمحافظات (Delivery Pricing)
CREATE TABLE IF NOT EXISTS delivery_pricing (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    governorate_name_ar TEXT NOT NULL UNIQUE,
    governorate_name_en TEXT,
    delivery_price DECIMAL(10,2) DEFAULT 0,
    currency TEXT DEFAULT 'IQD',
    is_free_delivery BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. جدول استخدام أكواد الخصم (Discount Code Usage Log)
CREATE TABLE IF NOT EXISTS discount_code_usage (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    discount_code_id UUID REFERENCES discount_codes(id) ON DELETE CASCADE,
    order_id UUID,
    customer_phone TEXT,
    customer_name TEXT,
    discount_amount DECIMAL(10,2) NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_discount_codes_name ON discount_codes(code_name);
CREATE INDEX IF NOT EXISTS idx_discount_codes_active ON discount_codes(is_active);
CREATE INDEX IF NOT EXISTS idx_discount_codes_dates ON discount_codes(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_delivery_pricing_governorate ON delivery_pricing(governorate_name_ar);
CREATE INDEX IF NOT EXISTS idx_delivery_pricing_active ON delivery_pricing(is_active);
CREATE INDEX IF NOT EXISTS idx_discount_usage_code ON discount_code_usage(discount_code_id);
CREATE INDEX IF NOT EXISTS idx_discount_usage_order ON discount_code_usage(order_id);

-- إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إنشاء المحفزات (Triggers) لتحديث updated_at
CREATE TRIGGER update_discount_codes_updated_at 
    BEFORE UPDATE ON discount_codes 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_delivery_pricing_updated_at 
    BEFORE UPDATE ON delivery_pricing 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إدراج البيانات الأولية لأسعار التوصيل للمحافظات العراقية
INSERT INTO delivery_pricing (governorate_name_ar, governorate_name_en, delivery_price, is_free_delivery) VALUES
('بغداد', 'Baghdad', 5000, false),
('الأنبار', 'Al Anbar', 10000, false),
('بابل', 'Babylon', 8000, false),
('البصرة', 'Basra', 12000, false),
('دهوك', 'Dohuk', 15000, false),
('ديالى', 'Diyala', 9000, false),
('ذي قار', 'Dhi Qar', 11000, false),
('أربيل', 'Erbil', 14000, false),
('القادسية (الديوانية)', 'Al-Qadisiyyah (Diwaniyah)', 10000, false),
('السليمانية', 'Sulaymaniyah', 13000, false),
('صلاح الدين', 'Salah al-Din', 9500, false),
('حلبجة', 'Halabja', 14500, false),
('كربلاء', 'Karbala', 7500, false),
('كركوك', 'Kirkuk', 11500, false),
('ميسان', 'Maysan', 12500, false),
('المثنى', 'Al Muthanna', 13500, false),
('النجف', 'Najaf', 8500, false),
('نينوى', 'Nineveh', 12000, false),
('واسط', 'Wasit', 9500, false)
ON CONFLICT (governorate_name_ar) DO NOTHING;

-- إدراج بعض أكواد الخصم التجريبية
INSERT INTO discount_codes (code_name, discount_type, discount_value, start_date, end_date, is_active) VALUES
('WELCOME10', 'percentage', 10.00, CURRENT_DATE, CURRENT_DATE + INTERVAL '30 days', true),
('SAVE5000', 'fixed', 5000.00, CURRENT_DATE, CURRENT_DATE + INTERVAL '15 days', true),
('NEWCUSTOMER', 'percentage', 15.00, CURRENT_DATE, CURRENT_DATE + INTERVAL '60 days', false)
ON CONFLICT (code_name) DO NOTHING;

-- إنشاء دالة للتحقق من صحة كود الخصم
CREATE OR REPLACE FUNCTION validate_discount_code(code_name_input TEXT)
RETURNS TABLE (
    is_valid BOOLEAN,
    discount_type TEXT,
    discount_value DECIMAL,
    message TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        CASE 
            WHEN dc.id IS NULL THEN false
            WHEN NOT dc.is_active THEN false
            WHEN CURRENT_DATE < dc.start_date THEN false
            WHEN CURRENT_DATE > dc.end_date THEN false
            WHEN dc.max_usage IS NOT NULL AND dc.usage_count >= dc.max_usage THEN false
            ELSE true
        END as is_valid,
        dc.discount_type,
        dc.discount_value,
        CASE 
            WHEN dc.id IS NULL THEN 'كود الخصم غير موجود'
            WHEN NOT dc.is_active THEN 'كود الخصم غير مفعل'
            WHEN CURRENT_DATE < dc.start_date THEN 'كود الخصم لم يبدأ بعد'
            WHEN CURRENT_DATE > dc.end_date THEN 'كود الخصم منتهي الصلاحية'
            WHEN dc.max_usage IS NOT NULL AND dc.usage_count >= dc.max_usage THEN 'تم استنفاد عدد مرات الاستخدام'
            ELSE 'كود الخصم صالح'
        END as message
    FROM discount_codes dc
    WHERE dc.code_name = code_name_input;
    
    -- If no record found, return invalid result
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, ''::TEXT, 0::DECIMAL, 'كود الخصم غير موجود'::TEXT;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة لحساب سعر التوصيل
CREATE OR REPLACE FUNCTION get_delivery_price(governorate_name TEXT)
RETURNS DECIMAL AS $$
DECLARE
    delivery_cost DECIMAL := 0;
BEGIN
    SELECT 
        CASE 
            WHEN dp.is_free_delivery THEN 0
            ELSE dp.delivery_price
        END
    INTO delivery_cost
    FROM delivery_pricing dp
    WHERE dp.governorate_name_ar = governorate_name 
    AND dp.is_active = true;
    
    -- If governorate not found, return default price
    IF delivery_cost IS NULL THEN
        delivery_cost := 10000; -- Default delivery price
    END IF;
    
    RETURN delivery_cost;
END;
$$ LANGUAGE plpgsql;

-- منح الصلاحيات المناسبة
GRANT ALL PRIVILEGES ON discount_codes TO anon, authenticated;
GRANT ALL PRIVILEGES ON delivery_pricing TO anon, authenticated;
GRANT ALL PRIVILEGES ON discount_code_usage TO anon, authenticated;

-- تمكين Row Level Security (RLS) للأمان
ALTER TABLE discount_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE delivery_pricing ENABLE ROW LEVEL SECURITY;
ALTER TABLE discount_code_usage ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسات الأمان (Security Policies)
-- السماح بالقراءة للجميع
CREATE POLICY "Allow read access for discount codes" ON discount_codes FOR SELECT USING (true);
CREATE POLICY "Allow read access for delivery pricing" ON delivery_pricing FOR SELECT USING (true);

-- السماح بالكتابة للمدراء فقط (يمكن تخصيصها حسب نظام المصادقة)
CREATE POLICY "Allow admin access for discount codes" ON discount_codes FOR ALL USING (true);
CREATE POLICY "Allow admin access for delivery pricing" ON delivery_pricing FOR ALL USING (true);
CREATE POLICY "Allow admin access for discount usage" ON discount_code_usage FOR ALL USING (true);
