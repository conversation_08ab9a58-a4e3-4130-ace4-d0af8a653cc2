/**
 * Professional Logout System for Care Admin Dashboard
 * نظام تسجيل الخروج الاحترافي للوحة التحكم الإدارية
 */

// Professional logout function with custom modal
function logout() {
    showLogoutModal();
}

// Show logout confirmation modal
function showLogoutModal() {
    // Play notification sound
    playNotificationSound();

    // Create modal overlay
    const overlay = document.createElement('div');
    overlay.className = 'logout-modal-overlay';
    overlay.id = 'logoutModalOverlay';

    // Get current user info
    const adminUser = sessionStorage.getItem('adminUser');
    let userName = 'المدير';
    let loginTime = '';
    
    if (adminUser) {
        try {
            const user = JSON.parse(adminUser);
            userName = user.username || 'المدير';
            if (user.loginTime) {
                const loginDate = new Date(user.loginTime);
                loginTime = loginDate.toLocaleDateString('ar-EG') + ' ' + loginDate.toLocaleTimeString('ar-EG');
            }
        } catch (error) {
            console.error('Error parsing user data:', error);
        }
    }

    // Create modal content
    overlay.innerHTML = `
        <div class="logout-modal">
            <div class="logout-modal-header">
                <button class="logout-modal-close" onclick="closeLogoutModal(false)">
                    <i class="fas fa-times"></i>
                </button>
                <div class="logout-modal-icon">
                    <i class="fas fa-sign-out-alt"></i>
                </div>
                <h3 class="logout-modal-title">تسجيل الخروج</h3>
            </div>
            <div class="logout-modal-body">
                <p class="logout-modal-message">
                    مرحباً <strong>${userName}</strong>، هل أنت متأكد من رغبتك في تسجيل الخروج من لوحة التحكم؟
                </p>
                
                <div class="logout-modal-info">
                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                        <i class="fas fa-user" style="color: #4a90a4;"></i>
                        <strong>المستخدم:</strong> ${userName}
                    </div>
                    ${loginTime ? `
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-clock" style="color: #4a90a4;"></i>
                        <strong>وقت تسجيل الدخول:</strong> ${loginTime}
                    </div>
                    ` : ''}
                </div>

                <div class="logout-modal-warning">
                    <i class="fas fa-info-circle"></i>
                    <strong>ملاحظة:</strong> سيتم إنهاء جلستك الحالية وستحتاج إلى تسجيل الدخول مرة أخرى للوصول إلى لوحة التحكم.
                </div>

                <div style="margin-top: 1rem; padding: 0.8rem; background: #f8f9fa; border-radius: 8px; font-size: 0.9rem; color: #666;">
                    <i class="fas fa-keyboard"></i>
                    <strong>اختصارات لوحة المفاتيح:</strong>
                    <br>• Enter: تأكيد تسجيل الخروج
                    <br>• Escape: إلغاء والبقاء في لوحة التحكم
                </div>
            </div>
            <div class="logout-modal-footer">
                <button class="logout-modal-btn logout-modal-btn-cancel" onclick="closeLogoutModal(false)" title="اضغط Escape للإلغاء">
                    <i class="fas fa-times"></i>
                    البقاء في لوحة التحكم
                </button>
                <button class="logout-modal-btn logout-modal-btn-confirm" onclick="confirmLogout()" title="اضغط Enter للتأكيد">
                    <i class="fas fa-sign-out-alt"></i>
                    تأكيد تسجيل الخروج
                </button>
            </div>
        </div>
    `;

    // Add to body
    document.body.appendChild(overlay);

    // Prevent body scroll
    document.body.style.overflow = 'hidden';

    // Show modal with animation
    setTimeout(() => {
        overlay.classList.add('show');
    }, 10);

    // Handle keyboard shortcuts
    const handleKeyboard = (e) => {
        if (e.key === 'Escape') {
            closeLogoutModal(false);
            document.removeEventListener('keydown', handleKeyboard);
        } else if (e.key === 'Enter') {
            e.preventDefault();
            confirmLogout();
            document.removeEventListener('keydown', handleKeyboard);
        } else if (e.key === 'Tab') {
            // Trap focus within modal
            const focusableElements = overlay.querySelectorAll('button');
            const firstElement = focusableElements[0];
            const lastElement = focusableElements[focusableElements.length - 1];
            
            if (e.shiftKey && document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
            } else if (!e.shiftKey && document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    };
    document.addEventListener('keydown', handleKeyboard);
    
    // Focus the cancel button initially
    setTimeout(() => {
        const cancelBtn = overlay.querySelector('.logout-modal-btn-cancel');
        if (cancelBtn) cancelBtn.focus();
    }, 100);

    // Handle click outside
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            closeLogoutModal(false);
        }
    });
}

// Close logout modal
function closeLogoutModal(confirmed) {
    const overlay = document.getElementById('logoutModalOverlay');
    if (overlay) {
        overlay.classList.remove('show');
        document.body.style.overflow = '';
        
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 300);
    }
}

// Confirm logout
async function confirmLogout() {
    const confirmBtn = document.querySelector('.logout-modal-btn-confirm');
    if (confirmBtn) {
        confirmBtn.classList.add('loading');
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الخروج...';
    }

    try {
        // Log the logout activity if logActivity function exists
        if (typeof logActivity === 'function') {
            await logActivity('logout', 'admin_session', null, { 
                action: 'user_logout',
                logout_time: new Date().toISOString()
            });
        }
    } catch (error) {
        console.warn('Could not log logout activity:', error);
    }

    // Simulate logout process
    setTimeout(() => {
        // Clear session data
        sessionStorage.removeItem('adminUser');
        
        // Play success sound
        playSuccessSound();
        
        // Redirect to login page
        window.location.href = 'login.html';
    }, 1000);
}

// Play notification sound
function playNotificationSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
        
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.2);
    } catch (error) {
        console.log('Could not play notification sound:', error);
    }
}

// Play success sound
function playSuccessSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(523, audioContext.currentTime);
        oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.1);
        oscillator.frequency.setValueAtTime(784, audioContext.currentTime + 0.2);
        
        gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.3);
    } catch (error) {
        console.log('Could not play success sound:', error);
    }
}
