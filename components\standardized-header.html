<!-- Standardized Responsive Header Component -->
<!-- CSS Styles for Header -->
<style>
/* Standardized Header Design - Clean and Simple */
header {
    background: #121414;
    color: white;
    padding: 1.5rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.logo {
    font-size: 2.5rem;
    font-weight: 700;
    text-decoration: none;
    color: white;
    letter-spacing: 1px;
    z-index: 1002;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 1rem;
    margin: 0;
    padding: 0;
}

.nav-menu li a {
    color: white;
    text-decoration: none;
    padding: 0.8rem 1.2rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-menu li a:hover,
.nav-menu li a.active {
    background: var(--primary-color);
    color: white;
}



/* Cart Icon */
.cart-icon {
    position: relative;
    cursor: pointer;
    padding: 0.8rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    color: white;
    z-index: 1002;
}

.cart-icon:hover {
    background: rgba(255,255,255,0.1);
}

/* Desktop Header Styles (Base Styles) */
header {
    padding: 1.5rem 0;
}

.logo {
    font-size: 2.5rem;
}

.nav-menu {
    gap: 1rem;
}

.nav-menu li a {
    padding: 0.8rem 1.2rem;
    font-size: 1rem;
}

/* Cart Icon */
.cart-icon {
    position: relative;
    cursor: pointer;
    padding: 0.8rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    color: white;
}

.cart-icon:hover {
    background: rgba(255,255,255,0.1);
}

.cart-icon i {
    font-size: 1.5rem;
}

.cart-count {
    position: absolute;
    top: 0;
    right: 0;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
}
</style>

<!-- HTML Structure for Header -->
<header>
    <div class="container">
        <div class="header-content">
            <a href="index.html" class="logo business-name">Care</a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="index.html">الرئيسية</a></li>
                    <li><a href="products.html">المنتجات</a></li>
                    <li><a href="offers.html">العروض</a></li>
                    <li><a href="guidelines.html">الإرشادات</a></li>
                    <li><a href="faq.html">الأسئلة الشائعة</a></li>
                    <li><a href="contact.html">اتصل بنا</a></li>
                </ul>
            </nav>
            <div class="cart-icon" onclick="window.location.href='cart.html'">
                <i class="fas fa-shopping-cart"></i>
                <span class="cart-count" id="cartCount">0</span>
            </div>
        </div>
    </div>
</header>
