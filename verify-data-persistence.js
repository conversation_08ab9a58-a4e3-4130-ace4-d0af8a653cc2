// Verification script for data persistence fix
// This script can be run in the browser console to verify the fixes

console.log('🔍 Data Persistence Verification Script');
console.log('=====================================');

// Function to check if elements exist and have values
function checkDataPersistence() {
    console.log('\n🧪 Checking data persistence...');
    
    // Check Features Section main fields
    console.log('\n📋 Features Section Main Fields:');
    const featuresBadge = document.getElementById('featuresBadge');
    const featuresTitle = document.getElementById('featuresTitle');
    const featuresSubtitle = document.getElementById('featuresSubtitle');
    
    console.log('- Badge:', {
        element: !!featuresBadge,
        value: featuresBadge?.value || 'EMPTY',
        hasPlaceholder: !!featuresBadge?.placeholder
    });
    
    console.log('- Title:', {
        element: !!featuresTitle,
        value: featuresTitle?.value || 'EMPTY',
        hasPlaceholder: !!featuresTitle?.placeholder
    });
    
    console.log('- Subtitle:', {
        element: !!featuresSubtitle,
        value: featuresSubtitle?.value || 'EMPTY',
        hasPlaceholder: !!featuresSubtitle?.placeholder
    });
    
    // Check About Us Section fields
    console.log('\n📖 About Us Section Fields:');
    const aboutBadge = document.getElementById('aboutBadge');
    const aboutTitle = document.getElementById('aboutTitle');
    const aboutSubtitle = document.getElementById('aboutSubtitle');
    const aboutDescription1 = document.getElementById('aboutDescription1');
    const aboutDescription2 = document.getElementById('aboutDescription2');
    
    console.log('- Badge:', {
        element: !!aboutBadge,
        value: aboutBadge?.value || 'EMPTY',
        hasPlaceholder: !!aboutBadge?.placeholder
    });
    
    console.log('- Title:', {
        element: !!aboutTitle,
        value: aboutTitle?.value || 'EMPTY',
        hasPlaceholder: !!aboutTitle?.placeholder
    });
    
    console.log('- Subtitle:', {
        element: !!aboutSubtitle,
        value: aboutSubtitle?.value || 'EMPTY',
        hasPlaceholder: !!aboutSubtitle?.placeholder
    });
    
    console.log('- Description 1:', {
        element: !!aboutDescription1,
        value: aboutDescription1?.value ? aboutDescription1.value.substring(0, 50) + '...' : 'EMPTY',
        hasPlaceholder: !!aboutDescription1?.placeholder
    });
    
    console.log('- Description 2:', {
        element: !!aboutDescription2,
        value: aboutDescription2?.value ? aboutDescription2.value.substring(0, 50) + '...' : 'EMPTY',
        hasPlaceholder: !!aboutDescription2?.placeholder
    });
    
    // Check individual features (1-4)
    console.log('\n🔧 Individual Features (1-4):');
    for (let i = 1; i <= 4; i++) {
        const titleField = document.getElementById(`feature${i}Title`);
        const descField = document.getElementById(`feature${i}Description`);
        const iconField = document.getElementById(`feature${i}Icon`);
        
        console.log(`- Feature ${i}:`, {
            title: titleField?.value || 'EMPTY',
            description: descField?.value ? descField.value.substring(0, 30) + '...' : 'EMPTY',
            icon: iconField?.value || 'EMPTY'
        });
    }
    
    // Summary
    const featuresMainEmpty = !featuresBadge?.value && !featuresTitle?.value && !featuresSubtitle?.value;
    const aboutMainEmpty = !aboutBadge?.value && !aboutTitle?.value && !aboutSubtitle?.value;
    
    console.log('\n📊 Summary:');
    console.log('- Features main fields populated:', !featuresMainEmpty ? '✅' : '❌');
    console.log('- About main fields populated:', !aboutMainEmpty ? '✅' : '❌');
    
    if (featuresMainEmpty || aboutMainEmpty) {
        console.log('\n⚠️ Some fields are empty. This could indicate:');
        console.log('1. Data not loaded from database');
        console.log('2. Form population timing issue');
        console.log('3. Element IDs not matching');
        console.log('\n💡 Try running: manualPopulate() or testDataPersistence()');
    } else {
        console.log('\n✅ Data persistence appears to be working correctly!');
    }
}

// Function to check current settings object
function checkCurrentSettings() {
    console.log('\n📋 Current Settings Object:');
    if (typeof currentSettings === 'undefined') {
        console.log('❌ currentSettings is not defined');
        return;
    }
    
    console.log('- Total settings:', Object.keys(currentSettings).length);
    
    const featureKeys = Object.keys(currentSettings).filter(key => key.startsWith('feature_'));
    const aboutKeys = Object.keys(currentSettings).filter(key => key.startsWith('about_'));
    
    console.log('- Feature keys:', featureKeys.length, featureKeys);
    console.log('- About keys:', aboutKeys.length, aboutKeys);
    
    // Check specific values
    console.log('\n🔍 Specific Values:');
    console.log('- features_badge:', currentSettings.features_badge || 'NOT SET');
    console.log('- features_title:', currentSettings.features_title || 'NOT SET');
    console.log('- features_subtitle:', currentSettings.features_subtitle || 'NOT SET');
    console.log('- about_badge:', currentSettings.about_badge || 'NOT SET');
    console.log('- about_title:', currentSettings.about_title || 'NOT SET');
    console.log('- about_subtitle:', currentSettings.about_subtitle || 'NOT SET');
}

// Function to run full verification
function runFullVerification() {
    console.log('🚀 Running Full Data Persistence Verification');
    console.log('==============================================');
    
    checkCurrentSettings();
    checkDataPersistence();
    
    console.log('\n🔧 Available Test Functions:');
    console.log('- checkDataPersistence() - Check form field values');
    console.log('- checkCurrentSettings() - Check settings object');
    console.log('- testDataPersistence() - Run built-in test');
    console.log('- manualPopulate() - Manually populate forms');
    console.log('- loadSettings() - Reload settings from database');
}

// Auto-run verification
setTimeout(() => {
    runFullVerification();
}, 1000);

// Export functions to global scope
window.checkDataPersistence = checkDataPersistence;
window.checkCurrentSettings = checkCurrentSettings;
window.runFullVerification = runFullVerification;

console.log('✅ Verification script loaded. Run runFullVerification() to test.');
