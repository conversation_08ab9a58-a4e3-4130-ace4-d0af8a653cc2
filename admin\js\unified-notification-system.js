/**
 * Unified Professional Notification System for Care Admin Dashboard
 * نظام الإشعارات الموحد الاحترافي للوحة التحكم الإدارية
 * 
 * Features:
 * - Professional design with modern animations
 * - RTL Arabic layout support
 * - Multiple notification types (success, error, warning, info)
 * - Auto-dismiss with progress indicator
 * - Keyboard accessibility
 * - Responsive design
 * - Consistent styling across all admin pages
 */

class UnifiedNotificationSystem {
    constructor() {
        this.notifications = [];
        this.maxNotifications = 3;
        this.defaultDuration = 5000;
        this.init();
    }

    init() {
        this.injectStyles();
        this.setupKeyboardHandlers();
    }

    // Inject unified notification styles
    injectStyles() {
        if (document.getElementById('unified-notification-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'unified-notification-styles';
        styles.textContent = `
            /* Unified Notification System Styles */
            .unified-notification {
                position: fixed;
                top: 20px;
                right: 20px;
                min-width: 350px;
                max-width: 450px;
                background: var(--color-bg-primary);
                border-radius: var(--radius-lg);
                box-shadow: var(--shadow-xl);
                z-index: 10000;
                transform: translateX(120%) scale(0.8);
                opacity: 0;
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                overflow: hidden;
                font-family: 'Cairo', sans-serif;
                direction: rtl;
                border: 1px solid var(--color-bg-tertiary);
            }

            .unified-notification.show {
                transform: translateX(0) scale(1);
                opacity: 1;
            }

            .unified-notification.hide {
                transform: translateX(120%) scale(0.8);
                opacity: 0;
            }

            .notification-content {
                display: flex;
                align-items: flex-start;
                gap: var(--spacing-md);
                padding: var(--spacing-lg);
                position: relative;
            }

            .notification-icon {
                width: 40px;
                height: 40px;
                border-radius: var(--radius-full);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.2rem;
                color: white;
                flex-shrink: 0;
            }

            .notification-icon.success {
                background: var(--gradient-success);
            }

            .notification-icon.error {
                background: var(--gradient-danger);
            }

            .notification-icon.warning {
                background: var(--gradient-warning);
            }

            .notification-icon.info {
                background: var(--gradient-info);
            }

            .notification-text {
                flex: 1;
                min-width: 0;
            }

            .notification-title {
                font-size: var(--font-size-lg);
                font-weight: var(--font-weight-bold);
                color: var(--color-text-primary);
                margin-bottom: var(--spacing-xs);
                line-height: var(--line-height-tight);
            }

            .notification-message {
                font-size: var(--font-size-base);
                color: var(--color-text-secondary);
                line-height: var(--line-height-normal);
                word-wrap: break-word;
            }

            .notification-close {
                position: absolute;
                top: var(--spacing-sm);
                left: var(--spacing-sm);
                width: 32px;
                height: 32px;
                border: none;
                background: var(--color-bg-tertiary);
                border-radius: var(--radius-full);
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                color: var(--color-text-muted);
                transition: var(--transition-fast);
                font-size: 0.875rem;
            }

            .notification-close:hover {
                background: var(--color-bg-quaternary);
                color: var(--color-text-primary);
            }

            .notification-progress {
                position: absolute;
                bottom: 0;
                left: 0;
                height: 3px;
                background: var(--color-primary);
                border-radius: 0 0 var(--radius-lg) var(--radius-lg);
                transform-origin: left;
                animation: progressBar var(--duration, 5000ms) linear forwards;
            }

            .notification-progress.success {
                background: var(--color-success);
            }

            .notification-progress.error {
                background: var(--color-danger);
            }

            .notification-progress.warning {
                background: var(--color-warning);
            }

            .notification-progress.info {
                background: var(--color-info);
            }

            @keyframes progressBar {
                from {
                    transform: scaleX(1);
                }
                to {
                    transform: scaleX(0);
                }
            }

            /* Multiple notifications stacking */
            .unified-notification:nth-child(2) {
                top: 100px;
                transform: translateX(120%) scale(0.9);
            }

            .unified-notification:nth-child(3) {
                top: 180px;
                transform: translateX(120%) scale(0.85);
            }

            .unified-notification:nth-child(2).show {
                transform: translateX(0) scale(0.9);
            }

            .unified-notification:nth-child(3).show {
                transform: translateX(0) scale(0.85);
            }

            /* Responsive design */
            @media (max-width: 768px) {
                .unified-notification {
                    right: 10px;
                    left: 10px;
                    min-width: auto;
                    max-width: none;
                    width: calc(100% - 20px);
                }

                .notification-content {
                    padding: var(--spacing-md);
                }

                .notification-icon {
                    width: 36px;
                    height: 36px;
                    font-size: 1.1rem;
                }

                .notification-title {
                    font-size: var(--font-size-base);
                }

                .notification-message {
                    font-size: var(--font-size-sm);
                }
            }

            /* High contrast mode */
            @media (prefers-contrast: high) {
                .unified-notification {
                    border: 2px solid var(--color-text-primary);
                }

                .notification-close {
                    border: 1px solid var(--color-text-muted);
                }
            }

            /* Reduced motion */
            @media (prefers-reduced-motion: reduce) {
                .unified-notification {
                    transition: opacity 0.2s ease;
                }

                .notification-progress {
                    animation: none;
                }
            }
        `;

        document.head.appendChild(styles);
    }

    // Setup keyboard handlers
    setupKeyboardHandlers() {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.dismissAll();
            }
        });
    }

    // Show notification
    show(options = {}) {
        const {
            type = 'info',
            title = this.getDefaultTitle(type),
            message = '',
            duration = this.defaultDuration,
            dismissible = true
        } = options;

        // Remove oldest notification if at max capacity
        if (this.notifications.length >= this.maxNotifications) {
            this.dismiss(this.notifications[0].id);
        }

        const notification = this.createNotification({
            type,
            title,
            message,
            duration,
            dismissible
        });

        this.notifications.push(notification);
        document.body.appendChild(notification.element);

        // Trigger show animation
        setTimeout(() => {
            notification.element.classList.add('show');
        }, 10);

        // Auto dismiss
        if (duration > 0) {
            notification.timeoutId = setTimeout(() => {
                this.dismiss(notification.id);
            }, duration);
        }

        return notification.id;
    }

    // Create notification element
    createNotification({ type, title, message, duration, dismissible }) {
        const id = 'notification_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        const element = document.createElement('div');
        element.className = 'unified-notification';
        element.id = id;
        element.setAttribute('role', 'alert');
        element.setAttribute('aria-live', 'polite');

        const iconMap = {
            success: 'check',
            error: 'times',
            warning: 'exclamation-triangle',
            info: 'info'
        };

        element.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon ${type}">
                    <i class="fas fa-${iconMap[type]}"></i>
                </div>
                <div class="notification-text">
                    <div class="notification-title">${title}</div>
                    ${message ? `<div class="notification-message">${message}</div>` : ''}
                </div>
                ${dismissible ? `
                    <button class="notification-close" onclick="window.unifiedNotifications.dismiss('${id}')" aria-label="إغلاق الإشعار">
                        <i class="fas fa-times"></i>
                    </button>
                ` : ''}
                ${duration > 0 ? `
                    <div class="notification-progress ${type}" style="--duration: ${duration}ms"></div>
                ` : ''}
            </div>
        `;

        return {
            id,
            element,
            type,
            timeoutId: null
        };
    }

    // Get default title based on type
    getDefaultTitle(type) {
        const titles = {
            success: 'تم بنجاح!',
            error: 'حدث خطأ!',
            warning: 'تحذير!',
            info: 'معلومات'
        };
        return titles[type] || titles.info;
    }

    // Dismiss specific notification
    dismiss(id) {
        const notificationIndex = this.notifications.findIndex(n => n.id === id);
        if (notificationIndex === -1) return;

        const notification = this.notifications[notificationIndex];
        
        // Clear timeout
        if (notification.timeoutId) {
            clearTimeout(notification.timeoutId);
        }

        // Remove from array
        this.notifications.splice(notificationIndex, 1);

        // Animate out and remove
        notification.element.classList.add('hide');
        setTimeout(() => {
            if (notification.element.parentElement) {
                notification.element.remove();
            }
        }, 400);
    }

    // Dismiss all notifications
    dismissAll() {
        this.notifications.forEach(notification => {
            this.dismiss(notification.id);
        });
    }

    // Convenience methods
    success(title, message, duration) {
        return this.show({ type: 'success', title, message, duration });
    }

    error(title, message, duration) {
        return this.show({ type: 'error', title, message, duration });
    }

    warning(title, message, duration) {
        return this.show({ type: 'warning', title, message, duration });
    }

    info(title, message, duration) {
        return this.show({ type: 'info', title, message, duration });
    }
}

// Initialize global notification system
window.unifiedNotifications = new UnifiedNotificationSystem();

// Legacy compatibility functions
function showNotification(message, type = 'info') {
    window.unifiedNotifications.show({
        type,
        message,
        title: window.unifiedNotifications.getDefaultTitle(type)
    });
}

function showMessage(message, type = 'success') {
    window.unifiedNotifications.show({
        type,
        message,
        title: window.unifiedNotifications.getDefaultTitle(type)
    });
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UnifiedNotificationSystem;
}
