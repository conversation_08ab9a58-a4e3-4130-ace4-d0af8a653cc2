<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>المنتجات - Care</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/page-header-backgrounds.css" rel="stylesheet">
    <link href="css/standardized-typography.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <style>
        /* CSS Variables - Established Design System */
        :root {
            /* Spacing System */
            --spacing-xs: 0.25rem;    /* 4px */
            --spacing-sm: 0.5rem;     /* 8px */
            --spacing-md: 1rem;       /* 16px */
            --spacing-lg: 1.5rem;     /* 24px */
            --spacing-xl: 2rem;       /* 32px */
            --spacing-xxl: 3rem;      /* 48px */

            /* Color System */
            --primary-color: #4a90a4;
            --primary-dark: #2c3e50;
            --primary-light: #6ba4b8;
            --text-primary: #000000;
            --text-secondary: #666666;
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --border-color: #e9ecef;

            /* Typography */
            --font-family: 'Cairo', sans-serif;
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 2rem;
            --font-size-4xl: 2.5rem;
            --font-size-5xl: 3.5rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            direction: rtl;
            padding-top: 90px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 var(--spacing-xl);
            direction: rtl;
        }

        /* Standardized Header Design - Clean and Simple */
        header {
            background: #121414;
            color: white;
            padding: 1.5rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: var(--font-size-4xl);
            font-weight: 700;
            text-decoration: none;
            color: white;
            letter-spacing: 1px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1rem;
            margin: 0;
            padding: 0;
        }

        .nav-menu li a {
            color: white;
            text-decoration: none;
            padding: 0.8rem 1.2rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-menu li a:hover,
        .nav-menu li a.active {
            background: var(--primary-color);
            color: white;
        }

        /* Cart Icon */
        .cart-icon {
            position: relative;
            cursor: pointer;
            padding: 0.8rem;
            border-radius: 50%;
            transition: all 0.3s ease;
            color: white;
        }

        .cart-icon:hover {
            background: rgba(255,255,255,0.1);
        }

        .cart-icon i {
            font-size: var(--font-size-2xl);
        }

        .cart-count {
            position: absolute;
            top: 0;
            right: 0;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-xs);
            font-weight: 600;
        }

        /* Page Header styles moved to css/page-header-backgrounds.css for standardization */
        /*
        .page-header {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
            color: white;
            text-align: center;
            padding: 6rem 0 4rem 0;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .page-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
        }

        .page-header .container {
            position: relative;
            z-index: 2;
        }

        .page-header-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-xl);
        }

        /* Breadcrumb Navigation - RTL Arabic Compliant */
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: var(--font-size-base);
            color: rgba(255,255,255,0.8);
            margin-bottom: var(--spacing-lg);
            direction: rtl;
        }

        .breadcrumb a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
        }

        .breadcrumb a:hover {
            color: white;
            background: rgba(255,255,255,0.1);
        }

        .breadcrumb .separator {
            color: rgba(255,255,255,0.6);
            margin: 0 0.25rem;
            font-size: var(--font-size-sm);
        }

        .breadcrumb .current {
            color: white;
            font-weight: 600;
        }

        .page-header h1 {
            font-size: var(--font-size-5xl);
            margin-bottom: var(--spacing-xl);
            color: white;
            font-weight: 700;
            position: relative;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .page-header h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 4px;
            background: linear-gradient(90deg, transparent, white, transparent);
            border-radius: 2px;
            box-shadow: 0 2px 8px rgba(255,255,255,0.3);
        }

        .page-header p {
            font-size: var(--font-size-xl);
            color: rgba(255,255,255,0.95);
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.7;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        /* Decorative Elements */
        .page-header-decoration {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            border: 2px solid rgba(255,255,255,0.1);
            border-radius: 50%;
            z-index: 1;
        }

        .page-header-decoration::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 150px;
            height: 150px;
            border: 1px solid rgba(255,255,255,0.05);
            border-radius: 50%;
        }
        */

        /* Filters Section - Modern Card-Based Professional Design */
        .filters-section {
            background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
            padding: var(--spacing-xxl) 0;
            border-bottom: 1px solid var(--border-color);
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            position: relative;
        }

        .filters-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(74,144,164,0.05)"/></pattern></defs><rect width="60" height="60" fill="url(%23dots)"/></svg>');
            opacity: 0.5;
        }

        .filters-container {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: var(--spacing-xxl);
            align-items: start;
            position: relative;
            z-index: 2;
            width: 100%;
            max-width: 1400px;
            margin: 0 auto;
            direction: rtl;
            padding: 0 var(--spacing-xl);
        }

        /* Enhanced Filters Card - Professional Design */
        .filters-card {
            background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(248, 249, 250, 0.95) 100%);
            border-radius: 20px;
            padding: var(--spacing-xxl);
            box-shadow: 0 16px 48px rgba(0,0,0,0.06), 0 8px 24px rgba(74, 144, 164, 0.08);
            border: 2px solid rgba(74, 144, 164, 0.12);
            position: relative;
            overflow: visible;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 0;
            width: 100%;
            backdrop-filter: blur(15px);
        }

        .filters-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 24px 64px rgba(0,0,0,0.08), 0 12px 32px rgba(74, 144, 164, 0.12);
            border-color: rgba(74, 144, 164, 0.2);
        }

        .filters-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark), var(--primary-color));
            border-radius: 20px 20px 0 0;
            opacity: 0.9;
        }

        .filters-card::after {
            content: '';
            position: absolute;
            top: 6px;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 25%, rgba(74, 144, 164, 0.03) 50%, transparent 75%);
            border-radius: 0 0 20px 20px;
            pointer-events: none;
        }

        .filters-title {
            font-size: var(--font-size-2xl);
            font-weight: 800;
            color: var(--primary-dark);
            margin-bottom: var(--spacing-xl);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            position: relative;
            z-index: 2;
            text-shadow: 0 2px 4px rgba(0,0,0,0.05);
            direction: rtl;
            text-align: right;
        }

        .filters-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            right: 0;
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
            border-radius: 2px;
            opacity: 0.8;
        }

        .filters-title i {
            color: var(--primary-color);
            font-size: var(--font-size-2xl);
            padding: var(--spacing-md);
            background: rgba(74, 144, 164, 0.12);
            border-radius: 14px;
            transition: all 0.4s ease;
            box-shadow: 0 4px 12px rgba(74, 144, 164, 0.15);
        }

        .filters-card:hover .filters-title i {
            background: rgba(74, 144, 164, 0.18);
            transform: scale(1.05) rotate(5deg);
            box-shadow: 0 6px 16px rgba(74, 144, 164, 0.2);
        }

        .search-and-filters {
            display: grid;
            grid-template-columns: 1.5fr 1fr 1fr 1fr auto;
            gap: var(--spacing-xl);
            align-items: end;
            width: 100%;
            direction: rtl;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
            min-width: 0;
            flex: 1;
            direction: rtl;
            text-align: right;
        }

        .filter-label {
            font-size: var(--font-size-base);
            font-weight: 700;
            color: var(--primary-dark);
            margin-bottom: var(--spacing-sm);
            white-space: nowrap;
            text-align: right;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
        }

        .filter-label::before {
            content: '';
            position: absolute;
            bottom: -4px;
            right: 0;
            width: 30px;
            height: 2px;
            background: var(--primary-color);
            border-radius: 1px;
        }

        .search-input {
            width: 100%;
            padding: var(--spacing-lg) var(--spacing-xl);
            border: 2px solid rgba(74, 144, 164, 0.15);
            border-radius: 18px;
            font-family: var(--font-family);
            font-size: var(--font-size-base);
            background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(248, 249, 250, 0.8) 100%);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            text-align: right;
            direction: rtl;
            box-shadow: 0 6px 16px rgba(0,0,0,0.04), inset 0 1px 3px rgba(74, 144, 164, 0.05);
            color: var(--text-primary);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(74, 144, 164, 0.15), 0 8px 20px rgba(0,0,0,0.08);
            transform: translateY(-2px);
            background: var(--bg-primary);
        }

        .search-input:hover {
            border-color: var(--primary-light);
            box-shadow: 0 4px 12px rgba(0,0,0,0.06);
        }

        .search-input::placeholder {
            color: var(--text-secondary);
            opacity: 0.8;
            text-align: right;
            font-weight: 500;
        }

        .filter-select {
            width: 100%;
            padding: var(--spacing-lg) var(--spacing-xl);
            border: 2px solid var(--border-color);
            border-radius: 16px;
            font-family: var(--font-family);
            font-size: var(--font-size-base);
            background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(248, 249, 250, 0.5) 100%);
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            appearance: none;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%234a90a4" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"><polyline points="6,9 12,15 18,9"></polyline></svg>');
            background-repeat: no-repeat;
            background-position: right 16px center;
            background-size: 18px;
            padding-right: 50px;
            text-align: right;
            direction: rtl;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            font-weight: 600;
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(74, 144, 164, 0.15), 0 4px 12px rgba(0,0,0,0.08);
            transform: translateY(-2px);
            background: var(--bg-primary);
        }

        .filter-select:hover {
            border-color: var(--primary-light);
            background: rgba(74, 144, 164, 0.03);
            box-shadow: 0 4px 12px rgba(0,0,0,0.06);
            transform: translateY(-1px);
        }

        .clear-filters {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: var(--spacing-lg) var(--spacing-xl);
            border-radius: 16px;
            font-family: var(--font-family);
            font-size: var(--font-size-base);
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: none;
            position: relative;
            overflow: hidden;
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.3);
            white-space: nowrap;
            width: 100%;
            text-align: center;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
            border: 2px solid transparent;
        }

        .clear-filters::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .clear-filters:hover::before {
            left: 100%;
        }

        .clear-filters:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(231, 76, 60, 0.4);
            border-color: rgba(255,255,255,0.2);
        }

        .clear-filters:active {
            transform: translateY(-1px);
        }

        /* Enhanced Sort Card - Professional Design */
        .sort-card {
            background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(248, 249, 250, 0.95) 100%);
            border-radius: 20px;
            padding: var(--spacing-xxl);
            box-shadow: 0 16px 48px rgba(0,0,0,0.06), 0 8px 24px rgba(74, 144, 164, 0.08);
            border: 2px solid rgba(74, 144, 164, 0.12);
            position: relative;
            overflow: hidden;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 280px;
            backdrop-filter: blur(15px);
            direction: rtl;
            text-align: right;
        }

        .sort-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 24px 64px rgba(0,0,0,0.08), 0 12px 32px rgba(74, 144, 164, 0.12);
            border-color: rgba(74, 144, 164, 0.2);
        }

        .sort-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, var(--primary-dark), var(--primary-color), var(--primary-dark));
            border-radius: 20px 20px 0 0;
            opacity: 0.9;
        }

        .sort-card::after {
            content: '';
            position: absolute;
            top: 6px;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 25%, rgba(74, 144, 164, 0.03) 50%, transparent 75%);
            border-radius: 0 0 20px 20px;
            pointer-events: none;
        }

        .sort-title {
            font-size: var(--font-size-2xl);
            font-weight: 800;
            color: var(--primary-dark);
            margin-bottom: var(--spacing-xl);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            position: relative;
            z-index: 2;
            text-shadow: 0 2px 4px rgba(0,0,0,0.05);
            direction: rtl;
            text-align: right;
        }

        .sort-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            right: 0;
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-dark), var(--primary-color));
            border-radius: 2px;
            opacity: 0.8;
        }

        .sort-title i {
            color: var(--primary-color);
            font-size: var(--font-size-2xl);
            padding: var(--spacing-md);
            background: rgba(74, 144, 164, 0.12);
            border-radius: 14px;
            transition: all 0.4s ease;
            box-shadow: 0 4px 12px rgba(74, 144, 164, 0.15);
        }

        .sort-card:hover .sort-title i {
            background: rgba(74, 144, 164, 0.18);
            transform: scale(1.05) rotate(-5deg);
            box-shadow: 0 6px 16px rgba(74, 144, 164, 0.2);
        }

        .sort-select {
            width: 100%;
            padding: var(--spacing-lg) var(--spacing-xl);
            border: 2px solid rgba(74, 144, 164, 0.15);
            border-radius: 18px;
            font-family: var(--font-family);
            font-size: var(--font-size-lg);
            background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(248, 249, 250, 0.8) 100%);
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            appearance: none;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%234a90a4" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"><polyline points="6,9 12,15 18,9"></polyline></svg>');
            background-repeat: no-repeat;
            background-position: right 18px center;
            background-size: 22px;
            padding-right: 55px;
            text-align: right;
            direction: rtl;
            box-shadow: 0 6px 16px rgba(0,0,0,0.04), inset 0 1px 3px rgba(74, 144, 164, 0.05);
            font-weight: 700;
            color: var(--primary-dark);
        }

        .sort-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(74, 144, 164, 0.15), 0 4px 12px rgba(0,0,0,0.08);
            transform: translateY(-2px);
            background: var(--bg-primary);
        }

        .sort-select:hover {
            border-color: var(--primary-light);
            background: rgba(74, 144, 164, 0.03);
            box-shadow: 0 4px 12px rgba(0,0,0,0.06);
            transform: translateY(-1px);
        }







        /* Products Section - Enhanced Professional Design */
        .products-section {
            padding: var(--spacing-xxl) 0;
            min-height: 60vh;
            background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(248, 249, 250, 0.5) 100%);
            position: relative;
        }

        .products-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(74,144,164,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.6;
            pointer-events: none;
        }

        .products-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-xxl);
            padding: var(--spacing-xxl);
            background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(248, 249, 250, 0.9) 100%);
            border-radius: 24px;
            box-shadow: 0 12px 40px rgba(0,0,0,0.06), 0 4px 16px rgba(74, 144, 164, 0.08);
            border: 2px solid rgba(74, 144, 164, 0.1);
            position: relative;
            overflow: hidden;
            direction: rtl;
            backdrop-filter: blur(10px);
        }

        .products-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(74, 144, 164, 0.03) 50%, transparent 70%);
            pointer-events: none;
        }

        .products-header-content {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
            position: relative;
            z-index: 2;
        }

        .products-title {
            font-size: var(--font-size-4xl);
            font-weight: 700;
            color: var(--primary-dark);
            margin: 0;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            position: relative;
        }

        .products-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            right: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
            border-radius: 2px;
        }

        .products-sub-description {
            font-size: var(--font-size-base);
            color: var(--text-secondary);
            font-weight: 400;
            margin: 1rem 0 0 0;
            opacity: 0.9;
            line-height: 1.6;
        }

        .products-count {
            font-size: var(--font-size-xl);
            font-weight: 700;
            color: var(--primary-color);
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 2;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: 10px;
            background-color: rgba(74, 144, 164, 0.1);
            border: 2px solid rgba(74, 144, 164, 0.2);
            transition: all 0.3s ease;
        }

        .products-count:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(74, 144, 164, 0.2);
        }

        /* Products Grid - Desktop Only */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
            gap: var(--spacing-xxl);
            margin-top: var(--spacing-xxl);
            padding: 0 var(--spacing-md);
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Product Card - Enhanced Professional Design with LTR Layout */
        .product-card {
            background: var(--bg-primary);
            border-radius: 20px;
            overflow: hidden;
            box-shadow:
                0 10px 40px rgba(0,0,0,0.06),
                0 6px 20px rgba(74, 144, 164, 0.08),
                0 2px 8px rgba(74, 144, 164, 0.04);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            border: 2px solid rgba(74, 144, 164, 0.08);
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
            backdrop-filter: blur(20px);
            min-height: 500px;
            transform: translateZ(0);
            will-change: transform, box-shadow;
            direction: ltr;
            text-align: left;
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg,
                var(--primary-color) 0%,
                var(--primary-dark) 50%,
                var(--primary-color) 100%);
            opacity: 0;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1;
            border-radius: 20px 20px 0 0;
        }

        .product-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(74, 144, 164, 0.02) 0%,
                transparent 50%,
                rgba(44, 62, 80, 0.02) 100%);
            opacity: 0;
            transition: opacity 0.5s ease;
            z-index: 0;
            border-radius: 20px;
        }

        .product-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow:
                0 20px 60px rgba(0,0,0,0.08),
                0 12px 30px rgba(74, 144, 164, 0.15),
                0 6px 15px rgba(74, 144, 164, 0.1);
            border-color: rgba(74, 144, 164, 0.3);
        }

        .product-card:hover::before {
            opacity: 1;
        }

        .product-card:hover::after {
            opacity: 1;
        }

        .product-image {
            width: 100%;
            height: 300px;
            background: linear-gradient(135deg,
                var(--bg-secondary) 0%,
                rgba(248, 249, 250, 0.95) 50%,
                rgba(74, 144, 164, 0.05) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-5xl);
            color: var(--primary-color);
            position: relative;
            overflow: hidden;
            border-bottom: 2px solid rgba(74, 144, 164, 0.08);
        }

        .product-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg,
                transparent 20%,
                rgba(74, 144, 164, 0.06) 40%,
                rgba(44, 62, 80, 0.04) 60%,
                transparent 80%);
            opacity: 0;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(-100%);
        }

        .product-card:hover .product-image::before {
            opacity: 1;
            transform: translateX(100%);
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            filter: brightness(1) contrast(1.05) saturate(1.1);
        }

        .product-card:hover .product-image img {
            transform: scale(1.1) rotate(1deg);
            filter: brightness(1.05) contrast(1.1) saturate(1.2);
        }

        .product-info {
            padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
            position: relative;
            z-index: 2;
            background: linear-gradient(180deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(248, 249, 250, 0.8) 50%,
                rgba(74, 144, 164, 0.02) 100%);
            direction: ltr;
            text-align: left;
        }

        .product-name {
            font-size: var(--font-size-xl);
            font-weight: 700;
            color: var(--text-primary);
            line-height: 1.4;
            margin-bottom: var(--spacing-xs);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            text-shadow: 0 1px 2px rgba(0,0,0,0.02);
            letter-spacing: -0.02em;
            text-align: right;
        }

        .product-card:hover .product-name {
            color: var(--primary-color);
            transform: translateY(-2px);
            text-shadow: 0 2px 4px rgba(74, 144, 164, 0.1);
        }

        .product-description {
            font-size: var(--font-size-base);
            color: var(--text-secondary);
            line-height: 1.7;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            flex: 1;
            transition: color 0.3s ease;
            letter-spacing: 0.01em;
            text-align: right;
        }

        .product-card:hover .product-description {
            color: #555;
        }

        .product-price {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-top: auto;
            padding: var(--spacing-md) 0;
            border-top: 2px solid rgba(74, 144, 164, 0.08);
            position: relative;
            direction: rtl;
            justify-content: flex-start;
        }

        .product-price::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg,
                var(--primary-color) 0%,
                transparent 50%,
                var(--primary-color) 100%);
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .product-card:hover .product-price::before {
            opacity: 0.3;
        }

        .current-price {
            font-size: var(--font-size-2xl);
            font-weight: 800;
            color: var(--primary-color);
            text-shadow: 0 2px 4px rgba(74, 144, 164, 0.15);
            transition: all 0.3s ease;
            letter-spacing: -0.02em;
        }

        .product-card:hover .current-price {
            transform: scale(1.05);
            text-shadow: 0 3px 6px rgba(74, 144, 164, 0.2);
        }

        .original-price {
            font-size: var(--font-size-lg);
            color: var(--text-secondary);
            text-decoration: line-through;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .product-card:hover .original-price {
            opacity: 0.9;
        }

        .discount-badge {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: var(--spacing-xs) var(--spacing-md);
            border-radius: 30px;
            font-size: var(--font-size-sm);
            font-weight: 700;
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.25);
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .product-card:hover .discount-badge {
            box-shadow: 0 6px 16px rgba(231, 76, 60, 0.4);
            animation: none;
        }

        .product-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: auto;
            padding-top: var(--spacing-lg);
            position: relative;
            z-index: 3;
            direction: ltr;
            justify-content: flex-start;
        }

        .add-to-cart {
            flex: 1.5;
            background: linear-gradient(135deg,
                var(--primary-color) 0%,
                var(--primary-dark) 100%);
            color: white;
            border: none;
            padding: var(--spacing-lg) var(--spacing-xl);
            border-radius: 16px;
            font-family: var(--font-family);
            font-size: var(--font-size-base);
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 24px rgba(74, 144, 164, 0.2);
            text-shadow: 0 1px 3px rgba(0,0,0,0.2);
            min-height: 52px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            letter-spacing: 0.02em;
        }

        .add-to-cart::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255,255,255,0.3),
                transparent);
            transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .add-to-cart::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.4s ease;
        }

        .add-to-cart:hover::before {
            left: 100%;
        }

        .add-to-cart:hover::after {
            width: 300px;
            height: 300px;
        }

        .add-to-cart:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 12px 32px rgba(74, 144, 164, 0.3);
            background: linear-gradient(135deg,
                var(--primary-dark) 0%,
                var(--primary-color) 100%);
        }

        .add-to-cart:active {
            transform: translateY(-2px) scale(0.98);
            transition: all 0.1s ease;
        }

        .add-to-cart:disabled {
            background: linear-gradient(135deg, var(--text-secondary), #999);
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            opacity: 0.7;
        }

        .add-to-cart:disabled::before,
        .add-to-cart:disabled::after {
            display: none;
        }

        .view-details {
            flex: 1;
            background: rgba(74, 144, 164, 0.05);
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            padding: var(--spacing-lg) var(--spacing-xl);
            border-radius: 16px;
            font-family: var(--font-family);
            font-size: var(--font-size-base);
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 140px;
            position: relative;
            overflow: hidden;
            min-height: 52px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            letter-spacing: 0.02em;
            backdrop-filter: blur(10px);
        }

        .view-details::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                var(--primary-color) 0%,
                var(--primary-dark) 100%);
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: -1;
        }

        .view-details::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.4s ease;
            z-index: -1;
        }

        .view-details:hover::before {
            transform: scaleX(1);
            transform-origin: right;
        }

        .view-details:hover::after {
            width: 200px;
            height: 200px;
        }

        .view-details:hover {
            color: white;
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 8px 24px rgba(74, 144, 164, 0.25);
            border-color: transparent;
        }

        .view-details:active {
            transform: translateY(-1px) scale(0.98);
            transition: all 0.1s ease;
        }

        /* Enhanced Product Card Interactions */
        .product-card:focus-within {
            outline: 3px solid rgba(74, 144, 164, 0.3);
            outline-offset: 2px;
        }

        .product-card .product-actions button:focus {
            outline: 2px solid rgba(74, 144, 164, 0.5);
            outline-offset: 2px;
        }

        /* Smooth Grid Animation */
        .products-grid .product-card {
            animation: fadeInUp 0.6s ease forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .products-grid .product-card:nth-child(1) { animation-delay: 0.1s; }
        .products-grid .product-card:nth-child(2) { animation-delay: 0.2s; }
        .products-grid .product-card:nth-child(3) { animation-delay: 0.3s; }
        .products-grid .product-card:nth-child(4) { animation-delay: 0.4s; }
        .products-grid .product-card:nth-child(5) { animation-delay: 0.5s; }
        .products-grid .product-card:nth-child(6) { animation-delay: 0.6s; }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Loading and No Products */
        .loading {
            text-align: center;
            padding: var(--spacing-xxl);
            font-size: var(--font-size-lg);
            color: var(--primary-color);
        }

        .no-products {
            text-align: center;
            padding: var(--spacing-xxl);
            color: var(--text-secondary);
        }

        .no-products i {
            font-size: var(--font-size-3xl);
            margin-bottom: var(--spacing-lg);
            color: var(--primary-color);
        }

        .no-products h3 {
            font-size: var(--font-size-xl);
            margin-bottom: var(--spacing-md);
            color: var(--text-primary);
        }

        /* Footer - Matching Homepage Design */
        footer {
            background: #0f1111;
            color: white;
            padding: 4rem 0 2rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
            margin-bottom: 3rem;
        }

        .footer-section h3 {
            color: #FFFFFF;
            margin-bottom: 2rem;
            font-size: var(--font-size-xl);
            font-weight: 700;
        }

        .footer-section p,
        .footer-section a {
            color: #FFFFFF;
            text-decoration: none;
            line-height: 1.8;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            margin-bottom: 0.8rem;
            transition: all 0.3s ease;
        }

        .footer-section a:hover {
            color: #4a90a4;
        }

        .footer-section i {
            font-size: var(--font-size-lg);
            color: #4a90a4;
            min-width: 20px;
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .social-links a:hover {
            border-color: #4a90a4;
            background: rgba(74, 144, 164, 0.2);
        }

        .social-links a i {
            font-size: var(--font-size-xl);
            min-width: auto;
        }

        .footer-bottom {
            text-align: center;
            padding: 2rem 0;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: #FFFFFF;
        }

        .business-name {
            color: #4a90a4;
            font-weight: 700;
        }

        /* Notification System */
        .cart-notification {
            position: fixed;
            top: 100px;
            right: 20px;
            z-index: 10000;
            opacity: 0;
            transform: translateX(400px);
            transition: all 0.4s ease;
            max-width: 400px;
            width: calc(100% - 40px);
        }

        .cart-notification.show {
            opacity: 1;
            transform: translateX(0);
        }

        .notification-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            border-left: 5px solid #4a90a4;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .notification-icon {
            background: linear-gradient(135deg, #4a90a4, #2c3e50);
            color: white;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-xl);
            flex-shrink: 0;
        }

        .notification-content {
            flex: 1;
            color: #000000;
        }

        .notification-title {
            font-size: var(--font-size-base);
            font-weight: 700;
            margin-bottom: 0.3rem;
            color: #4a90a4;
        }

        .notification-message {
            font-size: var(--font-size-sm);
            color: #000000;
            line-height: 1.4;
            margin-bottom: 0.8rem;
        }

        .notification-actions {
            display: flex;
            gap: 0.8rem;
        }

        .notification-btn {
            padding: 0.4rem 0.8rem;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-sm);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
        }

        .notification-btn.primary {
            background: linear-gradient(135deg, #4a90a4, #2c3e50);
            color: white;
        }

        .notification-btn.secondary {
            background: rgba(74, 144, 164, 0.1);
            color: #4a90a4;
            border: 1px solid rgba(74, 144, 164, 0.3);
        }
    </style>
</head>
<body>
    <header role="banner">
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo business-name" aria-label="الصفحة الرئيسية" data-setting="business_name">Care</a>



                <nav>
                    <ul class="nav-menu" id="navMenu">
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html" class="active">المنتجات</a></li>
                        <li><a href="offers.html">العروض</a></li>
                        <li><a href="guidelines.html">الإرشادات</a></li>
                        <li><a href="faq.html">الأسئلة الشائعة</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                    </ul>
                </nav>

                <div class="cart-icon" onclick="window.location.href='cart.html'" role="button" aria-label="سلة التسوق" tabindex="0">
                    <i class="fas fa-shopping-cart" aria-hidden="true"></i>
                    <span class="cart-count" id="cartCount">0</span>
                </div>
            </div>
        </div>


    </header>

    <section class="page-header products-bg" id="productsPageHeader">
        <div class="page-header-decoration"></div>
        <div class="container">
            <div class="page-header-content">
                <nav class="breadcrumb" aria-label="مسار التنقل">
                    <a href="index.html">الرئيسية</a>
                    <span class="separator">←</span>
                    <span class="current">المنتجات</span>
                </nav>
                <h1 data-setting="products_page_title">منتجاتنا المميزة</h1>
            </div>
        </div>
    </section>

    <section class="filters-section">
        <div class="container">
            <div class="filters-container">
                <div class="filters-card">
                    <h3 class="filters-title">
                        <i class="fas fa-filter"></i>
                        تصفية المنتجات
                    </h3>
                    <div class="search-and-filters">
                        <div class="filter-group">
                            <label class="filter-label">البحث</label>
                            <input type="text" id="searchInput" class="search-input" placeholder="ابحث عن منتج...">
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">الفئة</label>
                            <select id="categoryFilter" class="filter-select">
                                <option value="">جميع الفئات</option>
                                <option value="skincare">العناية بالبشرة</option>
                                <option value="haircare">العناية بالشعر</option>
                                <option value="general">عام</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">التوفر</label>
                            <select id="availabilityFilter" class="filter-select">
                                <option value="">جميع المنتجات</option>
                                <option value="available">متوفر</option>
                                <option value="unavailable">غير متوفر</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">السعر</label>
                            <select id="priceFilter" class="filter-select">
                                <option value="">جميع الأسعار</option>
                                <option value="0-25000">أقل من 25,000 د.ع</option>
                                <option value="25000-50000">25,000 - 50,000 د.ع</option>
                                <option value="50000-100000">50,000 - 100,000 د.ع</option>
                                <option value="100000+">أكثر من 100,000 د.ع</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label" style="opacity: 0;">إجراءات</label>
                            <button id="clearFilters" class="clear-filters">
                                <i class="fas fa-times"></i> مسح الفلاتر
                            </button>
                        </div>
                    </div>
                </div>

                <div class="sort-card">
                    <h3 class="sort-title">
                        <i class="fas fa-sort"></i>
                        ترتيب
                    </h3>
                    <select id="sortSelect" class="sort-select">
                        <option value="newest">الأحدث</option>
                        <option value="name">الاسم</option>
                        <option value="price_low">السعر: من الأقل للأعلى</option>
                        <option value="price_high">السعر: من الأعلى للأقل</option>
                    </select>
                </div>
            </div>
        </div>
    </section>

    <main class="products-section">
        <div class="container">
            <div class="products-header">
                <div class="products-header-content">
                    <h2 class="products-title featured-products-title" data-setting="featured_products_title">منتجاتنا المميزة</h2>
                    <p class="products-sub-description" data-setting="products_page_sub_description">اكتشف مجموعتنا المختارة بعناية من أفضل منتجات العناية بالبشرة والشعر</p>
                </div>
                <div class="products-count" id="productsCount">جاري التحميل...</div>
            </div>

            <div id="loading" class="loading">
                <i class="fas fa-spinner fa-spin"></i> جاري تحميل المنتجات...
            </div>

            <div class="products-grid" id="productsGrid">
                <!-- Products will be loaded here -->
            </div>

            <div id="noProducts" class="no-products" style="display: none;">
                <i class="fas fa-search"></i>
                <h3>لم يتم العثور على منتجات</h3>
                <p>لم يتم العثور على منتجات تطابق معايير البحث</p>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>معلومات التواصل</h3>
                    <p><i class="fas fa-map-marker-alt"></i> <span class="business-address" data-setting="business_address">الكرادة، قرب مطعم المحطة</span></p>
                    <p><i class="fas fa-phone"></i> <span class="business-phone" data-setting="business_phone">***********</span></p>
                    <p><i class="fas fa-envelope"></i> <span class="business-email" data-setting="business_email"><EMAIL></span></p>
                </div>
                <div class="footer-section">
                    <h3>أوقات العمل</h3>
                    <p><span class="working-days" data-setting="working_days">السبت - الخميس</span>: <span class="working-hours" data-setting="working_hours">10 صباحاً - 5 مساءً</span></p>
                    <p><span class="closed-day" data-setting="closed_day">الجمعة</span>: مغلق</p>
                </div>
                <div class="footer-section">
                    <h3>تابعنا على وسائل التواصل</h3>
                    <div class="social-links">
                        <a href="#" class="whatsapp-link" target="_blank" title="واتساب" style="color: #25d366;" aria-label="تواصل معنا عبر واتساب">
                            <i class="fab fa-whatsapp" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="facebook-link" target="_blank" title="فيسبوك" style="color: #1877f2; display: none;" aria-label="تابعنا على فيسبوك">
                            <i class="fab fa-facebook-f" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="instagram-link" target="_blank" title="إنستغرام" style="color: #e4405f; display: none;" aria-label="تابعنا على إنستغرام">
                            <i class="fab fa-instagram" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="twitter-link" target="_blank" title="تويتر" style="color: #1da1f2; display: none;" aria-label="تابعنا على تويتر">
                            <i class="fab fa-twitter" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="telegram-link" target="_blank" title="تيليجرام" style="color: #0088cc; display: none;" aria-label="تابعنا على تيليجرام">
                            <i class="fab fa-telegram-plane" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="linkedin-link" target="_blank" title="لينكد إن" style="color: #0077b5; display: none;" aria-label="تابعنا على لينكد إن">
                            <i class="fab fa-linkedin-in" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="tiktok-link" target="_blank" title="تيك توك" style="color: #ff0050; display: none;" aria-label="تابعنا على تيك توك">
                            <i class="fab fa-tiktok" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="youtube-link" target="_blank" title="يوتيوب" style="color: #ff0000; display: none;" aria-label="تابعنا على يوتيوب">
                            <i class="fab fa-youtube" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="snapchat-link" target="_blank" title="سناب شات" style="color: #fffc00; display: none;" aria-label="تابعنا على سناب شات">
                            <i class="fab fa-snapchat-ghost" aria-hidden="true"></i>
                        </a>                       
                    </div>
                </div>
                <div class="footer-section">
                    <p><a href="guidelines.html"><i class="fas fa-book"></i>دليل الاستخدام</a></p>
                    <p><a href="faq.html"><i class="fas fa-question-circle"></i>الأسئلة الشائعة</a></p>
                    <p><a href="terms.html"><i class="fas fa-shield-alt"></i>سياسة الخصوصية</a></p>
                    <p><a href="terms.html"><i class="fas fa-undo-alt"></i>سياسة الإرجاع</a></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <span class="business-name" data-setting="business_name">Care</span>. <span class="copyright-text" data-setting="copyright_text">جميع الحقوق محفوظة</span>.</p>
            </div>
        </div>
    </footer>

    <!-- Shared Supabase Configuration (must load first) -->
    <script src="js/supabase-config.js"></script>

    <!-- Site Settings Script -->
    <script src="js/site-settings.js"></script>

    <script>


        // Get shared Supabase client
        function getSupabaseClient() {
            // Use shared configuration (singleton pattern)
            if (window.SupabaseConfig) {
                return window.SupabaseConfig.getClient();
            }

            // Fallback: use global client if available
            if (window.globalSupabaseClient) {
                return window.globalSupabaseClient;
            }

            // Last resort: create client directly (should not happen if supabase-config.js is loaded)
            if (window.supabase && typeof window.supabase.createClient === 'function') {
                console.warn('⚠️ Creating Supabase client directly in products.html - supabase-config.js may not be loaded');
                const SUPABASE_URL = 'https://krqijjttwllohulmdwgs.supabase.co';
                const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70';
                window.globalSupabaseClient = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
                return window.globalSupabaseClient;
            }

            return null;
        }

        // Global variables
        let allProducts = [];
        let filteredProducts = [];
        let cart = JSON.parse(localStorage.getItem('cart')) || [];

        // Cart functionality
        function updateCartCount() {
            const cartCount = document.getElementById('cartCount');
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            cartCount.textContent = totalItems;
        }

        function addToCart(product) {
            const existingItem = cart.find(item => item.id === product.id);

            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                // Get the first available image from any of the three image fields
                const firstImage = product.image_url || product.image_url_2 || product.image_url_3;

                cart.push({
                    id: product.id,
                    name: product.name,
                    price: product.offer_price || product.price,
                    image_url: firstImage,
                    quantity: 1
                });
            }

            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartCount();

            // Show notification
            const quantity = existingItem ? existingItem.quantity : 1;
            showCartNotification(product.name, quantity);
        }

        function formatPrice(price) {
            return new Intl.NumberFormat('ar-IQ', {
                style: 'currency',
                currency: 'IQD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(price).replace('IQD', 'د.ع');
        }

        function getCategoryName(category) {
            switch(category) {
                case 'skincare': return 'العناية بالبشرة';
                case 'haircare': return 'العناية بالشعر';
                default: return 'عام';
            }
        }

        async function loadProducts() {
            try {
                const supabase = getSupabaseClient();
                if (!supabase) {
                    document.getElementById('loading').innerHTML = 'خطأ في الاتصال بقاعدة البيانات';
                    return;
                }

                const { data: products, error } = await supabase
                    .from('products')
                    .select('*')
                    .eq('is_active', true)
                    .order('created_at', { ascending: false });

                if (error) {
                    throw error;
                }

                allProducts = products || [];
                filteredProducts = [...allProducts];

                displayProducts();
                updateProductsCount();

            } catch (error) {
                document.getElementById('loading').innerHTML = 'حدث خطأ في تحميل المنتجات. يرجى إعادة تحميل الصفحة.';
            }
        }

        function displayProducts() {
            const grid = document.getElementById('productsGrid');
            const loading = document.getElementById('loading');
            const noProducts = document.getElementById('noProducts');

            loading.style.display = 'none';

            if (filteredProducts.length === 0) {
                grid.style.display = 'none';
                noProducts.style.display = 'block';
                return;
            }

            noProducts.style.display = 'none';
            grid.style.display = 'grid';

            grid.innerHTML = filteredProducts.map(product => {
                const hasOffer = product.is_on_offer && product.offer_price && product.offer_price < product.price;
                const discountPercentage = hasOffer ? Math.round(((product.price - product.offer_price) / product.price) * 100) : 0;

                // Get the first available image from any of the three image fields
                const firstImage = product.image_url || product.image_url_2 || product.image_url_3;

                return `
                    <div class="product-card" onclick="window.location.href='product-details.html?id=${product.id}'">
                        <div class="product-image">
                            ${firstImage ?
                                `<img src="${firstImage}" alt="${product.name}" loading="lazy">` :
                                `<i class="fas fa-box"></i>`
                            }
                        </div>
                        <div class="product-info">
                            <h3 class="product-name">${product.name}</h3>
                            <p class="product-description">${product.description || 'منتج عالي الجودة'}</p>
                            <div class="product-price">
                                <span class="current-price">${formatPrice(product.offer_price || product.price)}</span>
                                ${hasOffer ? `<span class="original-price">${formatPrice(product.price)}</span>` : ''}
                                ${hasOffer ? `<span class="discount-badge">-${discountPercentage}%</span>` : ''}
                            </div>
                            <div class="product-actions">
                                <button class="add-to-cart" onclick="event.stopPropagation(); addToCart(${JSON.stringify(product).replace(/"/g, '&quot;')})" ${!product.is_available ? 'disabled' : ''}>
                                    <i class="fas fa-shopping-cart"></i>
                                    ${product.is_available ? 'إضافة للسلة' : 'غير متوفر'}
                                </button>
                                <button class="view-details" onclick="event.stopPropagation(); window.location.href='product-details.html?id=${product.id}'">
                                    <i class="fas fa-eye"></i> التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function updateProductsCount() {
            const count = document.getElementById('productsCount');
            count.textContent = `عرض ${filteredProducts.length} من ${allProducts.length} منتج`;
        }

        function filterProducts() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const categoryFilter = document.getElementById('categoryFilter').value;
            const availabilityFilter = document.getElementById('availabilityFilter').value;
            const priceFilter = document.getElementById('priceFilter').value;

            filteredProducts = allProducts.filter(product => {
                const matchesSearch = product.name.toLowerCase().includes(searchTerm) ||
                                    (product.description && product.description.toLowerCase().includes(searchTerm));

                const matchesCategory = !categoryFilter || product.product_type === categoryFilter;

                const matchesAvailability = !availabilityFilter ||
                                          (availabilityFilter === 'available' && product.is_available) ||
                                          (availabilityFilter === 'unavailable' && !product.is_available);

                let matchesPrice = true;
                if (priceFilter) {
                    const price = product.offer_price || product.price;
                    switch(priceFilter) {
                        case '0-25000':
                            matchesPrice = price < 25000;
                            break;
                        case '25000-50000':
                            matchesPrice = price >= 25000 && price < 50000;
                            break;
                        case '50000-100000':
                            matchesPrice = price >= 50000 && price < 100000;
                            break;
                        case '100000+':
                            matchesPrice = price >= 100000;
                            break;
                    }
                }

                return matchesSearch && matchesCategory && matchesAvailability && matchesPrice;
            });

            sortProducts();
            displayProducts();
            updateProductsCount();
            updateClearFiltersButton();
        }

        function sortProducts() {
            const sortBy = document.getElementById('sortSelect').value;

            filteredProducts.sort((a, b) => {
                switch(sortBy) {
                    case 'name':
                        return a.name.localeCompare(b.name, 'ar');
                    case 'price_low':
                        return (a.offer_price || a.price) - (b.offer_price || b.price);
                    case 'price_high':
                        return (b.offer_price || b.price) - (a.offer_price || a.price);
                    case 'newest':
                    default:
                        return new Date(b.created_at) - new Date(a.created_at);
                }
            });
        }

        function clearAllFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('availabilityFilter').value = '';
            document.getElementById('priceFilter').value = '';

            filteredProducts = [...allProducts];
            sortProducts();
            displayProducts();
            updateProductsCount();
            updateClearFiltersButton();
        }

        function updateClearFiltersButton() {
            const searchInput = document.getElementById('searchInput').value;
            const categoryFilter = document.getElementById('categoryFilter').value;
            const availabilityFilter = document.getElementById('availabilityFilter').value;
            const priceFilter = document.getElementById('priceFilter').value;

            const hasActiveFilters = searchInput || categoryFilter || availabilityFilter || priceFilter;
            const clearFiltersBtn = document.getElementById('clearFilters');

            if (clearFiltersBtn) {
                clearFiltersBtn.style.display = hasActiveFilters ? 'block' : 'none';
            }
        }

        function showCartNotification(productName, quantity) {
            // Remove any existing notifications
            const existingNotification = document.querySelector('.cart-notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            // Create notification element
            const notification = document.createElement('div');
            notification.className = 'cart-notification';
            notification.setAttribute('role', 'alert');
            notification.setAttribute('aria-live', 'polite');

            notification.innerHTML = `
                <div class="notification-card">
                    <div class="notification-icon">
                        <i class="fas fa-check" aria-hidden="true"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-title">تمت الإضافة بنجاح!</div>
                        <div class="notification-message">
                            تم إضافة "${productName}" إلى السلة
                            ${quantity > 1 ? `(الكمية: ${quantity})` : ''}
                        </div>
                        <div class="notification-actions">
                            <a href="cart.html" class="notification-btn primary">
                                <i class="fas fa-shopping-cart"></i>
                                عرض السلة
                            </a>
                            <button class="notification-btn secondary" onclick="closeCartNotification()">
                                <i class="fas fa-shopping-bag"></i>
                                متابعة التسوق
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(notification);

            // Trigger show animation
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);

            // Auto close after 4 seconds
            setTimeout(() => {
                closeCartNotification();
            }, 4000);
        }

        function closeCartNotification() {
            const notification = document.querySelector('.cart-notification');
            if (notification) {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentElement) {
                        document.body.removeChild(notification);
                    }
                }, 400);
            }
        }

        // Initialize page
        function initializePage() {
            updateCartCount();

            // Check if Supabase is available
            if (window.supabase && typeof window.supabase.createClient === 'function') {
                loadProducts();
            } else {
                setTimeout(initializePage, 100);
            }
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Wait for site settings to load before loading products
            if (window.siteSettingsManager) {
                setTimeout(initializePage, 100);
            } else {
                window.addEventListener('siteSettingsLoaded', function() {
                    setTimeout(initializePage, 100);
                });

                // Fallback: load products after a delay if site settings don't load
                setTimeout(() => {
                    if (allProducts.length === 0) {
                        initializePage();
                    }
                }, 3000);
            }

            // Search and filter event listeners
            document.getElementById('searchInput').addEventListener('input', filterProducts);
            document.getElementById('categoryFilter').addEventListener('change', filterProducts);
            document.getElementById('availabilityFilter').addEventListener('change', filterProducts);
            document.getElementById('priceFilter').addEventListener('change', filterProducts);
            document.getElementById('sortSelect').addEventListener('change', function() {
                sortProducts();
                displayProducts();
            });

            // Clear filters button
            const clearFiltersBtn = document.getElementById('clearFilters');
            if (clearFiltersBtn) {
                clearFiltersBtn.addEventListener('click', clearAllFilters);
            }

            // Initialize clear filters button state
            updateClearFiltersButton();

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case 'f':
                            e.preventDefault();
                            document.getElementById('searchInput').focus();
                            break;
                        case 'k':
                            e.preventDefault();
                            clearAllFilters();
                            break;
                    }
                }
            });
        });
    </script>
</body>
</html>
