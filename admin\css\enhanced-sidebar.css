/**
 * Care E-commerce Professional Admin Dashboard Framework v2.0
 * إطار عمل لوحة التحكم الإدارية الاحترافية لمتجر Care - الإصدار المحدث
 *
 * ENHANCED COMPREHENSIVE DESIGN SYSTEM:
 * ====================================
 *
 * Primary Color Palette:
 * - #4a90a4 (Professional blue primary brand color)
 * - #2c3e50 (Dark secondary color for headers/text)
 * - #f8f9fa (Professional light background)
 * - #ffffff (Pure white for cards and forms)
 * - #2c3e50 (High contrast text)
 * - #5d6d7e (Secondary text)
 * - #85929e (Muted text)
 *
 * Enhanced Color System:
 * - Success: #27ae60 (Green for positive actions)
 * - Warning: #f39c12 (Orange for caution)
 * - Danger: #e74c3c (Red for destructive actions)
 * - Info: #17a2b8 (Blue for informational content)
 *
 * Typography Scale (Enhanced):
 * - Display: 2.5rem-3rem (Hero titles)
 * - Headings: 1.4rem-2.2rem (Page titles, section headers)
 * - Body: 1rem (Standard content)
 * - Small: 0.875rem (Secondary content)
 * - Caption: 0.75rem (Form labels, captions)
 * - Font: Cairo (Optimized for Arabic RTL with fallbacks)
 *
 * Enhanced Spacing System:
 * - Micro: 0.25rem (4px)
 * - Small: 0.5rem (8px)
 * - Base: 1rem (16px)
 * - Medium: 1.5rem (24px)
 * - Large: 2rem (32px)
 * - XL: 3rem (48px)
 * - Cards: 15px border radius, 2rem padding
 * - Touch targets: 44px minimum (WCAG AA compliant)
 *
 * Enhanced Responsive Breakpoints:
 * - XS: ≤320px (Very small mobile)
 * - SM: 321px-480px (Small mobile)
 * - MD: 481px-768px (Large mobile/Small tablet)
 * - LG: 769px-1200px (Tablet/Small desktop)
 * - XL: 1201px-1400px (Desktop)
 * - XXL: >1400px (Large desktop)
 *
 * Performance Optimizations:
 * - Hardware acceleration for animations
 * - Optimized CSS selectors
 * - Reduced repaints and reflows
 * - Modern CSS features with fallbacks
 */

/* ===== ENHANCED GLOBAL RESET & FOUNDATION ===== */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    /* Enhanced scroll performance */
    scroll-padding-top: 2rem;
    /* Better text rendering */
    text-rendering: optimizeLegibility;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    /* Enhanced background with better gradient */
    background: linear-gradient(135deg, var(--color-bg-secondary) 0%, var(--color-bg-tertiary) 50%, var(--color-bg-quaternary) 100%);
    background-attachment: fixed;
    color: var(--color-text-primary);
    direction: rtl;
    line-height: var(--line-height-normal);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    /* Enhanced font rendering */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    /* Better overflow handling */
    overflow-x: hidden;
    /* Improved performance */
    will-change: scroll-position;
    /* Better mobile experience */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
}

/* ===== ENHANCED TYPOGRAPHY SYSTEM ===== */
/* Heading Hierarchy */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', sans-serif;
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-md);
    margin-top: 0;
}

h1 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-extrabold);
    line-height: var(--line-height-tight);
    letter-spacing: -0.025em;
}

h2 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    letter-spacing: -0.025em;
}

h3 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
}

h4 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
}

h5 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

h6 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Paragraph and Text Elements */
p {
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-md);
    margin-top: 0;
}

.lead {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-light);
    line-height: var(--line-height-relaxed);
    color: var(--color-text-secondary);
}

.small, small {
    font-size: var(--font-size-sm);
    color: var(--color-text-muted);
}

.caption {
    font-size: var(--font-size-xs);
    color: var(--color-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-weight: var(--font-weight-medium);
}

/* Links */
a {
    color: var(--color-primary);
    text-decoration: none;
    transition: var(--transition-fast);
    font-weight: var(--font-weight-medium);
}

a:hover {
    color: var(--color-primary-dark);
    text-decoration: underline;
}

a:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
    border-radius: var(--radius-xs);
}

/* Lists */
ul, ol {
    margin: 0 0 var(--spacing-md) 0;
    padding-right: var(--spacing-lg);
}

li {
    margin-bottom: var(--spacing-xs);
    line-height: var(--line-height-relaxed);
}

/* Code and Pre */
code {
    font-family: 'Courier New', monospace;
    font-size: 0.875em;
    background: var(--color-bg-tertiary);
    padding: 0.2em 0.4em;
    border-radius: var(--radius-xs);
    color: var(--color-danger);
}

pre {
    background: var(--color-bg-tertiary);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    overflow-x: auto;
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
}

/* Blockquotes */
blockquote {
    border-right: 4px solid var(--color-primary);
    padding: var(--spacing-md) var(--spacing-lg);
    margin: var(--spacing-lg) 0;
    background: var(--color-bg-secondary);
    border-radius: var(--radius-md);
    font-style: italic;
    color: var(--color-text-secondary);
}

/* Arabic Text Optimization */
.arabic-text {
    font-family: 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif;
    font-feature-settings: 'liga' 1, 'calt' 1, 'kern' 1;
    text-rendering: optimizeLegibility;
}

/* Professional Text Styles */
.text-display {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-extrabold);
    line-height: var(--line-height-tight);
    letter-spacing: -0.025em;
}

.text-headline {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
}

.text-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-normal);
}

.text-subtitle {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-secondary);
}

.text-body {
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
}

.text-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Enhanced root variables for consistent theming */
:root {
    /* Z-INDEX LAYERING SYSTEM - Organized hierarchy */
    --z-base: 1;
    --z-content: 10;
    --z-header: 100;
    --z-top-bar: 500;
    --z-sidebar-overlay: 1500;
    --z-sidebar-mobile: 2000;
    --z-sidebar-desktop: 1000;
    --z-hamburger-btn: 2100;
    --z-modal: 3000;
    --z-tooltip: 4000;
    --z-skip-link: 9999;

    /* Primary Color Palette - Enhanced Professional Brand Colors */
    --color-primary: #4a90a4;
    --color-primary-dark: #357a8a;
    --color-primary-light: #6ba4b8;
    --color-primary-lighter: #8bb8cc;
    --color-primary-alpha-10: rgba(74, 144, 164, 0.1);
    --color-primary-alpha-20: rgba(74, 144, 164, 0.2);
    --color-primary-alpha-30: rgba(74, 144, 164, 0.3);

    /* Secondary Colors - Refined for better readability */
    --color-secondary: #2c3e50;
    --color-secondary-light: #34495e;
    --color-secondary-dark: #1a252f;

    /* Status Colors - Professional and Easy on Eyes */
    --color-success: #27ae60;
    --color-success-light: #2ecc71;
    --color-success-dark: #1e8449;
    --color-success-alpha: rgba(39, 174, 96, 0.1);

    --color-warning: #f39c12;
    --color-warning-light: #f1c40f;
    --color-warning-dark: #d68910;
    --color-warning-alpha: rgba(243, 156, 18, 0.1);

    --color-danger: #e74c3c;
    --color-danger-light: #ec7063;
    --color-danger-dark: #c0392b;
    --color-danger-alpha: rgba(231, 76, 60, 0.1);

    --color-info: #3498db;
    --color-info-light: #5dade2;
    --color-info-dark: #2980b9;
    --color-info-alpha: rgba(52, 152, 219, 0.1);

    /* Text Colors - Enhanced hierarchy for better readability */
    --color-text-primary: #2c3e50;
    --color-text-secondary: #5d6d7e;
    --color-text-muted: #85929e;
    --color-text-light: #bdc3c7;
    --color-text-inverse: #ffffff;
    --color-text-brand: #4a90a4;

    /* Background Colors - Professional and easy on eyes */
    --color-bg-primary: #ffffff;
    --color-bg-secondary: #f8f9fa;
    --color-bg-tertiary: #ecf0f1;
    --color-bg-quaternary: #d5dbdb;
    --color-bg-dark: #2c3e50;
    --color-bg-overlay: rgba(44, 62, 80, 0.5);

    /* Gradient Definitions - Professional and refined */
    --gradient-primary: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-light) 100%);
    --gradient-success: linear-gradient(135deg, var(--color-success) 0%, var(--color-success-dark) 100%);
    --gradient-warning: linear-gradient(135deg, var(--color-warning) 0%, var(--color-warning-dark) 100%);
    --gradient-danger: linear-gradient(135deg, var(--color-danger) 0%, var(--color-danger-dark) 100%);
    --gradient-info: linear-gradient(135deg, var(--color-info) 0%, var(--color-info-dark) 100%);
    --gradient-card: linear-gradient(135deg, var(--color-bg-primary) 0%, #fdfdfe 50%, #f8f9fa 100%);
    --gradient-sidebar: linear-gradient(135deg, var(--color-primary-dark) 0%, #2e6b7a 25%, var(--color-primary) 50%, #357a8a 75%, var(--color-primary-light) 100%);

    /* Spacing scale - Enhanced with micro and macro spacing */
    --spacing-xs: 0.25rem;    /* 4px */
    --spacing-sm: 0.5rem;     /* 8px */
    --spacing-md: 1rem;       /* 16px */
    --spacing-lg: 1.5rem;     /* 24px */
    --spacing-xl: 2rem;       /* 32px */
    --spacing-xxl: 3rem;      /* 48px */
    --spacing-xxxl: 4rem;     /* 64px */

    /* Border radius - Consistent rounded corners */
    --radius-xs: 4px;
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 15px;
    --radius-xl: 20px;
    --radius-xxl: 25px;
    --radius-full: 50%;

    /* Enhanced Shadow System - Professional and subtle */
    --shadow-xs: 0 1px 2px rgba(44, 62, 80, 0.04);
    --shadow-sm: 0 2px 4px rgba(44, 62, 80, 0.06);
    --shadow-md: 0 4px 12px rgba(44, 62, 80, 0.08);
    --shadow-lg: 0 8px 24px rgba(44, 62, 80, 0.12);
    --shadow-xl: 0 16px 48px rgba(44, 62, 80, 0.15);
    --shadow-xxl: 0 24px 64px rgba(44, 62, 80, 0.18);
    --shadow-brand: 0 4px 15px rgba(74, 144, 164, 0.25);
    --shadow-brand-lg: 0 8px 25px rgba(74, 144, 164, 0.35);

    /* Transition System */
    --transition-fast: 0.15s ease;
    --transition-base: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Typography Scale */
    --font-size-xs: 0.75rem;   /* 12px */
    --font-size-sm: 0.875rem;  /* 14px */
    --font-size-base: 1rem;    /* 16px */
    --font-size-lg: 1.125rem;  /* 18px */
    --font-size-xl: 1.25rem;   /* 20px */
    --font-size-2xl: 1.5rem;   /* 24px */
    --font-size-3xl: 1.875rem; /* 30px */
    --font-size-4xl: 2.25rem;  /* 36px */

    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
}

/* ===== ENHANCED ACCESSIBILITY & FOCUS MANAGEMENT ===== */
/* Modern focus-visible support with fallbacks */
:focus {
    outline: 3px solid var(--color-primary);
    outline-offset: 2px;
}

:focus:not(:focus-visible) {
    outline: none;
}

:focus-visible {
    outline: 3px solid var(--color-primary);
    outline-offset: 2px;
    box-shadow: 0 0 0 6px rgba(74, 144, 164, 0.2);
}

/* Enhanced focus styles for interactive elements */
button:focus,
input:focus,
select:focus,
textarea:focus,
a:focus,
[role="button"]:focus,
[tabindex]:focus {
    outline: 3px solid var(--color-primary);
    outline-offset: 2px;
    box-shadow: 0 0 0 6px rgba(74, 144, 164, 0.2);
    transition: var(--transition-fast);
}

/* Skip link for screen readers */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--color-primary);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: var(--radius-sm);
    z-index: var(--z-skip-link);
    font-weight: 600;
    transition: var(--transition-fast);
}

.skip-link:focus {
    top: 6px;
}

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== COLOR UTILITY CLASSES ===== */
/* Text Colors */
.text-primary { color: var(--color-primary) !important; }
.text-secondary { color: var(--color-text-secondary) !important; }
.text-muted { color: var(--color-text-muted) !important; }
.text-success { color: var(--color-success) !important; }
.text-warning { color: var(--color-warning) !important; }
.text-danger { color: var(--color-danger) !important; }
.text-info { color: var(--color-info) !important; }
.text-white { color: var(--color-text-inverse) !important; }
.text-dark { color: var(--color-text-primary) !important; }

/* Background Colors */
.bg-primary { background-color: var(--color-primary) !important; }
.bg-secondary { background-color: var(--color-bg-secondary) !important; }
.bg-success { background-color: var(--color-success) !important; }
.bg-warning { background-color: var(--color-warning) !important; }
.bg-danger { background-color: var(--color-danger) !important; }
.bg-info { background-color: var(--color-info) !important; }
.bg-white { background-color: var(--color-bg-primary) !important; }
.bg-light { background-color: var(--color-bg-secondary) !important; }
.bg-dark { background-color: var(--color-bg-dark) !important; }

/* Gradient Backgrounds */
.bg-gradient-primary { background: var(--gradient-primary) !important; }
.bg-gradient-secondary { background: var(--gradient-secondary) !important; }
.bg-gradient-success { background: var(--gradient-success) !important; }
.bg-gradient-warning { background: var(--gradient-warning) !important; }
.bg-gradient-danger { background: var(--gradient-danger) !important; }
.bg-gradient-info { background: var(--gradient-info) !important; }
.bg-gradient-card { background: var(--gradient-card) !important; }

/* Border Colors */
.border-primary { border-color: var(--color-primary) !important; }
.border-secondary { border-color: var(--color-text-secondary) !important; }
.border-success { border-color: var(--color-success) !important; }
.border-warning { border-color: var(--color-warning) !important; }
.border-danger { border-color: var(--color-danger) !important; }
.border-info { border-color: var(--color-info) !important; }
.border-light { border-color: var(--color-bg-tertiary) !important; }

/* ===== TYPOGRAPHY UTILITY CLASSES ===== */
.font-weight-light { font-weight: var(--font-weight-light) !important; }
.font-weight-normal { font-weight: var(--font-weight-normal) !important; }
.font-weight-medium { font-weight: var(--font-weight-medium) !important; }
.font-weight-semibold { font-weight: var(--font-weight-semibold) !important; }
.font-weight-bold { font-weight: var(--font-weight-bold) !important; }

.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-base { font-size: var(--font-size-base) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }
.text-2xl { font-size: var(--font-size-2xl) !important; }
.text-3xl { font-size: var(--font-size-3xl) !important; }
.text-4xl { font-size: var(--font-size-4xl) !important; }

/* ===== SPACING UTILITY CLASSES ===== */
.m-0 { margin: 0 !important; }
.m-xs { margin: var(--spacing-xs) !important; }
.m-sm { margin: var(--spacing-sm) !important; }
.m-md { margin: var(--spacing-md) !important; }
.m-lg { margin: var(--spacing-lg) !important; }
.m-xl { margin: var(--spacing-xl) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-xs { margin-bottom: var(--spacing-xs) !important; }
.mb-sm { margin-bottom: var(--spacing-sm) !important; }
.mb-md { margin-bottom: var(--spacing-md) !important; }
.mb-lg { margin-bottom: var(--spacing-lg) !important; }
.mb-xl { margin-bottom: var(--spacing-xl) !important; }

.p-0 { padding: 0 !important; }
.p-xs { padding: var(--spacing-xs) !important; }
.p-sm { padding: var(--spacing-sm) !important; }
.p-md { padding: var(--spacing-md) !important; }
.p-lg { padding: var(--spacing-lg) !important; }
.p-xl { padding: var(--spacing-xl) !important; }

/* ===== FLEXBOX UTILITY CLASSES ===== */
.flex { display: flex !important; }
.flex-column { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.items-center { align-items: center !important; }
.items-start { align-items: flex-start !important; }
.items-end { align-items: flex-end !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.justify-end { justify-content: flex-end !important; }
.gap-xs { gap: var(--spacing-xs) !important; }
.gap-sm { gap: var(--spacing-sm) !important; }
.gap-md { gap: var(--spacing-md) !important; }
.gap-lg { gap: var(--spacing-lg) !important; }
.gap-xl { gap: var(--spacing-xl) !important; }

/* ===== BORDER RADIUS UTILITY CLASSES ===== */
.rounded-xs { border-radius: var(--radius-xs) !important; }
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded-md { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }
.rounded-full { border-radius: var(--radius-full) !important; }

/* ===== SHADOW UTILITY CLASSES ===== */
.shadow-xs { box-shadow: var(--shadow-xs) !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-brand { box-shadow: var(--shadow-brand) !important; }
.shadow-none { box-shadow: none !important; }

/* Enhanced contrast for better readability */
@media (prefers-contrast: high) {
    :root {
        --color-text-primary: #000000;
        --color-text-secondary: #333333;
        --color-bg-primary: #ffffff;
        --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.25);
        --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.35);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* ===== UNIFIED SIDEBAR SYSTEM ===== */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile */
    /* STANDARDIZED gradient - identical across all pages */
    background: var(--gradient-sidebar);
    color: var(--color-text-inverse);
    z-index: var(--z-sidebar-desktop);
    transition: var(--transition-base);
    /* STANDARDIZED shadow system */
    box-shadow:
        -4px 0 30px rgba(0, 0, 0, 0.2),
        -2px 0 15px rgba(0, 0, 0, 0.1),
        -1px 0 5px rgba(0, 0, 0, 0.05);
    overflow-y: auto;
    overflow-x: hidden;
    transform: translateX(0);
    /* STANDARDIZED border */
    border-left: 1px solid var(--color-primary-alpha-30);
    /* Performance optimizations */
    will-change: transform;
    contain: layout style paint;
    /* Better backdrop support */
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    /* STANDARDIZED display structure */
    display: flex;
    flex-direction: column;
    font-family: 'Cairo', sans-serif;
}

/* STANDARDIZED sidebar top accent */
.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 50%, var(--color-primary) 100%);
    z-index: var(--z-base);
}

/* Custom scrollbar for sidebar */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* ===== COLLAPSIBLE SIDEBAR FUNCTIONALITY ===== */
.sidebar.collapsed {
    width: 70px;
    transform: translateX(0);
}

.sidebar.collapsed .sidebar-header h2 span,
.sidebar.collapsed .sidebar-header p,
.sidebar.collapsed .menu-item span {
    opacity: 0;
    visibility: hidden;
}

.sidebar.collapsed .menu-item {
    justify-content: center;
    padding: 1rem;
}

.sidebar.collapsed .menu-item i {
    margin: 0;
}

/* ===== SIDEBAR HEADER ===== */
.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    background: rgba(130, 135, 122, 0.1);
    position: relative;
}

.sidebar-header h2 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: white;
}

.sidebar-header h2 i {
    font-size: 2rem;
    color: #82877a;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.sidebar-header h2 span {
    transition: all 0.3s ease;
}

.sidebar-header p {
    opacity: 0.9;
    font-size: 0.9rem;
    margin: 0;
    font-weight: 400;
    transition: all 0.3s ease;
}

/* ===== SIDEBAR TOGGLE BUTTON ===== */
.sidebar-toggle {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: rgba(130, 135, 122, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

.sidebar-toggle:hover {
    background: rgba(130, 135, 122, 0.3);
    transform: scale(1.05);
}

/* ===== SIDEBAR MENU ===== */
.sidebar-menu {
    padding: 1rem 0;
    flex: 1;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: white;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    background: none;
    width: 100%;
    text-align: right;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    min-height: 44px; /* Touch-friendly minimum */
}

.menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: #82877a;
    transform: scaleY(0);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-item:hover,
.menu-item.active {
    background: rgba(130, 135, 122, 0.2);
    padding-right: 2rem;
    transform: translateX(-5px);
}

.menu-item:hover::before,
.menu-item.active::before {
    transform: scaleY(1);
}

.menu-item.active {
    background: rgba(130, 135, 122, 0.3);
    font-weight: 600;
}

.menu-item i {
    margin-left: 0.75rem;
    width: 20px;
    font-size: 1.2rem;
    text-align: center;
    transition: transform 0.3s ease;
    color: #82877a;
}

.menu-item:hover i,
.menu-item.active i {
    transform: scale(1.1);
    color: white;
}

.menu-item span {
    transition: all 0.3s ease;
}

/* ===== LOGOUT BUTTON ===== */
.logout-btn {
    position: absolute;
    bottom: 2rem;
    left: 1.5rem;
    right: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 1rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    backdrop-filter: blur(10px);
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.logout-btn:active {
    transform: translateY(0);
}

/* ===== FIXED RIGHT-SIDE SIDEBAR ===== */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(135deg, #121414 0%, #1a1f1f 25%, #2c3e50 50%, #34495e 75%, #2c3e50 100%);
    color: white;
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: -4px 0 30px rgba(0, 0, 0, 0.3);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    overflow-x: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #82877a 0%, #6b7062 50%, #82877a 100%);
    z-index: 1;
}

/* ===== MAIN CONTENT LAYOUT ===== */
.main-content {
    margin-right: 280px;
    min-height: 100vh;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 2rem;
}

.main-content.sidebar-collapsed {
    margin-right: 70px;
}

/* ===== THREE-SECTION SIDEBAR LAYOUT ===== */
.sidebar {
    display: flex;
    flex-direction: column;
}

.sidebar-section {
    position: relative;
}

/* ===== SEAMLESS BRAND SECTION ===== */
.sidebar-section.brand-section {
    padding: var(--spacing-xl) var(--spacing-lg);
    border-bottom: none;
    text-align: center;
    background: transparent; /* Seamless background */
    flex-shrink: 0;
}

.brand-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    margin-bottom: 0.8rem;
}

.brand-logo i {
    font-size: 2.2rem;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transition: var(--transition-base);
    width: 50px; /* STANDARDIZED icon container */
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.brand-logo:hover i {
    color: white;
    transform: scale(1.1);
    text-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    background: rgba(255, 255, 255, 0.2);
}

.brand-logo h2 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-text-inverse);
    margin: 0;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
    font-family: 'Cairo', sans-serif;
    line-height: var(--line-height-tight);
}

.brand-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-normal);
    margin: 0;
    opacity: 0.9;
    font-style: italic;
    font-family: 'Cairo', sans-serif;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

/* ===== UNIFIED NAVIGATION SECTION ===== */
.sidebar-section.unified-navigation {
    flex: 1;
    padding: 0; /* Remove padding for seamless design */
    border-bottom: none;
    overflow-y: auto;
    background: transparent;
    display: flex;
    flex-direction: column;
}

/* Legacy support for dashboard-section */
.sidebar-section.dashboard-section {
    flex: 1;
    padding: 0;
    border-bottom: none;
    overflow-y: auto;
    background: transparent;
}

/* REMOVED: Section headers and titles for seamless design */
.section-header {
    display: none; /* Hide section headers for seamless navigation */
}

.section-title {
    display: none; /* Hide section titles for clean appearance */
}

.sidebar-nav {
    padding: 0 var(--spacing-lg); /* Consistent padding for seamless design */
    flex: 1; /* Take available space in unified navigation */
}

.sidebar-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-xs); /* Reduced margin for seamless appearance */
    transition: var(--transition-base);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    position: relative;
    font-family: 'Cairo', sans-serif;
}

.sidebar-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(-2px);
}

.sidebar-link.active:hover {
    background: linear-gradient(135deg, rgba(130, 135, 122, 0.5) 0%, rgba(107, 112, 98, 0.5) 100%);
    transform: translateX(-3px);
}

.sidebar-link.active {
    background: linear-gradient(135deg, rgba(130, 135, 122, 0.4) 0%, rgba(107, 112, 98, 0.4) 100%);
    color: white !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    font-weight: 600;
    transform: translateX(-2px);
}

.sidebar-link.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #82877a 0%, #6b7062 100%);
    border-radius: 2px 0 0 2px;
    box-shadow: 0 0 8px rgba(130, 135, 122, 0.5);
}

.sidebar-link i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
    transition: var(--transition-base);
}

.sidebar-link.active i {
    color: white !important;
    transform: scale(1.1);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* ===== WEBSITE VIEW BUTTON STYLING ===== */
.sidebar-link[href*="../index.html"] {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white !important;
    margin-bottom: var(--spacing-md);
    box-shadow: 0 3px 10px rgba(23, 162, 184, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.sidebar-link[href*="../index.html"]:hover {
    background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
    transform: translateX(-3px);
    box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4);
}

.sidebar-link[href*="../index.html"] i {
    color: white !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* ===== LOGOUT BUTTON STYLING ===== */
.sidebar-link.logout-link {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white !important;
    margin-top: var(--spacing-md);
    box-shadow: 0 3px 10px rgba(220, 53, 69, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.sidebar-link.logout-link:hover {
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
    transform: translateX(-3px);
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
}

.sidebar-link.logout-link i {
    color: white !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* ===== SEAMLESS ADMIN SECTION ===== */
.sidebar-section.admin-section {
    padding: var(--spacing-lg);
    background: transparent; /* Seamless background */
    flex-shrink: 0;
}

/* ===== STANDARDIZED USER INFO COMPONENTS ===== */
.sidebar-user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin: var(--spacing-lg); /* Consistent margin for unified navigation */
    margin-top: var(--spacing-xl); /* Extra space above user info */
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    transition: var(--transition-base);
    font-family: 'Cairo', sans-serif;
}

.sidebar-user-info:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* STANDARDIZED user avatar - consistent across all components */
.user-avatar {
    width: 45px; /* STANDARDIZED size */
    height: 45px;
    border-radius: var(--radius-full);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-text-inverse);
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-lg);
    flex-shrink: 0;
    box-shadow: var(--shadow-brand);
    border: 2px solid white;
    transition: var(--transition-base);
    font-family: 'Cairo', sans-serif;
}

.user-avatar:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-brand-lg);
}

/* STANDARDIZED user details container */
.user-details {
    flex: 1;
    min-width: 0; /* Allow text truncation */
}

/* STANDARDIZED user name styling */
.user-name {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    color: var(--color-text-inverse);
    margin-bottom: var(--spacing-xs);
    font-family: 'Cairo', sans-serif;
    line-height: var(--line-height-tight);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* STANDARDIZED user role styling */
.user-role {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.8);
    font-weight: var(--font-weight-medium);
    font-family: 'Cairo', sans-serif;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.admin-links {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs); /* Consistent spacing for seamless design */
    padding: 0; /* Remove padding for seamless integration */
    margin-top: 0; /* Remove margin for seamless design */
}

/* ===== HAMBURGER MENU BUTTON ===== */
.hamburger-btn {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
    z-index: var(--z-hamburger-btn);
}

.hamburger-btn:hover {
    background: rgba(130, 135, 122, 0.1);
}

.hamburger-line {
    display: block;
    width: 22px;
    height: 2px;
    background: var(--color-text);
    margin: 4px 0;
    transition: all 0.3s ease;
    border-radius: 1px;
}

.hamburger-btn.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-btn.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.hamburger-btn.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* ===== SIDEBAR BACKDROP FOR MOBILE ===== */
.sidebar-backdrop {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: var(--z-sidebar-overlay);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sidebar-backdrop.active {
    display: block;
    opacity: 1;
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: #82877a;
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.nav-item:hover,
.nav-item.active {
    background: rgba(130, 135, 122, 0.2);
    color: white;
    transform: translateX(-5px);
    box-shadow: 0 4px 15px rgba(130, 135, 122, 0.2);
}

.nav-item:hover::before,
.nav-item.active::before {
    transform: scaleY(1);
}

.nav-item i {
    font-size: 1.2rem;
    color: #82877a;
    transition: all 0.3s ease;
    width: 24px;
    text-align: center;
}

.nav-item:hover i,
.nav-item.active i {
    color: white;
    transform: scale(1.1);
}

.nav-item span {
    font-weight: 500;
    transition: all 0.3s ease;
}

/* Admin action items styling */
.nav-item.admin-action {
    margin-top: 0.5rem;
}

.nav-item.admin-action:hover {
    background: rgba(255, 255, 255, 0.1);
}

.nav-item.logout-action:hover {
    background: rgba(220, 53, 69, 0.2);
    color: #ff6b7a;
}

.nav-item.logout-action:hover i {
    color: #dc3545;
}

/* Navigation separator */
.nav-separator {
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: 1rem 1.5rem;
}

/* ===== UNIFIED SIDEBAR USER SECTION ===== */
.sidebar-user-section .admin-user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.sidebar-user-section .admin-user-info:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.sidebar-user-section .admin-user-info .user-avatar {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #82877a 0%, #6b7062 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.2rem;
    box-shadow: 0 3px 10px rgba(130, 135, 122, 0.3);
    border: 2px solid white;
    transition: all 0.3s ease;
}

.sidebar-user-section .admin-user-info .user-avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(130, 135, 122, 0.4);
}

.sidebar-user-section .admin-user-info .user-details {
    flex: 1;
}

.sidebar-user-section .admin-user-info .user-name {
    font-weight: 700;
    color: white;
    font-size: 1rem;
    line-height: 1.2;
    margin-bottom: 0.2rem;
}

.sidebar-user-section .admin-user-info .user-role {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

/* ===== MOBILE HAMBURGER MENU (MOBILE ONLY) ===== */
.mobile-menu-btn {
    display: none; /* Hidden by default on desktop */
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.8rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 44px;
    height: 44px;
    align-items: center;
    justify-content: center;
}

.mobile-menu-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.hamburger-icon {
    width: 24px;
    height: 18px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.hamburger-line {
    width: 100%;
    height: 2px;
    background: white;
    border-radius: 2px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
}

.mobile-menu-btn.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-btn.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-btn.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* ===== ENHANCED SIDEBAR OVERLAY FOR MOBILE ===== */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6); /* Slightly darker for better contrast */
    z-index: var(--z-sidebar-overlay);
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(3px); /* Subtle blur effect */
    -webkit-backdrop-filter: blur(3px);
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* ===== MOBILE DROPDOWN SPECIFIC STYLES ===== */
.sidebar.mobile-dropdown {
    /* Enhanced mobile dropdown appearance */
    border: 2px solid rgba(255, 255, 255, 0.15);
    background: linear-gradient(135deg,
        rgba(18, 20, 20, 0.95) 0%,
        rgba(26, 31, 31, 0.95) 25%,
        rgba(44, 62, 80, 0.95) 50%,
        rgba(52, 73, 94, 0.95) 75%,
        rgba(44, 62, 80, 0.95) 100%);
}

.sidebar.mobile-dropdown::before {
    /* Enhanced top accent for mobile */
    height: 3px;
    background: linear-gradient(90deg,
        var(--color-primary) 0%,
        var(--color-primary-light) 50%,
        var(--color-primary) 100%);
}

/* ===== MOBILE DROPDOWN ANIMATION ENHANCEMENTS ===== */
@keyframes slideDownBounce {
    0% {
        transform: translateY(-100%);
        opacity: 0;
    }
    60% {
        transform: translateY(5px);
        opacity: 0.8;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideUpBounce {
    0% {
        transform: translateY(0);
        opacity: 1;
    }
    40% {
        transform: translateY(5px);
        opacity: 0.8;
    }
    100% {
        transform: translateY(-100%);
        opacity: 0;
    }
}

.sidebar.mobile-dropdown.active {
    animation: slideDownBounce 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.sidebar.mobile-dropdown:not(.active) {
    animation: slideUpBounce 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* ===== MOBILE SIDEBAR LAYERING FIXES ===== */
@media (max-width: 768px) {
    /* Ensure sidebar is always on top */
    .sidebar {
        z-index: var(--z-sidebar-mobile) !important;
        /* Force hardware acceleration for better performance */
        transform: translateY(-100%) translateZ(0);
        will-change: transform, opacity, visibility;
        /* Ensure proper stacking context */
        isolation: isolate;
    }

    .sidebar.active {
        transform: translateY(0) translateZ(0);
    }

    /* Ensure overlay is below sidebar but above everything else */
    .sidebar-overlay {
        z-index: var(--z-sidebar-overlay) !important;
    }

    /* Ensure hamburger button is always accessible */
    .mobile-menu-btn {
        z-index: var(--z-hamburger-btn) !important;
        /* Create new stacking context */
        isolation: isolate;
        /* Ensure it's always clickable */
        pointer-events: auto;
        position: relative;
    }

    /* Ensure top bar doesn't interfere */
    .top-bar {
        z-index: var(--z-top-bar) !important;
        /* Prevent interference with sidebar */
        isolation: isolate;
    }

    /* Ensure main content is below everything */
    .main-content {
        z-index: var(--z-content);
        position: relative;
        isolation: isolate;
    }
}

/* ===== DESKTOP BEHAVIOR (PERMANENT SIDEBAR) ===== */
@media (min-width: 769px) {
    .sidebar {
        /* Always visible on desktop */
        transform: translateY(0);
        visibility: visible;
        opacity: 1;
        position: fixed;
        top: 0;
        right: 0;
        width: 280px;
        height: 100vh;
    }

    .main-content {
        margin-right: 280px; /* Always accommodate sidebar */
        width: calc(100% - 280px);
    }

    .mobile-menu-btn {
        display: none !important; /* Never show on desktop */
    }

    .sidebar-overlay {
        display: none; /* No overlay needed on desktop */
    }
}

/* ===== MOBILE RESPONSIVE STYLES (COMPACT DROPDOWN BEHAVIOR) ===== */
@media (max-width: 768px) {
    .sidebar {
        /* Compact dropdown design for mobile */
        transform: translateY(-100%);
        width: 90%;
        max-width: 350px;
        height: auto;
        max-height: 70vh; /* Reduced height for better mobile experience */
        top: 65px; /* Positioned below top bar */
        right: 5%; /* Centered horizontally */
        left: 5%;
        margin: 0 auto;
        border-radius: 15px; /* Rounded corners for modern look */
        box-shadow:
            0 10px 40px rgba(0, 0, 0, 0.4),
            0 4px 20px rgba(0, 0, 0, 0.2),
            0 2px 10px rgba(0, 0, 0, 0.1);
        visibility: hidden;
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: fixed;
        z-index: var(--z-sidebar-mobile); /* Higher z-index for mobile */
        /* Compact scrolling */
        overflow-y: auto;
        overflow-x: hidden;
        /* Enhanced backdrop blur */
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        /* Border for definition */
        border: 2px solid rgba(255, 255, 255, 0.1);
    }

    .sidebar.active {
        /* Smooth slide down animation */
        transform: translateY(0);
        visibility: visible;
        opacity: 1;
    }

    .main-content {
        margin-right: 0; /* Full width on mobile */
        width: 100%;
        padding: 1.2rem; /* Slightly reduced padding */
    }

    .mobile-menu-btn,
    .hamburger-btn {
        display: flex !important; /* Show hamburger menu on mobile */
    }

    .top-bar {
        margin-bottom: 1.2rem;
        padding: 0.8rem 1.2rem;
        position: relative;
        z-index: var(--z-top-bar); /* Below sidebar but above content */
    }

    .page-title {
        font-size: 1.4rem;
    }

    .page-title i {
        font-size: 1.2rem;
    }

    /* Compact mobile sidebar sections */
    .sidebar-section.brand-section {
        padding: 1.2rem 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .brand-logo h2 {
        font-size: 1.3rem;
        margin-bottom: 0.3rem;
    }

    .brand-subtitle {
        font-size: 0.75rem;
        opacity: 0.8;
    }

    .sidebar-section.dashboard-section,
    .sidebar-section.unified-navigation {
        padding: 0.8rem 0;
        flex: 1;
    }

    .section-header {
        display: none; /* Hide section headers for compact design */
    }

    .sidebar-nav {
        padding: 0 0.6rem;
    }

    .sidebar-link {
        padding: 0.7rem 0.8rem;
        font-size: 0.9rem;
        margin-bottom: 0.2rem;
        border-radius: 8px;
    }

    .sidebar-section.admin-section {
        padding: 0.8rem;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        margin-top: auto; /* Push to bottom */
    }

    .sidebar-user-info {
        padding: 0.6rem;
        gap: 0.5rem;
        border-radius: 8px;
    }

    .user-avatar {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .user-name {
        font-size: 0.85rem;
    }

    .user-role {
        font-size: 0.75rem;
    }
}

/* ===== SMALL MOBILE STYLES (EXTRA COMPACT) ===== */
@media (max-width: 480px) {
    .main-content {
        padding: 0.8rem;
    }

    .top-bar {
        padding: 0.6rem 0.8rem;
        margin-bottom: 0.8rem;
        border-radius: 10px;
    }

    .page-title {
        font-size: 1.2rem;
    }

    .page-title i {
        font-size: 1rem;
    }

    .mobile-menu-btn {
        width: 38px;
        height: 38px;
        padding: 0.5rem;
    }

    .sidebar {
        width: 95%;
        max-width: 320px;
        max-height: 65vh; /* Even more compact for small screens */
        top: 55px;
        right: 2.5%;
        left: 2.5%;
        border-radius: 12px;
    }

    .sidebar-section.brand-section {
        padding: 0.8rem 0.6rem;
    }

    .brand-logo h2 {
        font-size: 1.1rem;
    }

    .brand-logo i {
        width: 40px;
        height: 40px;
        font-size: 1.8rem;
    }

    .brand-subtitle {
        font-size: 0.7rem;
    }

    .sidebar-nav {
        padding: 0 0.4rem;
    }

    .sidebar-link {
        padding: 0.6rem 0.6rem;
        font-size: 0.85rem;
        gap: 0.6rem;
    }

    .sidebar-link i {
        font-size: 1rem;
        width: 18px;
    }

    .sidebar-section.admin-section {
        padding: 0.6rem;
    }

    .sidebar-user-info {
        padding: 0.5rem;
        gap: 0.4rem;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
        font-size: 0.85rem;
    }

    .user-name {
        font-size: 0.8rem;
    }

    .user-role {
        font-size: 0.7rem;
    }
}

/* ===== TOUCH INTERACTION ENHANCEMENTS ===== */
@media (max-width: 768px) {
    /* Enhanced touch targets for mobile */
    .sidebar-link,
    .menu-item {
        min-height: 44px; /* WCAG AA compliant touch target */
        touch-action: manipulation; /* Prevent double-tap zoom */
        -webkit-tap-highlight-color: rgba(130, 135, 122, 0.3);
        position: relative;
        overflow: hidden;
    }

    /* Touch feedback animation */
    .sidebar-link::after,
    .menu-item::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.3s ease, height 0.3s ease;
        pointer-events: none;
        z-index: var(--z-base);
    }

    .sidebar-link:active::after,
    .menu-item:active::after {
        width: 100px;
        height: 100px;
    }

    /* Enhanced mobile menu button */
    .mobile-menu-btn {
        touch-action: manipulation;
        -webkit-tap-highlight-color: rgba(130, 135, 122, 0.3);
        transition: all 0.2s ease;
        z-index: var(--z-hamburger-btn); /* Ensure hamburger is always clickable */
        position: relative;
    }

    .mobile-menu-btn:active {
        transform: scale(0.95);
        background: rgba(255, 255, 255, 0.2);
    }

    /* Smooth scrolling for mobile sidebar */
    .sidebar {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
    }

    /* Custom scrollbar for mobile */
    .sidebar::-webkit-scrollbar {
        width: 4px;
    }

    .sidebar::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 2px;
    }

    .sidebar::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 2px;
    }

    .sidebar::-webkit-scrollbar-thumb:active {
        background: rgba(255, 255, 255, 0.4);
    }

    /* ===== ENSURE ALL SIDEBAR ELEMENTS ARE CLICKABLE ===== */
    .sidebar * {
        /* Ensure all elements inside sidebar are interactive */
        pointer-events: auto;
        /* Create proper stacking context */
        position: relative;
        z-index: 1;
    }

    .sidebar-link,
    .menu-item,
    .sidebar-user-info,
    .user-avatar,
    .brand-logo {
        /* Ensure interactive elements are always clickable */
        pointer-events: auto !important;
        cursor: pointer;
        /* Ensure proper layering */
        z-index: 2;
        position: relative;
    }

    /* Prevent any overlay from blocking interactions */
    .sidebar::before,
    .sidebar::after {
        pointer-events: none;
        z-index: 0;
    }
}

@media (min-width: 769px) {
    .sidebar-overlay {
        display: none;
    }
}

/* ===== CONTENT CONTAINERS ===== */
.dashboard-content,
.products-content,
.orders-content,
.content-content,
.settings-content {
    padding: 0; /* Remove extra padding since main-content has padding */
}

/* ===== STANDARDIZED TOP BAR SYSTEM ===== */
.top-bar {
    background: var(--gradient-secondary);
    color: var(--color-text-inverse);
    padding: var(--spacing-lg) var(--spacing-xl);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-xl);
    display: flex;
    justify-content: flex-start;
    align-items: center;
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
    min-height: 60px; /* STANDARDIZED height */
    font-family: 'Cairo', sans-serif;
    transition: var(--transition-base);
    z-index: var(--z-top-bar); /* Ensure proper layering */
}

.top-bar:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.top-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    opacity: 0.8;
}

.top-bar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: var(--spacing-lg);
}

.top-bar-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex: 1;
}

.top-bar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* STANDARDIZED page title section */
.page-title-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex: 1;
}

.page-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-text-inverse);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-family: 'Cairo', sans-serif;
}

.page-title i {
    font-size: 1.5rem;
    color: var(--color-primary-light);
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-base);
}

.page-title:hover i {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.2);
}

/* STANDARDIZED top bar actions */
.top-bar-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.page-title {
    font-size: 2.2rem;
    font-weight: 700;
    color: white;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.page-title i {
    color: #82877a;
    font-size: 2rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.8rem 1.2rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.user-info:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.user-avatar {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #82877a 0%, #6b7062 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.2rem;
    box-shadow: 0 3px 10px rgba(130, 135, 122, 0.3);
    border: 2px solid white;
    transition: all 0.3s ease;
}

.user-avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(130, 135, 122, 0.4);
}

.user-details {
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
}

.user-name {
    font-weight: 700;
    color: white;
    font-size: 1rem;
    line-height: 1.2;
}

.user-role {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

/* ===== SIMPLIFIED USER INFO COMPONENT ===== */
.user-info-simple {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.6rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.user-info-simple:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.user-info-simple .user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #82877a 0%, #6b7062 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.1rem;
    box-shadow: 0 3px 10px rgba(130, 135, 122, 0.3);
    border: 2px solid white;
    transition: all 0.3s ease;
}

.user-info-simple .user-avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(130, 135, 122, 0.4);
}

.user-info-simple .user-details {
    display: flex;
    flex-direction: column;
}

.user-info-simple .user-name {
    font-weight: 600;
    color: white;
    font-size: 0.95rem;
    line-height: 1.2;
}

/* ===== LOGOUT BUTTON ===== */
.logout-btn {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    border: none;
    padding: 0.6rem 1.2rem;
    border-radius: 20px;
    font-family: 'Cairo', sans-serif;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 3px 10px rgba(220, 53, 69, 0.3);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.logout-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.logout-btn:hover::before {
    left: 100%;
}

.logout-btn:hover {
    background: linear-gradient(135deg, #e74c3c 0%, #dc3545 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
    border-color: rgba(255, 255, 255, 0.3);
}

.logout-btn:active {
    transform: translateY(0);
    box-shadow: 0 3px 10px rgba(220, 53, 69, 0.3);
}

.logout-btn i {
    font-size: 0.9rem;
    transition: transform 0.3s ease;
}

.logout-btn:hover i {
    transform: rotate(10deg);
}

/* ===== TOP BAR LOGOUT BUTTON (Legacy) ===== */
.top-bar-logout-btn {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 3px 10px rgba(220, 53, 69, 0.3);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.top-bar-logout-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.top-bar-logout-btn:hover::before {
    left: 100%;
}

.top-bar-logout-btn:hover {
    background: linear-gradient(135deg, #e74c3c 0%, #dc3545 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
    border-color: rgba(255, 255, 255, 0.3);
}

.top-bar-logout-btn:active {
    transform: translateY(0);
    box-shadow: 0 3px 10px rgba(220, 53, 69, 0.3);
}

.top-bar-logout-btn i {
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.top-bar-logout-btn:hover i {
    transform: rotate(10deg);
}

/* ===== ENHANCED PROFESSIONAL CARD SYSTEM ===== */
.card {
    /* Enhanced background with subtle texture */
    background: var(--gradient-card);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    /* Enhanced multi-layer shadow system */
    box-shadow:
        var(--shadow-md),
        0 1px 3px rgba(74, 144, 164, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);
    border: 1px solid var(--color-primary-alpha-10);
    transition: var(--transition-base);
    margin-bottom: var(--spacing-xl);
    position: relative;
    overflow: hidden;
    /* Performance optimizations */
    will-change: transform, box-shadow;
    contain: layout style;
    font-family: 'Cairo', sans-serif;
}

/* Enhanced card interaction states */
.card:hover {
    transform: translateY(-4px) scale(1.01);
    box-shadow:
        var(--shadow-xl),
        0 8px 32px rgba(130, 135, 122, 0.2),
        0 4px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.7);
    border-color: var(--color-primary-alpha-20);
}

.card:hover::before {
    opacity: 1;
}

/* Card content structure */
.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    background: rgba(130, 135, 122, 0.02);
    border-top: 1px solid var(--color-primary-alpha-10);
    margin: var(--spacing-lg) calc(-1 * var(--spacing-xl)) calc(-1 * var(--spacing-xl));
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

/* ===== UNIFIED WELCOME SECTION STYLING ===== */
.welcome-section {
    margin-bottom: var(--spacing-xl);
}

.welcome-section .card {
    background: linear-gradient(135deg, var(--color-bg-primary) 0%, #f8f9fa 100%);
    border: 1px solid rgba(130, 135, 122, 0.1);
    padding: var(--spacing-xl);
}

.welcome-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: var(--shadow-md);
    transition: var(--transition-base);
}

.welcome-section .card:hover .welcome-icon {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.welcome-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

/* ===== UNIFIED MESSAGE SYSTEM ===== */
.messages-container {
    margin-bottom: var(--spacing-lg);
}

.message {
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
}

.message::before {
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    font-size: 1.1rem;
}

.message.success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
    color: var(--color-success);
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.message.success::before {
    content: '\f058'; /* fa-check-circle */
}

.message.error {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%);
    color: var(--color-danger);
    border: 1px solid rgba(220, 53, 69, 0.2);
}

.message.error::before {
    content: '\f06a'; /* fa-exclamation-circle */
}

.message.info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(23, 162, 184, 0.05) 100%);
    color: var(--color-info);
    border: 1px solid rgba(23, 162, 184, 0.2);
}

.message.info::before {
    content: '\f05a'; /* fa-info-circle */
}

.message.warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
    color: var(--color-warning);
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.message.warning::before {
    content: '\f071'; /* fa-exclamation-triangle */
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    /* Enhanced gradient with more stops */
    background: linear-gradient(90deg,
        var(--color-primary) 0%,
        var(--color-primary-dark) 25%,
        var(--color-primary-light) 50%,
        var(--color-primary-dark) 75%,
        var(--color-primary) 100%);
    opacity: 0;
    transition: var(--transition-base);
    /* Subtle animation */
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { background-position: 200% 0; }
    50% { background-position: -200% 0; }
}

.card:hover {
    transform: translateY(-6px) scale(1.01);
    /* Enhanced hover shadow with multiple layers */
    box-shadow:
        var(--shadow-xl),
        0 8px 32px rgba(74, 144, 164, 0.2),
        0 4px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.7);
    border-color: rgba(74, 144, 164, 0.2);
}

.card:hover::before {
    opacity: 1;
}

/* Card variants */
.card-elevated {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-flat {
    box-shadow: var(--shadow-sm);
    border: 2px solid rgba(130, 135, 122, 0.1);
}

.card-outlined {
    background: transparent;
    border: 2px solid var(--color-primary);
    box-shadow: none;
}

.card-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--color-primary-alpha-10);
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    right: 0;
    width: 60px;
    height: 2px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xs);
}

.card-header h1,
.card-header h2,
.card-header h3 {
    color: var(--color-text-primary);
    font-weight: var(--font-weight-bold);
    margin: 0;
    flex: 1;
    font-family: 'Cairo', sans-serif;
}

.card-header h1 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-extrabold);
}

.card-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
}

.card-header h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
}

.card-header i {
    color: #ffffff;
    font-size: 1.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    text-shadow: none;
}

/* Card header with icon styling */
.card-header .section-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-text-inverse);
    font-size: 1.3rem;
    box-shadow: var(--shadow-brand);
    transition: var(--transition-base);
}

.card:hover .card-header .section-icon {
    transform: scale(1.05) rotate(5deg);
    box-shadow: var(--shadow-brand-lg);
}

/* Card actions */
.card-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.card-actions .toggle-btn {
    background: var(--color-primary-alpha-10);
    border: 1px solid var(--color-primary-alpha-20);
    color: var(--color-primary);
    width: 36px;
    height: 36px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-base);
}

.card-actions .toggle-btn:hover {
    background: var(--color-primary);
    color: var(--color-text-inverse);
    transform: scale(1.05);
}

/* ===== ENHANCED STATS CARDS SYSTEM ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl) var(--spacing-lg);
    box-shadow:
        var(--shadow-md),
        0 2px 8px rgba(130, 135, 122, 0.1);
    transition: var(--transition-base);
    border: 1px solid rgba(130, 135, 122, 0.1);
    position: relative;
    overflow: hidden;
    min-height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 50%, var(--color-primary) 100%);
    opacity: 0.8;
}

.stat-card:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow:
        var(--shadow-xl),
        0 8px 32px rgba(130, 135, 122, 0.2);
    border-color: rgba(130, 135, 122, 0.2);
}

.stat-card:hover::before {
    opacity: 1;
    background-size: 200% 100%;
    animation: shimmer 2s ease-in-out infinite;
}

/* Unified stat card layouts - both with and without header */
.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
}

.stat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* For cards without header (simplified layout) */
.stat-card:not(:has(.stat-header)) {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
}

.stat-card:not(:has(.stat-header)) .stat-icon {
    flex-shrink: 0;
}

.stat-card:not(:has(.stat-header)) .stat-content {
    flex: 1;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    box-shadow: 0 4px 15px rgba(130, 135, 122, 0.3);
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 20px rgba(130, 135, 122, 0.4);
}

.stat-card:hover .stat-icon::before {
    opacity: 1;
}

/* Stat icon color variants */
.stat-icon.orders { background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); }
.stat-icon.products { background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); }
.stat-icon.sales { background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); }
.stat-icon.customers { background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%); }
.stat-icon.total { background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%); }
.stat-icon.pending { background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); }
.stat-icon.completed { background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); }
.stat-icon.cancelled { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }
.stat-icon.confirmed { background: linear-gradient(135deg, #20c997 0%, #1aa179 100%); }
.stat-icon.shipped { background: linear-gradient(135deg, #6f42c1 0%, #59359a 100%); }
.stat-icon.faq { background: linear-gradient(135deg, #fd7e14 0%, #e8590c 100%); }
.stat-icon.guidelines { background: linear-gradient(135deg, #6610f2 0%, #520dc2 100%); }
.stat-icon.tips { background: linear-gradient(135deg, #e83e8c 0%, #d91a72 100%); }
.stat-icon.terms { background: linear-gradient(135deg, #6c757d 0%, #545b62 100%); }

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--color-text-primary);
    margin-bottom: 0.5rem;
    line-height: 1.2;
    transition: var(--transition-fast);
}

.stat-card:hover .stat-value {
    color: var(--color-primary);
}

.stat-label {
    color: var(--color-text-secondary);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.3rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-description {
    color: var(--color-text-muted);
    font-size: 0.8rem;
    font-weight: 400;
    line-height: 1.4;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.4rem 0.8rem;
    border-radius: 25px;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    transition: var(--transition-base);
}

.stat-change:hover {
    transform: scale(1.05);
}

.stat-change.positive {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.15) 0%, rgba(40, 167, 69, 0.05) 100%);
    color: var(--color-success);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.stat-change.negative {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.15) 0%, rgba(220, 53, 69, 0.05) 100%);
    color: var(--color-danger);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.stat-change.neutral {
    background: linear-gradient(135deg, rgba(130, 135, 122, 0.15) 0%, rgba(130, 135, 122, 0.05) 100%);
    color: var(--color-primary);
    border: 1px solid rgba(130, 135, 122, 0.3);
}

/* ===== PROFESSIONAL FORM SYSTEM ===== */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
}

.form-section {
    background: linear-gradient(135deg, var(--color-bg-primary) 0%, #fafbfc 100%);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(130, 135, 122, 0.1);
    margin-bottom: var(--spacing-xl);
    position: relative;
    overflow: hidden;
}

.form-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 50%, var(--color-primary) 100%);
    opacity: 0.8;
}

.form-section:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.form-section h3 {
    color: var(--color-text-primary);
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid rgba(130, 135, 122, 0.1);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.form-section h3 i {
    color: var(--color-primary);
    font-size: 1.2rem;
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.form-group {
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
    color: var(--color-text-primary);
    font-size: 0.9rem;
    position: relative;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-group label.required::after {
    content: '*';
    color: var(--color-danger);
    margin-right: var(--spacing-xs);
    font-weight: 700;
    font-size: 1.1rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid rgba(130, 135, 122, 0.2);
    border-radius: var(--radius-md);
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    transition: var(--transition-base);
    min-height: 44px;
    background: linear-gradient(135deg, var(--color-bg-primary) 0%, #fafbfc 100%);
    color: var(--color-text-primary);
    position: relative;
}

.form-group input::placeholder,
.form-group select::placeholder,
.form-group textarea::placeholder {
    color: var(--color-text-muted);
    font-style: italic;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow:
        0 0 0 4px rgba(130, 135, 122, 0.15),
        var(--shadow-md);
    transform: translateY(-2px);
    background: var(--color-bg-primary);
}

.form-group input:valid,
.form-group select:valid,
.form-group textarea:valid {
    border-color: var(--color-success);
}

.form-group input:invalid:not(:placeholder-shown),
.form-group select:invalid:not(:placeholder-shown),
.form-group textarea:invalid:not(:placeholder-shown) {
    border-color: var(--color-danger);
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
    line-height: 1.6;
    font-family: 'Cairo', sans-serif;
}

/* Enhanced form validation feedback */
.form-group .form-feedback {
    margin-top: var(--spacing-xs);
    font-size: 0.8rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.form-group .form-feedback.success {
    color: var(--color-success);
}

.form-group .form-feedback.error {
    color: var(--color-danger);
}

.form-group .form-feedback::before {
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
}

.form-group .form-feedback.success::before {
    content: '\f058'; /* fa-check-circle */
}

.form-group .form-feedback.error::before {
    content: '\f06a'; /* fa-exclamation-circle */
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 1rem;
}

.checkbox-group input[type="checkbox"] {
    width: 20px;
    height: 20px;
    margin: 0;
    accent-color: #82877a;
}

.checkbox-group label {
    margin: 0;
    font-weight: 500;
    cursor: pointer;
}

/* ===== ENHANCED PROFESSIONAL BUTTON SYSTEM ===== */
.btn {
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-md);
    font-family: 'Cairo', -apple-system, BlinkMacSystemFont, sans-serif;
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: var(--transition-base);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-base);
    min-height: 44px; /* WCAG AA compliant touch target */
    text-decoration: none;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
    /* Performance optimizations */
    will-change: transform, box-shadow;
    contain: layout style;
    /* Better text rendering */
    text-rendering: optimizeLegibility;
    /* Prevent text selection */
    user-select: none;
    -webkit-user-select: none;
    /* Better touch experience */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1;
}

/* Button size variants */
.btn-xs {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    min-height: 32px;
    border-radius: var(--radius-sm);
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    min-height: 36px;
    border-radius: var(--radius-sm);
}

.btn-lg {
    padding: var(--spacing-lg) var(--spacing-xxl);
    font-size: var(--font-size-lg);
    min-height: 52px;
    border-radius: var(--radius-lg);
}

.btn-xl {
    padding: var(--spacing-xl) var(--spacing-xxxl);
    font-size: var(--font-size-xl);
    min-height: 60px;
    border-radius: var(--radius-xl);
}

/* Button width variants */
.btn-block {
    width: 100%;
    display: flex;
}

.btn-auto {
    width: auto;
}

/* Button icon styling */
.btn i {
    font-size: 1.1em;
    transition: var(--transition-fast);
}

.btn:hover i {
    transform: scale(1.1);
}

.btn-icon-only {
    padding: var(--spacing-md);
    width: 44px;
    height: 44px;
    border-radius: var(--radius-full);
}

.btn-icon-only i {
    margin: 0;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent);
    transition: var(--transition-slow);
    pointer-events: none;
}

.btn:hover::before {
    left: 100%;
}

/* Enhanced button states */
.btn:active {
    transform: translateY(1px) scale(0.98);
    transition: var(--transition-fast);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    pointer-events: none;
}

.btn:disabled::before {
    display: none;
}

/* Enhanced button variants with improved gradients */
.btn-primary {
    background: linear-gradient(135deg,
        var(--color-primary) 0%,
        var(--color-primary-dark) 50%,
        var(--color-primary) 100%);
    color: var(--color-text-inverse);
    box-shadow: var(--shadow-md), 0 4px 15px rgba(74, 144, 164, 0.3);
    border-color: var(--color-primary);
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-lg), 0 8px 25px rgba(74, 144, 164, 0.4);
    border-color: rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg,
        var(--color-primary-light) 0%,
        var(--color-primary) 50%,
        var(--color-primary-dark) 100%);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 50%, #495057 100%);
    color: var(--color-text-inverse);
    box-shadow: var(--shadow-md), 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-lg), 0 8px 25px rgba(108, 117, 125, 0.4);
    background: linear-gradient(135deg, #7c8287 0%, #6c757d 50%, #5a6268 100%);
}

.btn-success {
    background: linear-gradient(135deg,
        var(--color-success) 0%,
        #20c997 50%,
        #17a2b8 100%);
    color: var(--color-text-inverse);
    box-shadow: var(--shadow-md), 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-lg), 0 8px 25px rgba(40, 167, 69, 0.4);
    background: linear-gradient(135deg, #34ce57 0%, var(--color-success) 50%, #20c997 100%);
}

.btn-danger {
    background: linear-gradient(135deg,
        var(--color-danger) 0%,
        #c82333 50%,
        #bd2130 100%);
    color: var(--color-text-inverse);
    box-shadow: var(--shadow-md), 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-danger:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-lg), 0 8px 25px rgba(220, 53, 69, 0.4);
    background: linear-gradient(135deg, #e74c3c 0%, var(--color-danger) 50%, #c82333 100%);
}

.btn-warning {
    background: linear-gradient(135deg,
        var(--color-warning) 0%,
        #e0a800 50%,
        #d39e00 100%);
    color: #212529;
    box-shadow: var(--shadow-md), 0 4px 15px rgba(255, 193, 7, 0.3);
}

.btn-warning:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-lg), 0 8px 25px rgba(255, 193, 7, 0.4);
    background: linear-gradient(135deg, #ffcd39 0%, var(--color-warning) 50%, #e0a800 100%);
}

.btn-info {
    background: linear-gradient(135deg,
        var(--color-info) 0%,
        #138496 50%,
        #117a8b 100%);
    color: var(--color-text-inverse);
    box-shadow: var(--shadow-md), 0 4px 15px rgba(23, 162, 184, 0.3);
}

.btn-info:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-lg), 0 8px 25px rgba(23, 162, 184, 0.4);
    background: linear-gradient(135deg, #1dd1a1 0%, var(--color-info) 50%, #138496 100%);
}

.btn-outline-primary {
    background: transparent;
    color: #82877a;
    border-color: #82877a;
}

.btn-outline-primary:hover {
    background: #82877a;
    color: white;
    transform: translateY(-3px);
}

.btn:active {
    transform: translateY(-1px);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

.btn:disabled::before {
    display: none;
}

.btn-sm {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
    min-height: 36px;
}

.btn-lg {
    padding: 1.2rem 2.5rem;
    font-size: 1.1rem;
    min-height: 52px;
}

/* ===== ENHANCED PROFESSIONAL TABLE SYSTEM ===== */
.table-container {
    background: linear-gradient(135deg, var(--color-bg-primary) 0%, #fafbfc 100%);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow:
        var(--shadow-md),
        0 4px 16px rgba(130, 135, 122, 0.1);
    border: 1px solid rgba(130, 135, 122, 0.1);
    margin-bottom: var(--spacing-xl);
    position: relative;
}

.table-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 50%, var(--color-primary) 100%);
    opacity: 0.8;
}

.table {
    width: 100%;
    border-collapse: collapse;
    font-size: 1rem;
    font-family: 'Cairo', sans-serif;
}

.table th,
.table td {
    padding: var(--spacing-lg) var(--spacing-md);
    text-align: right;
    border-bottom: 1px solid rgba(130, 135, 122, 0.08);
    vertical-align: middle;
    transition: var(--transition-fast);
}

.table th {
    background: linear-gradient(135deg,
        var(--color-secondary) 0%,
        #1a1c1c 25%,
        #2c3e50 50%,
        #34495e 75%,
        #2c3e50 100%);
    color: var(--color-text-inverse);
    font-weight: 700;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-bottom: 2px solid var(--color-primary);
}

.table th:first-child {
    border-top-right-radius: var(--radius-md);
}

.table th:last-child {
    border-top-left-radius: var(--radius-md);
}

.table tbody tr {
    transition: var(--transition-base);
    position: relative;
}

.table tbody tr::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--color-primary);
    transform: scaleY(0);
    transition: var(--transition-base);
}

.table tbody tr:hover {
    background: linear-gradient(135deg,
        rgba(130, 135, 122, 0.08) 0%,
        rgba(130, 135, 122, 0.04) 50%,
        rgba(130, 135, 122, 0.02) 100%);
    transform: translateX(-4px);
    box-shadow: 0 4px 12px rgba(130, 135, 122, 0.1);
}

.table tbody tr:hover::before {
    transform: scaleY(1);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

.table tbody tr:nth-child(even) {
    background: rgba(130, 135, 122, 0.02);
}

.table tbody tr:nth-child(even):hover {
    background: linear-gradient(135deg,
        rgba(130, 135, 122, 0.12) 0%,
        rgba(130, 135, 122, 0.06) 50%,
        rgba(130, 135, 122, 0.03) 100%);
}

/* Enhanced table cell content styling */
.table td {
    color: var(--color-text-primary);
    font-weight: 500;
}

.table td:first-child {
    font-weight: 600;
    color: var(--color-primary);
}

/* Product image styling in tables */
.product-image {
    width: 50px;
    height: 50px;
    border-radius: var(--radius-sm);
    object-fit: cover;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-base);
}

.product-image:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

/* Price display in tables */
.price-display {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.2rem;
}

.original-price {
    text-decoration: line-through;
    color: var(--color-text-muted);
    font-size: 0.85rem;
}

.sale-price {
    color: var(--color-danger);
    font-weight: 700;
    font-size: 1.1rem;
}

.regular-price {
    color: var(--color-text-primary);
    font-weight: 600;
    font-size: 1rem;
}

/* ===== PROFESSIONAL STATUS BADGES ===== */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.status-badge::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
}

.status-badge.active {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.15) 0%, rgba(40, 167, 69, 0.05) 100%);
    color: #28a745;
    border-color: rgba(40, 167, 69, 0.3);
}

.status-badge.inactive {
    background: linear-gradient(135deg, rgba(108, 117, 125, 0.15) 0%, rgba(108, 117, 125, 0.05) 100%);
    color: #6c757d;
    border-color: rgba(108, 117, 125, 0.3);
}

.status-badge.pending {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.15) 0%, rgba(255, 193, 7, 0.05) 100%);
    color: #ffc107;
    border-color: rgba(255, 193, 7, 0.3);
}

.status-badge.confirmed {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.15) 0%, rgba(40, 167, 69, 0.05) 100%);
    color: #28a745;
    border-color: rgba(40, 167, 69, 0.3);
}

.status-badge.cancelled {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.15) 0%, rgba(220, 53, 69, 0.05) 100%);
    color: #dc3545;
    border-color: rgba(220, 53, 69, 0.3);
}

.status-badge.featured {
    background: linear-gradient(135deg, rgba(130, 135, 122, 0.15) 0%, rgba(130, 135, 122, 0.05) 100%);
    color: #82877a;
    border-color: rgba(130, 135, 122, 0.3);
}

.status-badge.offer {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.15) 0%, rgba(255, 193, 7, 0.05) 100%);
    color: #ffc107;
    border-color: rgba(255, 193, 7, 0.3);
}

.status-badge.shipped {
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.15) 0%, rgba(0, 123, 255, 0.05) 100%);
    color: #007bff;
    border-color: rgba(0, 123, 255, 0.3);
}

.status-badge.delivered {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.15) 0%, rgba(40, 167, 69, 0.05) 100%);
    color: #28a745;
    border-color: rgba(40, 167, 69, 0.3);
}

.status-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* ===== ACTION BUTTONS ===== */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    justify-content: center;
}

.action-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    font-size: 0.9rem;
}

.action-btn.edit {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.action-btn.edit:hover {
    background: #28a745;
    color: white;
    transform: scale(1.1);
}

.action-btn.delete {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.action-btn.delete:hover {
    background: #dc3545;
    color: white;
    transform: scale(1.1);
}

.action-btn.view {
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.action-btn.view:hover {
    background: #007bff;
    color: white;
    transform: scale(1.1);
}

/* ===== LOGOUT MODAL ANIMATIONS ===== */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* ===== ENHANCED MOBILE MENU BUTTON ===== */
.mobile-menu-btn {
    display: none; /* Hidden by default, shown on mobile via media query */
    background: linear-gradient(135deg, #82877a 0%, #6b7062 100%);
    border: none;
    color: white;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 12px;
    /* Cross-browser transitions */
    -webkit-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    -moz-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    -ms-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    -o-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    width: 48px;
    height: 48px;
    /* Cross-browser flexbox */
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -moz-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(130, 135, 122, 0.3);
    overflow: hidden;
    /* Cross-browser touch improvements */
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    /* Prevent text selection on double tap */
    -webkit-touch-callout: none;
    /* Force hardware acceleration */
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000;
    perspective: 1000;
}

.mobile-menu-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mobile-menu-btn:hover::before {
    opacity: 1;
}

.mobile-menu-btn:hover {
    -webkit-transform: translateY(-2px) scale(1.05) translateZ(0);
    -moz-transform: translateY(-2px) scale(1.05) translateZ(0);
    -ms-transform: translateY(-2px) scale(1.05) translateZ(0);
    -o-transform: translateY(-2px) scale(1.05) translateZ(0);
    transform: translateY(-2px) scale(1.05) translateZ(0);
    box-shadow: 0 6px 20px rgba(130, 135, 122, 0.4);
}

.mobile-menu-btn:active {
    -webkit-transform: translateY(0) scale(0.98) translateZ(0);
    -moz-transform: translateY(0) scale(0.98) translateZ(0);
    -ms-transform: translateY(0) scale(0.98) translateZ(0);
    -o-transform: translateY(0) scale(0.98) translateZ(0);
    transform: translateY(0) scale(0.98) translateZ(0);
    box-shadow: 0 2px 8px rgba(130, 135, 122, 0.3);
}

/* Hamburger Icon Container */
.hamburger-icon {
    width: 24px;
    height: 18px;
    position: relative;
    /* Cross-browser flexbox */
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -moz-box-orient: vertical;
    -moz-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -moz-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    /* Cross-browser transitions */
    -webkit-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    -moz-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    -ms-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    -o-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hamburger Lines */
.hamburger-line {
    width: 100%;
    height: 2px;
    background: white;
    border-radius: 2px;
    /* Cross-browser transitions */
    -webkit-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    -moz-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    -ms-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    -o-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    /* Cross-browser transform origin */
    -webkit-transform-origin: center;
    -moz-transform-origin: center;
    -ms-transform-origin: center;
    -o-transform-origin: center;
    transform-origin: center;
    /* Force hardware acceleration */
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
    transform: translateZ(0);
}

.hamburger-line:nth-child(1) {
    -webkit-transform-origin: top left;
    -moz-transform-origin: top left;
    -ms-transform-origin: top left;
    -o-transform-origin: top left;
    transform-origin: top left;
}

.hamburger-line:nth-child(3) {
    -webkit-transform-origin: bottom left;
    -moz-transform-origin: bottom left;
    -ms-transform-origin: bottom left;
    -o-transform-origin: bottom left;
    transform-origin: bottom left;
}

/* Active State - Transform to X */
.mobile-menu-btn.active .hamburger-line:nth-child(1) {
    -webkit-transform: rotate(45deg) translate(3px, 3px) translateZ(0);
    -moz-transform: rotate(45deg) translate(3px, 3px) translateZ(0);
    -ms-transform: rotate(45deg) translate(3px, 3px) translateZ(0);
    -o-transform: rotate(45deg) translate(3px, 3px) translateZ(0);
    transform: rotate(45deg) translate(3px, 3px) translateZ(0);
}

.mobile-menu-btn.active .hamburger-line:nth-child(2) {
    opacity: 0;
    -webkit-transform: scaleX(0) translateZ(0);
    -moz-transform: scaleX(0) translateZ(0);
    -ms-transform: scaleX(0) translateZ(0);
    -o-transform: scaleX(0) translateZ(0);
    transform: scaleX(0) translateZ(0);
}

.mobile-menu-btn.active .hamburger-line:nth-child(3) {
    -webkit-transform: rotate(-45deg) translate(3px, -3px) translateZ(0);
    -moz-transform: rotate(-45deg) translate(3px, -3px) translateZ(0);
    -ms-transform: rotate(-45deg) translate(3px, -3px) translateZ(0);
    -o-transform: rotate(-45deg) translate(3px, -3px) translateZ(0);
    transform: rotate(-45deg) translate(3px, -3px) translateZ(0);
}

/* Active State Button */
.mobile-menu-btn.active {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.mobile-menu-btn.active:hover {
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

/* ===== SIDEBAR OVERLAY FOR MOBILE ===== */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(2px);
    /* Prevent scrolling on background */
    overscroll-behavior: contain;
    /* Improve touch handling */
    touch-action: none;
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Prevent body scroll when sidebar is open */
body.sidebar-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
}

/* ===== HOMEPAGE TABS SYSTEM ===== */

/* Homepage Tabs Container */
.homepage-tabs-container {
    background: var(--color-bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: 2rem;
}

/* Homepage Tab Navigation */
.homepage-tabs-nav {
    display: flex;
    flex-wrap: wrap;
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    padding: 0.5rem;
    gap: 0.5rem;
    border-bottom: 1px solid var(--color-border);
}

.homepage-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.2rem;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-family: var(--font-family);
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-height: 44px;
    backdrop-filter: blur(10px);
}

.homepage-tab:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.homepage-tab.active {
    background: white;
    color: var(--color-primary);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.homepage-tab i {
    font-size: 1rem;
    opacity: 0.9;
}

.homepage-tab.active i {
    opacity: 1;
}

/* Homepage Tab Content */
.homepage-tabs-content {
    padding: 2rem;
    background: var(--color-bg-primary);
    min-height: 400px;
}

.homepage-tab-content {
    display: none !important;
    animation: fadeIn 0.3s ease;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.homepage-tab-content.active {
    display: block !important;
    opacity: 1;
    transform: translateY(0);
}

/* Enhanced Card Styling for Homepage Tabs */
.homepage-tab-content .card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--color-border);
    overflow: hidden;
    transition: all 0.3s ease;
}

.homepage-tab-content .card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.homepage-tab-content .card-header {
    background: linear-gradient(135deg, var(--color-bg-secondary) 0%, #f8f9fa 100%);
    padding: 2rem;
    border-bottom: 1px solid var(--color-border);
    text-align: center;
}

.homepage-tab-content .card-header h3 {
    color: var(--color-text-primary);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
}

.homepage-tab-content .card-header h3 i {
    color: var(--color-primary);
    font-size: 1.3rem;
}

.homepage-tab-content .card-description {
    color: var(--color-text-secondary);
    font-size: 1rem;
    margin: 0;
    line-height: 1.6;
}

.homepage-tab-content .card-body {
    padding: 2rem;
}

/* Form Enhancements for Homepage Tabs */
.homepage-tab-content .form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.homepage-tab-content .form-group {
    margin-bottom: 1.5rem;
}

.homepage-tab-content .form-group label {
    display: block;
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.homepage-tab-content .form-group input,
.homepage-tab-content .form-group textarea,
.homepage-tab-content .form-group select {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 2px solid var(--color-border);
    border-radius: var(--radius-md);
    font-family: var(--font-family);
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: white;
}

.homepage-tab-content .form-group input:focus,
.homepage-tab-content .form-group textarea:focus,
.homepage-tab-content .form-group select:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px var(--color-primary-alpha-10);
    transform: translateY(-1px);
}

.homepage-tab-content .btn {
    padding: 0.9rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
    min-height: 48px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.homepage-tab-content .btn-primary {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    color: white;
    border: none;
    box-shadow: var(--shadow-sm);
}

.homepage-tab-content .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* ===== HOMEPAGE TABS RESPONSIVE DESIGN ===== */

/* Large Screens - Optimize tab layout */
@media (max-width: 1200px) {
    .homepage-tabs-nav {
        justify-content: center;
    }

    .homepage-tab {
        flex: 1;
        min-width: 140px;
        justify-content: center;
    }

    .homepage-tab span {
        font-size: 0.85rem;
    }
}

/* Tablet Screens - Stack tabs vertically */
@media (max-width: 768px) {
    .homepage-tabs-nav {
        flex-direction: column;
        gap: 0.3rem;
    }

    .homepage-tab {
        width: 100%;
        justify-content: flex-start;
        padding: 1rem 1.5rem;
    }

    .homepage-tabs-content {
        padding: 1rem;
    }

    .homepage-tab-content .card-header {
        padding: 1.5rem;
    }

    .homepage-tab-content .card-body {
        padding: 1.5rem;
    }

    .homepage-tab-content .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* Mobile Screens - Compact design */
@media (max-width: 480px) {
    .homepage-tab {
        padding: 0.8rem 1rem;
        font-size: 0.85rem;
    }

    .homepage-tab i {
        font-size: 0.9rem;
    }

    .homepage-tabs-content {
        padding: 0.5rem;
    }

    .homepage-tab-content .card-header {
        padding: 1rem;
    }

    .homepage-tab-content .card-header h3 {
        font-size: 1.2rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .homepage-tab-content .card-body {
        padding: 1rem;
    }
}

/* ===== COMPREHENSIVE RESPONSIVE DESIGN SYSTEM ===== */

/* Ultra-Wide Screens (>1400px) */
@media (min-width: 1401px) {
    .stats-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: 2rem;
    }

    .form-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .main-content {
        padding: 3rem;
        max-width: 1600px;
        margin-left: auto;
    }

    .card {
        padding: 2.5rem;
    }

    .table th,
    .table td {
        padding: 1.5rem 1.2rem;
    }
}

/* Large Screens (1201px-1400px) */
@media (max-width: 1400px) and (min-width: 1201px) {
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1.8rem;
    }

    .form-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .main-content {
        padding: 2.5rem;
    }

    .card {
        padding: 2.2rem;
    }
}

/* Desktop (769px - 1200px) */
@media (max-width: 1200px) and (min-width: 769px) {
    .sidebar {
        width: 260px;
    }

    .main-content {
        margin-right: 260px;
        padding: 2rem;
    }

    .main-content.sidebar-collapsed {
        margin-right: 70px;
    }

    .sidebar-header {
        padding: 1.5rem 1rem;
    }

    .sidebar-header h2 {
        font-size: 1.6rem;
    }

    .menu-item {
        padding: 0.8rem 1rem;
        font-size: 0.95rem;
    }

    .page-title {
        font-size: 2rem;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 1.5rem;
    }

    .form-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .card {
        padding: 1.8rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .btn {
        padding: 0.9rem 1.8rem;
    }
}

/* ===== TABLET RESPONSIVE STYLES (COMPACT DROPDOWN) ===== */
@media (max-width: 768px) and (min-width: 481px) {
    .sidebar {
        /* Compact dropdown for tablets */
        transform: translateY(-100%);
        width: 85%;
        max-width: 400px;
        height: auto;
        max-height: 75vh;
        top: 70px;
        right: 7.5%;
        left: 7.5%;
        margin: 0 auto;
        border-radius: 15px;
        box-shadow:
            0 12px 45px rgba(0, 0, 0, 0.35),
            0 6px 25px rgba(0, 0, 0, 0.2),
            0 3px 12px rgba(0, 0, 0, 0.1);
        visibility: hidden;
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: fixed;
        z-index: var(--z-sidebar-mobile); /* Same high z-index as mobile */
        overflow-y: auto;
        overflow-x: hidden;
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 2px solid rgba(255, 255, 255, 0.1);
    }

    .sidebar.active {
        transform: translateY(0);
        visibility: visible;
        opacity: 1;
    }

    .main-content {
        margin-right: 0;
        padding: 1.5rem;
        width: 100%;
    }

    .top-bar {
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        border-radius: 12px;
        position: relative;
        z-index: var(--z-top-bar); /* Consistent z-index */
    }

    .page-title {
        font-size: 1.6rem;
    }

    /* Tablet sidebar sections */
    .sidebar-section.brand-section {
        padding: 1.5rem 1.2rem;
    }

    .brand-logo h2 {
        font-size: 1.4rem;
    }

    .sidebar-nav {
        padding: 0 1rem;
    }

    .sidebar-link {
        padding: 0.8rem 1rem;
        font-size: 0.95rem;
    }

    .sidebar-section.admin-section {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.2rem;
    }

    .stat-card {
        padding: 1.5rem 1.2rem;
    }

    .stat-value {
        font-size: 2.2rem;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .card {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .table-container {
        overflow-x: auto;
        border-radius: 12px;
    }

    .table th,
    .table td {
        padding: 0.8rem 0.6rem;
        font-size: 0.9rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }

    .btn {
        padding: 0.8rem 1.5rem;
        font-size: 0.95rem;
    }

    .status-badge {
        padding: 0.4rem 0.8rem;
        font-size: 0.75rem;
    }

    .mobile-menu-btn {
        display: flex !important;
        width: 48px;
        height: 48px;
        min-width: 48px;
        min-height: 48px;
    }
}

/* Enhanced Mobile Devices (max-width: 480px) */
@media (max-width: 480px) {
    .sidebar {
        width: 100%;
        transform: translateX(100%);
        box-shadow: none;
        z-index: 1200;
        visibility: hidden;
        transition: var(--transition-base), visibility 0s linear 0.3s;
        /* Enhanced mobile performance */
        will-change: transform, visibility;
        /* Better mobile backdrop */
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
    }

    .sidebar.active {
        transform: translateX(0);
        visibility: visible;
        box-shadow: none;
        transition: var(--transition-base);
    }

    .main-content {
        margin-right: 0;
        padding: var(--spacing-md);
        transition: var(--transition-base);
        /* Better mobile scrolling */
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: contain;
    }

    .main-content.sidebar-open {
        transform: translateX(-100%);
        /* Prevent scrolling when sidebar is open */
        overflow: hidden;
        height: 100vh;
        height: 100dvh;
    }

    .mobile-menu-btn {
        /* Cross-browser flexbox display */
        display: -webkit-box !important;
        display: -webkit-flex !important;
        display: -moz-box !important;
        display: -ms-flexbox !important;
        display: flex !important;
        /* Enhanced mobile interaction */
        touch-action: manipulation;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        /* Larger touch target for mobile */
        min-width: 48px;
        min-height: 48px;
        /* Prevent zoom on double tap (iOS) */
        -webkit-touch-callout: none;
        -webkit-tap-highlight-color: transparent;
    }

    .mobile-menu-btn:active {
        -webkit-transform: translateY(0) scale(0.95) translateZ(0);
        -moz-transform: translateY(0) scale(0.95) translateZ(0);
        -ms-transform: translateY(0) scale(0.95) translateZ(0);
        -o-transform: translateY(0) scale(0.95) translateZ(0);
        transform: translateY(0) scale(0.95) translateZ(0);
    }

    /* Enhanced hamburger for mobile */
    .hamburger-icon {
        width: 22px;
        height: 16px;
    }

    .hamburger-line {
        height: 2.5px;
    }

    .top-bar {
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
    }

    .page-title {
        font-size: 1.3rem;
    }

    .page-title i {
        font-size: 1.1rem;
    }

    .top-bar-right {
        gap: 1rem;
    }

    .user-info {
        gap: 0.5rem;
        padding: 0.6rem 1rem;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .user-details {
        display: none;
    }

    /* Simplified user info responsive styles */
    .user-info-simple {
        gap: 0.5rem;
        padding: 0.5rem 0.8rem;
    }

    .user-info-simple .user-avatar {
        width: 36px;
        height: 36px;
        font-size: 1rem;
    }

    .user-info-simple .user-details {
        display: none;
    }

    .logout-btn {
        padding: 0.5rem 0.8rem;
        font-size: 0.8rem;
    }

    .logout-btn span {
        display: none;
    }

    .top-bar-logout-btn {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
    }

    .top-bar-logout-btn span {
        display: none;
    }

    /* Mobile responsive components */
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.2rem;
        text-align: center;
    }

    .stat-value {
        font-size: 2rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
        margin: 0 auto 1rem;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0.8rem;
    }

    .card {
        padding: 1.2rem;
        margin-bottom: 1rem;
        border-radius: 10px;
    }

    .card-header h1 {
        font-size: 1.6rem;
    }

    .card-header h2 {
        font-size: 1.4rem;
    }

    .card-header h3 {
        font-size: 1.2rem;
    }

    .table-container {
        overflow-x: auto;
        border-radius: 8px;
    }

    .table th,
    .table td {
        padding: 0.6rem 0.4rem;
        font-size: 0.85rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.4rem;
    }

    .action-btn {
        width: 36px;
        height: 36px;
        font-size: 0.85rem;
    }

    .btn {
        padding: 0.8rem 1.2rem;
        font-size: 0.9rem;
        min-height: 44px;
    }

    .btn-sm {
        padding: 0.6rem 1rem;
        font-size: 0.85rem;
        min-height: 40px;
    }

    .status-badge {
        padding: 0.3rem 0.6rem;
        font-size: 0.7rem;
    }
}

/* Small Mobile Devices (max-width: 480px) */
@media (max-width: 480px) {
    .sidebar {
        width: 100%;
    }

    .top-bar {
        padding: 0.8rem;
        border-radius: 0 0 8px 8px;
        margin-bottom: 0.5rem;
    }

    .page-title {
        font-size: 1.2rem;
    }

    .page-title i {
        font-size: 1rem;
    }

    .top-bar-right {
        gap: 0.8rem;
    }

    .user-info {
        padding: 0.5rem 0.8rem;
    }

    .user-avatar {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .top-bar-logout-btn {
        padding: 0.5rem 0.8rem;
        font-size: 0.75rem;
        border-radius: 20px;
    }

    .page-title {
        font-size: 1.2rem;
    }

    .sidebar-header {
        padding: 1.5rem 1rem;
    }

    .sidebar-header h2 {
        font-size: 1.2rem;
    }

    .menu-item {
        padding: 1rem;
        font-size: 0.95rem;
    }

    .logout-btn {
        bottom: 1rem;
        left: 1rem;
        right: 1rem;
        padding: 0.8rem;
    }

    /* Enhanced mobile menu button for small screens */
    .mobile-menu-btn {
        width: 44px;
        height: 44px;
        padding: 0.6rem;
        /* Ensure minimum touch target */
        min-width: 44px;
        min-height: 44px;
    }

    .hamburger-icon {
        width: 20px;
        height: 14px;
    }

    .hamburger-line {
        height: 2px;
        /* Slightly thicker for better visibility on small screens */
        min-height: 2px;
    }

    /* Small mobile responsive for new components */
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-value {
        font-size: 1.8rem;
    }

    .main-content {
        padding: 1rem;
    }
}

/* High DPI / Retina Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi), (min-resolution: 2dppx) {
    .hamburger-line {
        /* Sharper lines on high DPI displays */
        height: 2.5px;
    }

    .mobile-menu-btn {
        /* Enhanced shadows for retina */
        box-shadow: 0 4px 12px rgba(130, 135, 122, 0.4);
    }

    .mobile-menu-btn:hover {
        box-shadow: 0 6px 20px rgba(130, 135, 122, 0.5);
    }
}

/* ===== UNIFIED HAMBURGER BUTTON STANDARDIZATION ===== */
/* Ensures consistent hamburger button across all admin pages */

/* Standardized hamburger button for both .hamburger-btn and .mobile-menu-btn */
.hamburger-btn,
.mobile-menu-btn {
    display: none; /* Hidden by default on desktop */
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    min-width: 44px;
    min-height: 44px;
    background: transparent;
    border: none;
    cursor: pointer;
    z-index: var(--z-hamburger);
    position: relative;
    transition: all 0.3s ease;
    touch-action: manipulation;
    -webkit-tap-highlight-color: rgba(74, 144, 164, 0.3);
    border-radius: 8px;
    padding: 8px;
}

.hamburger-btn:hover,
.mobile-menu-btn:hover {
    background: rgba(74, 144, 164, 0.1);
    transform: translateY(-1px);
}

.hamburger-btn:active,
.mobile-menu-btn:active {
    transform: translateY(0) scale(0.95);
    background: rgba(130, 135, 122, 0.2);
}

/* Standardized hamburger lines */
.hamburger-btn .hamburger-line,
.hamburger-line {
    display: block;
    width: 22px;
    height: 2px;
    background: #ffffff;
    margin: 3px 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 1px;
}

/* Active state animation */
.hamburger-btn.active .hamburger-line:nth-child(1),
.mobile-menu-btn.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-btn.active .hamburger-line:nth-child(2),
.mobile-menu-btn.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.hamburger-btn.active .hamburger-line:nth-child(3),
.mobile-menu-btn.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Show hamburger button on mobile/tablet */
@media (max-width: 1199px) {
    .hamburger-btn,
    .mobile-menu-btn {
        display: flex !important;
    }
}

/* Very Small Devices (max-width: 320px) */
@media (max-width: 320px) {
    .hamburger-btn,
    .mobile-menu-btn {
        width: 40px;
        height: 40px;
        padding: 0.5rem;
        min-width: 40px;
        min-height: 40px;
    }

    .hamburger-btn .hamburger-line,
    .hamburger-line {
        width: 18px;
        height: 12px;
    }

    .hamburger-line {
        height: 1.5px;
    }
}

/* Large Mobile Devices (481px - 767px) */
@media (min-width: 481px) and (max-width: 767px) {
    .mobile-menu-btn {
        width: 50px;
        height: 50px;
        padding: 0.8rem;
    }

    .hamburger-icon {
        width: 26px;
        height: 20px;
    }

    .hamburger-line {
        height: 2.5px;
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */

/* Enhanced Focus styles for accessibility */
.menu-item:focus,
.logout-btn:focus,
.mobile-menu-btn:focus {
    outline: 3px solid #82877a;
    outline-offset: 3px;
    box-shadow: 0 0 0 6px rgba(130, 135, 122, 0.2);
    /* Ensure focus is visible in all browsers */
    -webkit-box-shadow: 0 0 0 6px rgba(130, 135, 122, 0.2);
    -moz-box-shadow: 0 0 0 6px rgba(130, 135, 122, 0.2);
}

.mobile-menu-btn:focus {
    outline: 3px solid white;
    outline-offset: 3px;
    box-shadow: 0 0 0 6px rgba(255, 255, 255, 0.3);
    -webkit-box-shadow: 0 0 0 6px rgba(255, 255, 255, 0.3);
    -moz-box-shadow: 0 0 0 6px rgba(255, 255, 255, 0.3);
}

.mobile-menu-btn.active:focus {
    outline: 3px solid #fff;
    box-shadow: 0 0 0 6px rgba(255, 255, 255, 0.3);
    -webkit-box-shadow: 0 0 0 6px rgba(255, 255, 255, 0.3);
    -moz-box-shadow: 0 0 0 6px rgba(255, 255, 255, 0.3);
}

/* Focus visible for modern browsers */
.mobile-menu-btn:focus-visible {
    outline: 3px solid white;
    outline-offset: 3px;
}

/* Remove focus outline when not using keyboard */
.mobile-menu-btn:focus:not(:focus-visible) {
    outline: none;
    box-shadow: none;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .sidebar {
        background: #000;
        border-right: 2px solid #fff;
    }

    .menu-item:hover,
    .menu-item.active {
        background: #333;
    }

    .mobile-menu-btn {
        background: #000 !important;
        border: 2px solid #fff !important;
        color: #fff !important;
    }

    .mobile-menu-btn.active {
        background: #fff !important;
        color: #000 !important;
    }

    .hamburger-line {
        background: #fff !important;
    }

    .mobile-menu-btn.active .hamburger-line {
        background: #000 !important;
    }

    .mobile-menu-btn:focus {
        outline: 3px solid #fff !important;
        box-shadow: 0 0 0 6px rgba(255, 255, 255, 0.5) !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .mobile-menu-btn {
        background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark) 100%);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    }

    .mobile-menu-btn:hover {
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.6);
    }

    .mobile-menu-btn.active {
        background: linear-gradient(135deg, var(--color-danger) 0%, var(--color-danger-dark) 100%);
    }
}

/* Light mode support */
@media (prefers-color-scheme: light) {
    .mobile-menu-btn {
        background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .sidebar,
    .main-content,
    .menu-item,
    .logout-btn,
    .mobile-menu-btn,
    .sidebar-overlay,
    .hamburger-icon,
    .hamburger-line {
        -webkit-transition: none !important;
        -moz-transition: none !important;
        -ms-transition: none !important;
        -o-transition: none !important;
        transition: none !important;
        -webkit-animation: none !important;
        -moz-animation: none !important;
        -ms-animation: none !important;
        -o-animation: none !important;
        animation: none !important;
    }

    .menu-item:hover i,
    .mobile-menu-btn:hover,
    .mobile-menu-btn:active {
        -webkit-transform: none !important;
        -moz-transform: none !important;
        -ms-transform: none !important;
        -o-transform: none !important;
        transform: none !important;
    }

    .mobile-menu-btn.active .hamburger-line {
        -webkit-transition: none !important;
        -moz-transition: none !important;
        -ms-transition: none !important;
        -o-transition: none !important;
        transition: none !important;
    }

    /* Still allow the X transformation but without animation */
    .mobile-menu-btn.active .hamburger-line:nth-child(1) {
        -webkit-transform: rotate(45deg) translate(3px, 3px) !important;
        -moz-transform: rotate(45deg) translate(3px, 3px) !important;
        -ms-transform: rotate(45deg) translate(3px, 3px) !important;
        -o-transform: rotate(45deg) translate(3px, 3px) !important;
        transform: rotate(45deg) translate(3px, 3px) !important;
    }

    .mobile-menu-btn.active .hamburger-line:nth-child(2) {
        opacity: 0 !important;
        -webkit-transform: scaleX(0) !important;
        -moz-transform: scaleX(0) !important;
        -ms-transform: scaleX(0) !important;
        -o-transform: scaleX(0) !important;
        transform: scaleX(0) !important;
    }

    .mobile-menu-btn.active .hamburger-line:nth-child(3) {
        -webkit-transform: rotate(-45deg) translate(3px, -3px) !important;
        -moz-transform: rotate(-45deg) translate(3px, -3px) !important;
        -ms-transform: rotate(-45deg) translate(3px, -3px) !important;
        -o-transform: rotate(-45deg) translate(3px, -3px) !important;
        transform: rotate(-45deg) translate(3px, -3px) !important;
    }
}

/* ===== ANIMATION KEYFRAMES ===== */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(100%);
    }
}

/* ===== SIDEBAR STATES ===== */
.sidebar.collapsed {
    width: 80px;
}

.sidebar.collapsed .sidebar-header h2 span,
.sidebar.collapsed .sidebar-header p,
.sidebar.collapsed .menu-item span {
    display: none;
}

.sidebar.collapsed .menu-item {
    justify-content: center;
    padding: 1rem;
}

.sidebar.collapsed .menu-item i {
    margin: 0;
}

.sidebar.collapsed .logout-btn {
    padding: 0.8rem;
}

.sidebar.collapsed .logout-btn span {
    display: none;
}

/* ===== JAVASCRIPT FUNCTIONALITY STYLES ===== */
/* These styles support the JavaScript functionality for sidebar toggle */

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Sidebar toggle button animations */
.sidebar-toggle.active {
    transform: rotate(180deg);
}

/* Mobile menu button animations */
.mobile-menu-btn.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-btn.active .hamburger-line:nth-child(2) {
    opacity: 0;
    transform: scaleX(0);
}

.mobile-menu-btn.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* ===== ENHANCED PRINT STYLES ===== */
@media print {
    .sidebar,
    .mobile-menu-btn,
    .sidebar-overlay,
    .top-bar-logout-btn,
    .action-buttons {
        display: none !important;
    }

    .main-content {
        margin-right: 0 !important;
        padding: 1rem !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        break-inside: avoid;
    }

    .table {
        font-size: 0.8rem !important;
    }

    .btn {
        display: none !important;
    }
}

/* ===== MODERN CSS UTILITIES ===== */
/* Container queries support (when available) */
@supports (container-type: inline-size) {
    .card-container {
        container-type: inline-size;
    }

    @container (max-width: 400px) {
        .card {
            padding: var(--spacing-md);
        }
    }
}

/* Enhanced loading states */
.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--color-primary);
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Enhanced error states */
.error {
    border-color: var(--color-danger) !important;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;
}

.error-message {
    color: var(--color-danger);
    font-size: 0.875rem;
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* Enhanced success states */
.success {
    border-color: var(--color-success) !important;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1) !important;
}

.success-message {
    color: var(--color-success);
    font-size: 0.875rem;
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* Modern scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--color-bg-tertiary);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--color-primary);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--color-primary-dark);
}

/* Firefox scrollbar */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--color-primary) var(--color-bg-tertiary);
}

/* Enhanced selection styling */
::selection {
    background: rgba(130, 135, 122, 0.2);
    color: var(--color-text-primary);
}

::-moz-selection {
    background: rgba(130, 135, 122, 0.2);
    color: var(--color-text-primary);
}

/* Modern CSS Grid utilities */
.grid {
    display: grid;
    gap: var(--spacing-lg);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Modern Flexbox utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }

/* Modern spacing utilities */
.m-0 { margin: 0; }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }

/* Text utilities */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }
.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-normal { font-weight: 400; }
.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }

/* Duplicate utilities removed - already defined above */

/* Border utilities */
.border { border: 1px solid var(--color-primary-alpha-10); }
.border-primary { border-color: var(--color-primary); }
.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-full { border-radius: var(--radius-full); }

/* Shadow utilities */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* ===== HAMBURGER MENU BUTTON ===== */
.hamburger-btn {
    display: none; /* Hidden on desktop */
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: var(--radius-md);
    transition: var(--transition-base);
    position: relative;
}

.hamburger-btn:hover {
    background: rgba(130, 135, 122, 0.1);
    transform: scale(1.05);
}

.hamburger-btn:active {
    transform: scale(0.95);
}

/* Hamburger Lines */
.hamburger-line {
    width: 24px;
    height: 3px;
    background: var(--color-text-primary);
    border-radius: 2px;
    transition: var(--transition-base);
    margin: 2px 0;
}

.hamburger-btn.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.hamburger-btn.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.hamburger-btn.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* ===== MOBILE SIDEBAR BACKDROP ===== */
.sidebar-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: calc(var(--z-sidebar-mobile) - 1);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-base);
    backdrop-filter: blur(2px);
}

.sidebar-backdrop.active {
    opacity: 1;
    visibility: visible;
}

/* ===== MOBILE RESPONSIVE DESIGN ===== */
@media (max-width: 1199px) {
    /* Show hamburger button on mobile/tablet */
    .hamburger-btn {
        display: flex;
    }

    /* Mobile sidebar behavior */
    .sidebar {
        position: fixed;
        top: var(--top-bar-height);
        right: 0;
        width: 100%;
        height: calc(100vh - var(--top-bar-height));
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: var(--z-sidebar-mobile);
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    .sidebar.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    /* Adjust main content for mobile */
    .main-content {
        margin-right: 0;
        width: 100%;
    }

    /* Enhanced mobile navigation styling */
    .sidebar-nav {
        padding: var(--spacing-lg);
    }

    .sidebar-link {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-sm);
    }

    /* Mobile user info component */
    .sidebar-user-info {
        margin: var(--spacing-lg);
        margin-top: var(--spacing-xl);
    }
}
