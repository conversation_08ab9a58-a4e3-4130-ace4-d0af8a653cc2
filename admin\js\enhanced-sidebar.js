/**
 * Enhanced Sidebar System for Care Admin Dashboard
 * نظام الشريط الجانبي المحسن للوحة التحكم الإدارية
 * 
 * Features:
 * - Responsive mobile/tablet hamburger menu
 * - Smooth slide-down animations
 * - RTL Arabic layout support
 * - Touch-friendly interactions
 * - Backdrop overlay system
 * - Professional UI/UX standards
 */

class EnhancedSidebarManager {
    constructor() {
        this.sidebar = null;
        this.backdrop = null;
        this.hamburgerBtn = null;
        this.isOpen = false;
        this.isMobile = false;
        
        // Configuration
        this.config = {
            breakpoints: {
                desktop: 1199,
                tablet: 768,
                mobile: 480
            },
            zIndex: {
                sidebar: 2000,
                backdrop: 1999,
                hamburger: 2100
            },
            animations: {
                duration: 400,
                easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
            }
        };
        
        this.init();
    }
    
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }
    
    setup() {
        this.findElements();
        this.createBackdrop();
        this.setupEventListeners();
        this.updateMobileState();
        this.setActiveNavigation();
        
        console.log('✅ Enhanced Sidebar Manager initialized');
    }
    
    findElements() {
        this.sidebar = document.getElementById('sidebar') || document.querySelector('.sidebar');
        this.hamburgerBtn = document.getElementById('hamburgerBtn') || document.querySelector('.hamburger-btn');
        
        if (!this.sidebar) {
            console.warn('⚠️ Sidebar element not found');
            return;
        }
        
        if (!this.hamburgerBtn) {
            console.warn('⚠️ Hamburger button not found');
        }
    }
    
    createBackdrop() {
        if (!this.sidebar) return;
        
        // Remove existing backdrop if any
        const existingBackdrop = document.getElementById('sidebarBackdrop');
        if (existingBackdrop) {
            existingBackdrop.remove();
        }
        
        // Create new backdrop
        this.backdrop = document.createElement('div');
        this.backdrop.id = 'sidebarBackdrop';
        this.backdrop.className = 'sidebar-overlay';
        this.backdrop.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: ${this.config.zIndex.backdrop};
            opacity: 0;
            visibility: hidden;
            transition: all ${this.config.animations.duration}ms ${this.config.animations.easing};
            cursor: pointer;
        `;
        
        document.body.appendChild(this.backdrop);
    }
    
    setupEventListeners() {
        // Hamburger button click
        if (this.hamburgerBtn) {
            this.hamburgerBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleSidebar();
            });
        }
        
        // Backdrop click
        if (this.backdrop) {
            this.backdrop.addEventListener('click', () => {
                this.closeSidebar();
            });
        }
        
        // Sidebar links click (close on mobile)
        this.setupSidebarLinks();
        
        // Window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });
        
        // Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.closeSidebar();
            }
        });
    }
    
    setupSidebarLinks() {
        const sidebarLinks = document.querySelectorAll('.sidebar-link, .menu-item, [data-sidebar-link]');
        sidebarLinks.forEach(link => {
            link.addEventListener('click', () => {
                // Only close on mobile/tablet, not on logout links
                if (this.isMobile && !link.classList.contains('logout-link')) {
                    setTimeout(() => {
                        this.closeSidebar();
                    }, 150);
                }
            });
        });
    }
    
    updateMobileState() {
        this.isMobile = window.innerWidth <= this.config.breakpoints.desktop;
        
        if (this.hamburgerBtn) {
            this.hamburgerBtn.style.display = this.isMobile ? 'flex' : 'none';
        }
        
        // Close sidebar when switching to desktop
        if (!this.isMobile && this.isOpen) {
            this.closeSidebar();
        }
    }
    
    toggleSidebar() {
        if (this.isOpen) {
            this.closeSidebar();
        } else {
            this.openSidebar();
        }
    }
    
    openSidebar() {
        if (!this.sidebar || this.isOpen || !this.isMobile) return;
        
        console.log('📱 Opening mobile sidebar');
        
        // Add active classes
        this.sidebar.classList.add('active');
        if (this.backdrop) this.backdrop.classList.add('active');
        if (this.hamburgerBtn) this.hamburgerBtn.classList.add('active');
        
        // Show backdrop
        if (this.backdrop) {
            this.backdrop.style.visibility = 'visible';
            this.backdrop.style.opacity = '1';
        }
        
        // Prevent body scroll
        this.preventBodyScroll();
        
        // Update state
        this.isOpen = true;
        
        // Update ARIA attributes
        if (this.hamburgerBtn) {
            this.hamburgerBtn.setAttribute('aria-expanded', 'true');
            this.hamburgerBtn.setAttribute('aria-label', 'إغلاق القائمة');
        }
        
        // Focus management
        this.sidebar.setAttribute('tabindex', '-1');
        this.sidebar.focus();
    }
    
    closeSidebar() {
        if (!this.sidebar || !this.isOpen) return;
        
        console.log('📱 Closing mobile sidebar');
        
        // Remove active classes
        this.sidebar.classList.remove('active');
        if (this.backdrop) this.backdrop.classList.remove('active');
        if (this.hamburgerBtn) this.hamburgerBtn.classList.remove('active');
        
        // Hide backdrop
        if (this.backdrop) {
            this.backdrop.style.opacity = '0';
            this.backdrop.style.visibility = 'hidden';
        }
        
        // Restore body scroll
        this.restoreBodyScroll();
        
        // Update state
        this.isOpen = false;
        
        // Update ARIA attributes
        if (this.hamburgerBtn) {
            this.hamburgerBtn.setAttribute('aria-expanded', 'false');
            this.hamburgerBtn.setAttribute('aria-label', 'فتح القائمة');
        }
        
        // Return focus to hamburger button
        if (this.hamburgerBtn) {
            this.hamburgerBtn.focus();
        }
    }
    
    preventBodyScroll() {
        document.body.style.overflow = 'hidden';
        document.body.style.position = 'fixed';
        document.body.style.width = '100%';
        document.body.style.top = `-${window.scrollY}px`;
    }
    
    restoreBodyScroll() {
        const scrollY = document.body.style.top;
        document.body.style.overflow = '';
        document.body.style.position = '';
        document.body.style.width = '';
        document.body.style.top = '';
        
        if (scrollY) {
            window.scrollTo(0, parseInt(scrollY || '0') * -1);
        }
    }
    
    setActiveNavigation() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.sidebar-link, .nav-item');
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            
            const href = link.getAttribute('href');
            if (href && currentPath.includes(href.replace('./', ''))) {
                link.classList.add('active');
            }
        });
        
        // Default to dashboard if no match found
        if (!document.querySelector('.nav-item.active, .sidebar-link.active')) {
            const dashboardLink = document.querySelector('.nav-item[href*="dashboard"], .sidebar-link[href*="dashboard"]');
            if (dashboardLink) {
                dashboardLink.classList.add('active');
            }
        }
    }
    
    handleResize() {
        const wasMobile = this.isMobile;
        this.updateMobileState();
        
        // Close sidebar when switching from mobile to desktop
        if (wasMobile && !this.isMobile && this.isOpen) {
            this.closeSidebar();
        }
    }
}

// Initialize the enhanced sidebar manager
let enhancedSidebarManager;

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        enhancedSidebarManager = new EnhancedSidebarManager();
    });
} else {
    enhancedSidebarManager = new EnhancedSidebarManager();
}

// Export for global access
window.EnhancedSidebarManager = EnhancedSidebarManager;
window.enhancedSidebarManager = enhancedSidebarManager;
