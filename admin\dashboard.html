<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1024">
    <title>لوحة التحكم الرئيسية - Care</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/logout-modal.css">
    <link rel="stylesheet" href="css/enhanced-sidebar.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Removed conflicting sidebar scripts - using unified system -->
    
    <style>
        /* Enhanced Dashboard-specific styles - base styles are in enhanced-sidebar.css */

        /* Welcome Section */
        .welcome-section .card {
            background: linear-gradient(135deg, var(--color-bg-primary) 0%, #f8f9fa 100%);
            border: 1px solid var(--color-primary-alpha-10);
        }

        .welcome-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: var(--shadow-md);
        }

        .welcome-actions .btn {
            min-height: 40px;
        }

        /* Enhanced stat cards with uniform sizing */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            min-height: 140px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        /* Enhanced Products Distribution Card with standardized dimensions */
        .enhanced-products-card {
            background: linear-gradient(135deg, var(--color-bg-primary) 0%, #f8f9fa 100%);
            border: 1px solid rgba(130, 135, 122, 0.15);
            box-shadow: 0 8px 25px rgba(130, 135, 122, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            min-height: 400px;
        }

        .enhanced-products-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(130, 135, 122, 0.15);
        }

        /* Standardized chart container */
        .enhanced-chart-container {
            display: flex;
            flex-direction: column;
            height: 320px;
            padding: 1rem;
        }

        .chart-wrapper {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        /* Uniform card grid layout */
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .activity-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
            gap: 1.5rem;
        }

        .products-icon {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .chart-title-section .chart-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--color-text-primary);
            margin: 0 0 0.25rem 0;
            font-family: 'Cairo', sans-serif;
        }

        .chart-title-section .chart-subtitle {
            font-size: 0.85rem;
            color: var(--color-text-secondary);
            margin: 0;
            font-family: 'Cairo', sans-serif;
        }

        .enhanced-chart-container {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-lg);
            padding: var(--spacing-lg);
            align-items: center;
        }

        .chart-wrapper {
            position: relative;
            max-width: 300px;
            margin: 0 auto;
        }

        .products-legend {
            min-width: 200px;
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm);
            background: rgba(130, 135, 122, 0.05);
            border-radius: var(--radius-md);
            transition: all 0.3s ease;
        }

        .legend-item:hover {
            background: rgba(130, 135, 122, 0.1);
            transform: translateX(-2px);
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .legend-text {
            font-size: 0.9rem;
            color: var(--color-text-primary);
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
        }

        .legend-value {
            font-size: 0.8rem;
            color: var(--color-text-secondary);
            margin-right: auto;
            font-family: 'Cairo', sans-serif;
        }

        .enhanced-chart-footer {
            background: linear-gradient(135deg, rgba(130, 135, 122, 0.03) 0%, rgba(130, 135, 122, 0.01) 100%);
            border-top: 1px solid rgba(130, 135, 122, 0.1);
            padding: var(--spacing-lg);
        }

        .products-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-md);
        }

        .stat-card {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            background: var(--color-bg-primary);
            border-radius: var(--radius-lg);
            border: 1px solid rgba(130, 135, 122, 0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(130, 135, 122, 0.1);
        }

        .stat-card .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
            color: white;
            flex-shrink: 0;
        }

        .stat-card:nth-child(1) .stat-icon {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
        }

        .stat-card:nth-child(2) .stat-icon {
            background: linear-gradient(135deg, var(--color-success) 0%, #1e7e34 100%);
        }

        .stat-card:nth-child(3) .stat-icon {
            background: linear-gradient(135deg, var(--color-warning) 0%, #e0a800 100%);
        }

        .stat-content {
            display: flex;
            flex-direction: column;
            gap: 0.2rem;
        }

        .stat-content .stat-number {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--color-text-primary);
            font-family: 'Cairo', sans-serif;
        }

        .stat-content .stat-label {
            font-size: 0.8rem;
            color: var(--color-text-secondary);
            font-family: 'Cairo', sans-serif;
        }

        /* Enhanced Monthly Sales Card */
        .enhanced-sales-card {
            background: linear-gradient(135deg, var(--color-bg-primary) 0%, #f8f9fa 100%);
            border: 1px solid rgba(130, 135, 122, 0.15);
            box-shadow: 0 8px 25px rgba(130, 135, 122, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .enhanced-sales-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(130, 135, 122, 0.15);
        }

        .sales-icon {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }

        .enhanced-chart-controls {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .modern-select {
            background: linear-gradient(135deg, var(--color-bg-primary) 0%, #f8f9fa 100%);
            border: 1px solid rgba(130, 135, 122, 0.2);
            border-radius: var(--radius-md);
            padding: var(--spacing-sm) var(--spacing-md);
            font-family: 'Cairo', sans-serif;
            font-size: 0.9rem;
            color: var(--color-text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 140px;
        }

        .modern-select:hover {
            border-color: var(--color-primary);
            box-shadow: 0 2px 8px rgba(130, 135, 122, 0.1);
        }

        .modern-select:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(130, 135, 122, 0.1);
        }

        .enhanced-sales-container {
            padding: var(--spacing-lg);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }

        .sales-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
        }

        .metric-card {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            background: linear-gradient(135deg, rgba(130, 135, 122, 0.05) 0%, rgba(130, 135, 122, 0.02) 100%);
            border-radius: var(--radius-lg);
            border: 1px solid rgba(130, 135, 122, 0.1);
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(130, 135, 122, 0.1);
        }

        .metric-card .metric-icon {
            width: 45px;
            height: 45px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
            flex-shrink: 0;
        }

        .metric-card:nth-child(1) .metric-icon {
            background: linear-gradient(135deg, var(--color-success) 0%, #1e7e34 100%);
        }

        .metric-card:nth-child(2) .metric-icon {
            background: linear-gradient(135deg, var(--color-info) 0%, #117a8b 100%);
        }

        .metric-content {
            display: flex;
            flex-direction: column;
            gap: 0.2rem;
        }

        .metric-content .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--color-text-primary);
            font-family: 'Cairo', sans-serif;
        }

        .metric-content .metric-label {
            font-size: 0.85rem;
            color: var(--color-text-secondary);
            font-family: 'Cairo', sans-serif;
        }

        .enhanced-sales-footer {
            background: linear-gradient(135deg, rgba(130, 135, 122, 0.03) 0%, rgba(130, 135, 122, 0.01) 100%);
            border-top: 1px solid rgba(130, 135, 122, 0.1);
            padding: var(--spacing-lg);
        }

        .sales-legend {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .sales-legend .legend-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-sm);
            background: rgba(130, 135, 122, 0.05);
            border-radius: var(--radius-md);
            transition: all 0.3s ease;
        }

        .sales-legend .legend-item:hover {
            background: rgba(130, 135, 122, 0.1);
            transform: translateX(-2px);
        }

        .sales-legend .legend-color {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .sales-legend .legend-text {
            font-size: 0.9rem;
            color: var(--color-text-primary);
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
        }

        .sales-legend .legend-value {
            font-size: 0.85rem;
            color: var(--color-text-secondary);
            margin-right: auto;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
        }

        /* Enhanced Recent Activity Card */
        .enhanced-activity-card {
            background: linear-gradient(135deg, var(--color-bg-primary) 0%, #f8f9fa 100%);
            border: 1px solid rgba(130, 135, 122, 0.15);
            box-shadow: 0 8px 25px rgba(130, 135, 122, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .enhanced-activity-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(130, 135, 122, 0.15);
        }

        .activity-icon-header {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
        }

        .section-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--color-text-primary);
            margin: 0 0 0.25rem 0;
            font-family: 'Cairo', sans-serif;
        }

        .section-subtitle {
            font-size: 0.85rem;
            color: var(--color-text-secondary);
            margin: 0;
            font-family: 'Cairo', sans-serif;
        }

        .activity-container {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
            padding: var(--spacing-lg);
        }

        .activity-item {
            display: flex;
            align-items: flex-start;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            background: linear-gradient(135deg, rgba(130, 135, 122, 0.03) 0%, rgba(130, 135, 122, 0.01) 100%);
            border-radius: var(--radius-lg);
            border: 1px solid rgba(130, 135, 122, 0.1);
            transition: all 0.3s ease;
            animation: slideInActivity 0.5s ease-out;
        }

        .activity-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(130, 135, 122, 0.1);
            border-color: rgba(130, 135, 122, 0.2);
        }

        .activity-icon-wrapper {
            flex-shrink: 0;
        }

        .activity-icon {
            width: 45px;
            height: 45px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
            color: white;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        .activity-icon.order {
            background: linear-gradient(135deg, var(--color-success) 0%, #1e7e34 100%);
        }

        .activity-icon.customer {
            background: linear-gradient(135deg, var(--color-info) 0%, #117a8b 100%);
        }

        .activity-icon.product {
            background: linear-gradient(135deg, var(--color-warning) 0%, #e0a800 100%);
        }

        .activity-icon.user {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
        }

        .activity-content {
            flex: 1;
            min-width: 0;
        }

        .activity-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--spacing-xs);
        }

        .activity-text {
            font-size: 0.95rem;
            font-weight: 600;
            color: var(--color-text-primary);
            font-family: 'Cairo', sans-serif;
        }

        .activity-status {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            color: white;
            flex-shrink: 0;
        }

        .activity-status.success {
            background: linear-gradient(135deg, var(--color-success) 0%, #1e7e34 100%);
        }

        .activity-status.pending {
            background: linear-gradient(135deg, var(--color-warning) 0%, #e0a800 100%);
        }

        .activity-status.info {
            background: linear-gradient(135deg, var(--color-info) 0%, #117a8b 100%);
        }

        .activity-meta {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .activity-time {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: 0.8rem;
            color: var(--color-text-secondary);
            font-family: 'Cairo', sans-serif;
        }

        .activity-time i {
            font-size: 0.7rem;
            opacity: 0.7;
        }

        .activity-type {
            background: linear-gradient(135deg, rgba(130, 135, 122, 0.1) 0%, rgba(130, 135, 122, 0.05) 100%);
            color: var(--color-text-secondary);
            padding: 0.2rem 0.5rem;
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 500;
            font-family: 'Cairo', sans-serif;
        }

        @keyframes slideInActivity {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Enhanced Recent Orders Card */
        .enhanced-orders-card {
            background: linear-gradient(135deg, var(--color-bg-primary) 0%, #f8f9fa 100%);
            border: 1px solid rgba(130, 135, 122, 0.15);
            box-shadow: 0 8px 25px rgba(130, 135, 122, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .enhanced-orders-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(130, 135, 122, 0.15);
        }

        .orders-icon-header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .orders-container {
            padding: var(--spacing-lg);
        }

        .order-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            background: linear-gradient(135deg, rgba(130, 135, 122, 0.03) 0%, rgba(130, 135, 122, 0.01) 100%);
            border-radius: var(--radius-lg);
            border: 1px solid rgba(130, 135, 122, 0.1);
            margin-bottom: var(--spacing-md);
            transition: all 0.3s ease;
            animation: slideInOrder 0.5s ease-out;
        }

        .order-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(130, 135, 122, 0.1);
            border-color: rgba(130, 135, 122, 0.2);
        }

        .order-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.1rem;
            flex-shrink: 0;
            box-shadow: 0 3px 10px rgba(130, 135, 122, 0.2);
        }

        .order-content {
            flex: 1;
            min-width: 0;
        }

        .order-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--spacing-xs);
        }

        .order-customer {
            font-size: 0.95rem;
            font-weight: 600;
            color: var(--color-text-primary);
            font-family: 'Cairo', sans-serif;
        }

        .order-amount {
            font-size: 0.9rem;
            font-weight: 700;
            color: var(--color-success);
            font-family: 'Cairo', sans-serif;
        }

        .order-meta {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .order-time {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: 0.8rem;
            color: var(--color-text-secondary);
            font-family: 'Cairo', sans-serif;
        }

        .order-time i {
            font-size: 0.7rem;
            opacity: 0.7;
        }

        .order-status {
            padding: 0.2rem 0.6rem;
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
            text-align: center;
        }

        .order-status.pending {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 193, 7, 0.1) 100%);
            color: #856404;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        .order-status.confirmed {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(40, 167, 69, 0.1) 100%);
            color: #155724;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .order-status.processing {
            background: linear-gradient(135deg, rgba(23, 162, 184, 0.2) 0%, rgba(23, 162, 184, 0.1) 100%);
            color: #0c5460;
            border: 1px solid rgba(23, 162, 184, 0.3);
        }

        .order-status.shipped {
            background: linear-gradient(135deg, rgba(130, 135, 122, 0.2) 0%, rgba(130, 135, 122, 0.1) 100%);
            color: var(--color-text-primary);
            border: 1px solid rgba(130, 135, 122, 0.3);
        }

        .order-status.delivered {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.3) 0%, rgba(40, 167, 69, 0.2) 100%);
            color: #155724;
            border: 1px solid rgba(40, 167, 69, 0.4);
        }

        .order-status.cancelled {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.2) 0%, rgba(220, 53, 69, 0.1) 100%);
            color: #721c24;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .empty-state {
            text-align: center;
            padding: var(--spacing-xxl);
            color: var(--color-text-secondary);
        }

        .empty-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto var(--spacing-lg);
            background: linear-gradient(135deg, rgba(130, 135, 122, 0.1) 0%, rgba(130, 135, 122, 0.05) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: var(--color-text-muted);
        }

        .empty-state h4 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--color-text-primary);
            margin: 0 0 var(--spacing-sm) 0;
            font-family: 'Cairo', sans-serif;
        }

        .empty-state p {
            font-size: 0.9rem;
            color: var(--color-text-secondary);
            margin: 0 0 var(--spacing-md) 0;
            font-family: 'Cairo', sans-serif;
            line-height: 1.5;
        }

        .empty-state .btn {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            border: none;
            color: white;
            padding: var(--spacing-sm) var(--spacing-lg);
            border-radius: var(--radius-md);
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .empty-state .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(130, 135, 122, 0.3);
        }

        @keyframes slideInOrder {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Mobile Responsive Styles */
        @media (max-width: 768px) {
            .enhanced-chart-container {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .products-legend {
                min-width: auto;
                margin-top: var(--spacing-md);
            }

            .products-stats-grid {
                grid-template-columns: 1fr;
            }

            .enhanced-sales-container {
                padding: var(--spacing-md);
            }

            .sales-metrics {
                grid-template-columns: 1fr;
            }

            .metric-card {
                padding: var(--spacing-sm);
            }

            .activity-item {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-sm);
            }

            .activity-header {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-xs);
            }

            .order-item {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-sm);
            }

            .order-header {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-xs);
            }

            .card-actions {
                flex-direction: column;
                gap: var(--spacing-xs);
            }

            .card-actions .btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* Tablet Responsive Styles */
        @media (max-width: 1199px) and (min-width: 769px) {
            .enhanced-chart-container {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .products-legend {
                margin-top: var(--spacing-md);
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: var(--spacing-sm);
            }

            .sales-metrics {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        .stat-card {
            background: linear-gradient(135deg, var(--color-bg-primary) 0%, #fafbfc 100%);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-md);
            border: 1px solid rgba(130, 135, 122, 0.1);
            transition: var(--transition-base);
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
            opacity: 0;
            transition: var(--transition-base);
        }

        .stat-card:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: var(--shadow-lg);
            border-color: rgba(130, 135, 122, 0.2);
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-md);
        }

        .stat-content {
            text-align: center;
        }

        /* Enhanced stat icon gradients with better contrast */
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            transition: var(--transition-base);
            box-shadow: var(--shadow-md);
        }

        .stat-icon.orders {
            background: linear-gradient(135deg, var(--color-info) 0%, var(--color-info-dark) 100%);
        }
        .stat-icon.products {
            background: linear-gradient(135deg, var(--color-danger) 0%, var(--color-danger-dark) 100%);
        }
        .stat-icon.sales {
            background: linear-gradient(135deg, var(--color-success) 0%, var(--color-success-dark) 100%);
        }
        .stat-icon.customers {
            background: linear-gradient(135deg, var(--color-warning) 0%, var(--color-warning-dark) 100%);
        }
        .stat-icon.weekly-orders,
        .stat-icon.weekly-revenue {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
        }

        .stat-card:hover .stat-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: var(--shadow-lg);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-sm);
            line-height: 1.2;
        }

        .stat-label {
            color: var(--color-text-secondary);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: var(--spacing-xs);
        }

        .stat-description {
            color: var(--color-text-muted);
            font-size: 0.875rem;
            font-weight: 400;
        }

        .stat-change {
            font-size: 0.8rem;
            font-weight: 600;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-full);
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            transition: var(--transition-base);
        }

        .stat-change.positive {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
            color: var(--color-success);
            border: 1px solid rgba(40, 167, 69, 0.2);
        }

        .stat-change.negative {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%);
            color: var(--color-danger);
            border: 1px solid rgba(220, 53, 69, 0.2);
        }

        .stat-change.neutral {
            background: linear-gradient(135deg, rgba(130, 135, 122, 0.1) 0%, rgba(130, 135, 122, 0.05) 100%);
            color: var(--color-primary);
            border: 1px solid rgba(130, 135, 122, 0.2);
        }

        .loading-placeholder {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading-shimmer 1.5s infinite;
            border-radius: var(--radius-sm);
            display: inline-block;
            min-width: 60px;
            height: 1em;
        }

        @keyframes loading-shimmer {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Enhanced Charts Section */
        .charts-section {
            margin-bottom: var(--spacing-xl);
        }

        .chart-card {
            position: relative;
        }

        .chart-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .chart-controls {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .chart-period {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid rgba(130, 135, 122, 0.2);
            border-radius: var(--radius-sm);
            background: var(--color-bg-primary);
            font-family: 'Cairo', sans-serif;
            font-size: 0.875rem;
            transition: var(--transition-base);
        }

        .chart-period:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(130, 135, 122, 0.1);
        }

        .chart-container {
            position: relative;
            margin: var(--spacing-lg) 0;
            min-height: 300px;
        }

        .chart-loading {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-md);
            border-radius: var(--radius-md);
            opacity: 0;
            visibility: hidden;
            transition: var(--transition-base);
        }

        .chart-loading.active {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(130, 135, 122, 0.2);
            border-top-color: var(--color-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .chart-footer {
            border-top: 1px solid rgba(130, 135, 122, 0.1);
            padding-top: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        .chart-legend {
            display: flex;
            justify-content: center;
            gap: var(--spacing-lg);
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: 0.875rem;
            color: var(--color-text-secondary);
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .chart-stats {
            display: flex;
            justify-content: space-around;
            text-align: center;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--color-primary);
        }

        .stat-text {
            font-size: 0.875rem;
            color: var(--color-text-secondary);
        }

        /* Enhanced Activity Section */
        .activity-section {
            margin-bottom: var(--spacing-xl);
        }

        .section-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .card-actions {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .card-content {
            padding: 0;
        }

        .loading-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-xl);
            gap: var(--spacing-md);
            color: var(--color-text-secondary);
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            border-bottom: 1px solid rgba(130, 135, 122, 0.1);
            transition: var(--transition-base);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-item:hover {
            background: rgba(130, 135, 122, 0.05);
            transform: translateX(-2px);
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            flex-shrink: 0;
        }

        .activity-icon.order {
            background: linear-gradient(135deg, var(--color-success) 0%, #20c997 100%);
        }

        .activity-icon.customer {
            background: linear-gradient(135deg, var(--color-info) 0%, #138496 100%);
        }

        .activity-icon.product {
            background: linear-gradient(135deg, var(--color-warning) 0%, #e0a800 100%);
        }

        .activity-icon.user {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
        }

        .activity-content {
            flex: 1;
        }

        .activity-text {
            font-weight: 600;
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .activity-meta {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .activity-time {
            font-size: 0.875rem;
            color: var(--color-text-muted);
        }

        .activity-type {
            font-size: 0.75rem;
            padding: var(--spacing-xs) var(--spacing-sm);
            background: rgba(130, 135, 122, 0.1);
            color: var(--color-primary);
            border-radius: var(--radius-full);
            font-weight: 600;
        }

        .activity-status {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            flex-shrink: 0;
        }

        .activity-status.success {
            background: rgba(40, 167, 69, 0.1);
            color: var(--color-success);
        }

        .activity-status.pending {
            background: rgba(255, 193, 7, 0.1);
            color: var(--color-warning);
        }

        .activity-status.info {
            background: rgba(23, 162, 184, 0.1);
            color: var(--color-info);
        }

        /* Enhanced Responsive Design */
        @media (max-width: 768px) {
            .welcome-section .flex {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-md);
            }

            .welcome-actions {
                width: 100%;
                justify-content: stretch;
            }

            .welcome-actions .btn {
                flex: 1;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-md);
            }

            .stat-card {
                padding: var(--spacing-md);
            }

            .stat-value {
                font-size: 2rem;
            }

            .charts-section {
                grid-template-columns: 1fr;
            }

            .chart-container {
                min-height: 250px;
            }

            .activity-section {
                grid-template-columns: 1fr;
            }

            .activity-item {
                padding: var(--spacing-sm);
            }

            .card-header .flex {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-sm);
            }

            .card-actions {
                width: 100%;
                justify-content: stretch;
            }

            .card-actions .btn {
                flex: 1;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                gap: var(--spacing-sm);
            }

            .stat-card {
                padding: var(--spacing-sm);
            }

            .stat-icon {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }

            .stat-value {
                font-size: 1.8rem;
            }

            .chart-container {
                min-height: 200px;
            }

            .activity-item {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-sm);
            }

            .activity-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-xs);
            }
        }

        /* Enhanced animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stat-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .stat-card:nth-child(1) { animation-delay: 0.1s; }
        .stat-card:nth-child(2) { animation-delay: 0.2s; }
        .stat-card:nth-child(3) { animation-delay: 0.3s; }
        .stat-card:nth-child(4) { animation-delay: 0.4s; }
        .stat-card:nth-child(5) { animation-delay: 0.5s; }
        .stat-card:nth-child(6) { animation-delay: 0.6s; }

        .card {
            animation: fadeInUp 0.6s ease forwards;
        }

        /* Toast animations */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* Print styles */
        @media print {
            .welcome-actions,
            .card-actions,
            .chart-controls {
                display: none !important;
            }

            .stat-card {
                break-inside: avoid;
                box-shadow: none !important;
                border: 1px solid #ddd !important;
            }

            .chart-container {
                min-height: auto !important;
            }
        }

        /* Enhanced Charts Section */
        .charts-section {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .chart-period {
            background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
            border: 2px solid #e9ecef;
            padding: 0.8rem 1.2rem;
            border-radius: 10px;
            font-size: 0.9rem;
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            min-height: 44px;
        }

        .chart-period:focus {
            outline: none;
            border-color: #82877a;
            box-shadow:
                0 0 0 4px rgba(130, 135, 122, 0.1),
                0 2px 8px rgba(130, 135, 122, 0.2);
            transform: translateY(-1px);
        }

        .chart-period:hover {
            border-color: #82877a;
            transform: translateY(-1px);
        }

        /* Recent Activity */
        .activity-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .view-all {
            color: #82877a;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .view-all:hover {
            color: #121414;
            text-decoration: underline;
        }

        /* Responsive Charts */
        @media (max-width: 768px) {
            .charts-section {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .chart-period {
                padding: 0.6rem 1rem;
                font-size: 0.85rem;
            }
        }

        @media (max-width: 480px) {
            .charts-section {
                gap: 1rem;
            }

            .activity-section {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid #f8f9fa;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            color: white;
        }

        .activity-icon.order { background: #3498db; }
        .activity-icon.product { background: #e74c3c; }
        .activity-icon.customer { background: #f39c12; }

        .activity-content {
            flex: 1;
        }

        .activity-text {
            font-size: 0.9rem;
            color: #333;
            margin-bottom: 0.2rem;
        }

        .activity-time {
            font-size: 0.8rem;
            color: #666;
        }

        /* Loading */
        .loading {
            text-align: center;
            padding: 2rem;
            color: #82877a;
        }

        .loading i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
            margin-bottom: 0.5rem;
        }

        .loading p {
            margin: 0;
            font-size: 0.9rem;
        }

        .fa-spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideOutRight {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100px);
            }
        }

        /* Error state */
        .error-state {
            text-align: center;
            padding: 2rem;
            color: #e74c3c;
        }

        .error-state i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        /* Chart loading overlay */
        .chart-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.9);
            padding: 1rem;
            border-radius: 8px;
            z-index: 10;
        }

        .chart-card {
            position: relative;
        }

        /* Mobile responsive styles are now in css/enhanced-sidebar.css */

        /* ===== TIMELINE STATS SECTION STYLING ===== */
        .timeline-stats-section {
            margin-bottom: 2rem;
        }

        .timeline-stats-section .card-header {
            border-bottom: 1px solid rgba(130, 135, 122, 0.1);
            padding-bottom: 1.5rem;
            margin-bottom: 2rem;
        }

        .timeline-icon {
            width: 50px;
            height: 50px;
            border-radius: var(--radius-lg);
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.25rem;
            box-shadow: var(--shadow-md);
        }

        .timeline-stats-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .stats-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.2);
            border-radius: var(--radius-full);
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--color-success);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--color-success);
            animation: pulse 2s infinite;
        }

        .status-indicator.inactive {
            background: var(--color-danger);
            animation: none;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* Timeline Stats Grid Layout */
        .timeline-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        /* Timeline Stat Cards */
        .timeline-stat-card {
            background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            box-shadow:
                0 4px 20px rgba(130, 135, 122, 0.08),
                0 1px 3px rgba(130, 135, 122, 0.1);
            border: 1px solid rgba(130, 135, 122, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            min-height: 180px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .timeline-stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-light) 50%, var(--color-primary) 100%);
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .timeline-stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow:
                0 12px 40px rgba(130, 135, 122, 0.15),
                0 4px 12px rgba(130, 135, 122, 0.1);
            border-color: rgba(130, 135, 122, 0.15);
        }

        .timeline-stat-card:hover::before {
            opacity: 1;
            background-size: 200% 100%;
            animation: shimmer 2s ease-in-out infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* Timeline Stat Card Header */
        .timeline-stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .timeline-stat-icon {
            width: 55px;
            height: 55px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.4rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .timeline-stat-icon.years {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        }

        .timeline-stat-icon.customers {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .timeline-stat-icon.products {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        .timeline-stat-icon.brands {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
        }

        .timeline-stat-card:hover .timeline-stat-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .timeline-stat-badge {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            background: rgba(74, 144, 164, 0.1);
            border: 1px solid rgba(74, 144, 164, 0.2);
            border-radius: var(--radius-full);
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--color-primary);
        }

        /* Timeline Stat Content */
        .timeline-stat-content {
            text-align: center;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .timeline-stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--color-text-primary);
            margin-bottom: 0.5rem;
            line-height: 1.2;
            font-family: 'Cairo', sans-serif;
        }

        .timeline-stat-label {
            color: var(--color-text-secondary);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
            font-family: 'Cairo', sans-serif;
        }

        .timeline-stat-description {
            color: var(--color-text-muted);
            font-size: 0.875rem;
            font-weight: 400;
            font-family: 'Cairo', sans-serif;
        }

        /* Timeline Stat Progress Bars */
        .timeline-stat-progress {
            margin-top: 1rem;
            height: 4px;
            background: rgba(130, 135, 122, 0.1);
            border-radius: var(--radius-full);
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            border-radius: var(--radius-full);
            transition: all 1s ease-in-out;
            position: relative;
            opacity: 0;
            transform: scaleX(0);
            transform-origin: right;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: progress-shine 2s infinite;
        }

        @keyframes progress-shine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .years-progress {
            background: linear-gradient(90deg, #3498db 0%, #2980b9 100%);
            width: 85%;
        }

        .customers-progress {
            background: linear-gradient(90deg, #e74c3c 0%, #c0392b 100%);
            width: 92%;
        }

        .products-progress {
            background: linear-gradient(90deg, #f39c12 0%, #e67e22 100%);
            width: 78%;
        }

        .brands-progress {
            background: linear-gradient(90deg, #9b59b6 0%, #8e44ad 100%);
            width: 88%;
        }

        /* Timeline Stats Footer */
        .timeline-stats-footer {
            border-top: 1px solid rgba(130, 135, 122, 0.1);
            padding-top: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .stats-summary {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .summary-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--color-text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .summary-item i {
            color: var(--color-primary);
            font-size: 1rem;
        }

        .stats-actions {
            display: flex;
            gap: 0.75rem;
        }

        /* Loading States */
        .timeline-stats-grid.loading .timeline-stat-card {
            opacity: 0.6;
            pointer-events: none;
        }

        .timeline-stats-grid.loading .timeline-stat-value {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading-shimmer 1.5s infinite;
            color: transparent;
        }

        @keyframes loading-shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* Timeline Stats Error State */
        .timeline-stats-error {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.05) 0%, rgba(231, 76, 60, 0.02) 100%);
            border: 1px solid rgba(231, 76, 60, 0.1);
            border-radius: var(--radius-lg);
            padding: 2rem;
            text-align: center;
        }

        .timeline-stats-error i {
            color: var(--color-danger);
            margin-bottom: 1rem;
        }

        .timeline-stats-error p {
            color: var(--color-text-secondary);
            margin-bottom: 1rem;
        }

        /* RTL Arabic Support Enhancements */
        [dir="rtl"] .timeline-stat-badge {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .timeline-stat-header {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .timeline-stats-footer {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .stats-summary {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .summary-item {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .progress-bar {
            transform-origin: left;
        }

        /* Responsive Design for Timeline Stats */
        @media (max-width: 1200px) {
            .timeline-stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1.25rem;
            }

            .timeline-stat-card {
                padding: 1.25rem;
                min-height: 160px;
            }

            .timeline-stat-value {
                font-size: 2.2rem;
            }
        }

        @media (max-width: 768px) {
            .timeline-stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
                gap: 1rem;
            }

            .timeline-stats-footer {
                flex-direction: column;
                align-items: flex-start;
            }

            .stats-summary {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- STANDARDIZED Sidebar -->
    <div class="sidebar" id="sidebar">
        <!-- Brand Section (Top) -->
        <div class="sidebar-section brand-section">
            <div class="brand-logo">
                <i class="fas fa-store"></i>
                <h2>Care Admin</h2>
            </div>
            <p class="brand-subtitle">لوحة التحكم الإدارية</p>
        </div>

        <!-- Unified Navigation Section -->
        <div class="sidebar-section unified-navigation">
            <!-- Seamless Navigation List -->
            <nav class="sidebar-nav">
                <!-- Dashboard Navigation Links -->
                <a href="dashboard.html" class="sidebar-link active">
                    <i class="fas fa-tachometer-alt"></i>
                    الرئيسية
                </a>
                <a href="orders.html" class="sidebar-link">
                    <i class="fas fa-shopping-bag"></i>
                    إدارة الطلبات
                </a>
                <a href="products.html" class="sidebar-link">
                    <i class="fas fa-box"></i>
                    إدارة المنتجات
                </a>
                <a href="cart-management.html" class="sidebar-link">
                    <i class="fas fa-shopping-cart"></i>
                    إدارة سلة التسوق
                </a>
                <a href="content.html" class="sidebar-link">
                    <i class="fas fa-edit"></i>
                    إدارة المحتوى
                </a>
                <a href="site-settings.html" class="sidebar-link">
                    <i class="fas fa-cog"></i>
                    إعدادات الموقع
                </a>

                <!-- Admin Navigation Links (seamlessly integrated) -->
                <a href="../index.html" class="sidebar-link" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    عرض الموقع
                </a>
                <a href="#" class="sidebar-link logout-link" onclick="showLogoutModal()" title="تسجيل الخروج">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </nav>

            <!-- User Info Component (at bottom of navigation) -->
            <div class="sidebar-user-info">
                <div class="user-avatar" id="sidebarUserAvatar">A</div>
                <div class="user-details">
                    <div class="user-name" id="sidebarUserName">مدير النظام</div>
                    <div class="user-role">مدير النظام</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Backdrop -->
    <div class="sidebar-backdrop" id="sidebarBackdrop" onclick="closeMobileSidebar()"></div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="top-bar">
            <div class="top-bar-content">
                <div class="page-title-section">
                    <h1 class="page-title">
                        <i class="fas fa-tachometer-alt"></i>
                        لوحة التحكم الرئيسية
                    </h1>
                </div>

                <div class="top-bar-actions">
                    <!-- Mobile Hamburger Menu Button -->
                    <button class="hamburger-btn" onclick="toggleMobileSidebar()" title="القائمة" id="hamburgerBtn">
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                    </button>
                </div>
            </div>
        </div>

        <div class="dashboard-content">
            <!-- Enhanced Welcome Section -->
            <div class="welcome-section mb-lg">
                <div class="card card-elevated">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-lg">
                            <div class="welcome-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div>
                                <h2 class="text-xl font-bold mb-sm">مرحباً بك في لوحة التحكم</h2>
                                <p class="text-secondary">إليك نظرة سريعة على أداء متجرك اليوم</p>
                            </div>
                        </div>
                        <div class="welcome-actions flex gap-sm">
                            <!-- Refresh and export buttons removed for cleaner interface -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card" data-stat="orders">
                    <div class="stat-header">
                        <div class="stat-icon orders">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-change positive" id="ordersChange">
                            <i class="fas fa-arrow-up"></i>
                            <span>+12%</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="totalOrders">
                            <span class="loading-placeholder">0</span>
                        </div>
                        <div class="stat-label">إجمالي الطلبات</div>
                        <div class="stat-description">منذ بداية الشهر</div>
                    </div>
                </div>

                <div class="stat-card" data-stat="products">
                    <div class="stat-header">
                        <div class="stat-icon products">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-change positive" id="productsChange">
                            <i class="fas fa-arrow-up"></i>
                            <span>+5%</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="totalProducts">
                            <span class="loading-placeholder">0</span>
                        </div>
                        <div class="stat-label">إجمالي المنتجات</div>
                        <div class="stat-description">المنتجات النشطة</div>
                    </div>
                </div>

                <div class="stat-card" data-stat="sales">
                    <div class="stat-header">
                        <div class="stat-icon sales">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-change positive" id="salesChange">
                            <i class="fas fa-arrow-up"></i>
                            <span>+18%</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="totalSales">
                            <span class="loading-placeholder">0 د.ع</span>
                        </div>
                        <div class="stat-label">إجمالي المبيعات</div>
                        <div class="stat-description">الطلبات المكتملة</div>
                    </div>
                </div>

                <div class="stat-card" data-stat="customers">
                    <div class="stat-header">
                        <div class="stat-icon customers">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-change positive" id="customersChange">
                            <i class="fas fa-arrow-up"></i>
                            <span>+8%</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="totalCustomers">
                            <span class="loading-placeholder">0</span>
                        </div>
                        <div class="stat-label">العملاء</div>
                        <div class="stat-description">العملاء الفريدون</div>
                    </div>
                </div>

                <!-- Enhanced Weekly Statistics Cards -->
                <div class="stat-card" data-stat="weekly-orders">
                    <div class="stat-header">
                        <div class="stat-icon weekly-orders">
                            <i class="fas fa-calendar-week"></i>
                        </div>
                        <div class="stat-change neutral" id="weeklyOrdersChange">
                            <i class="fas fa-calendar"></i>
                            <span>هذا الأسبوع</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="weeklyOrders">
                            <span class="loading-placeholder">0</span>
                        </div>
                        <div class="stat-label">الطلبات الأسبوعية</div>
                        <div class="stat-description">آخر 7 أيام</div>
                    </div>
                </div>

                <div class="stat-card" data-stat="weekly-revenue">
                    <div class="stat-header">
                        <div class="stat-icon weekly-revenue">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-change neutral" id="weeklyRevenueChange">
                            <i class="fas fa-calendar"></i>
                            <span>هذا الأسبوع</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="weeklyRevenue">
                            <span class="loading-placeholder">0 د.ع</span>
                        </div>
                        <div class="stat-label">الإيرادات الأسبوعية</div>
                        <div class="stat-description">آخر 7 أيام</div>
                    </div>
                </div>
            </div>

            <!-- Timeline Stats Section -->
            <div class="timeline-stats-section mb-lg">
                <div class="card card-elevated">
                    <div class="card-header">
                        <div class="flex items-center gap-md">
                            <div class="section-icon timeline-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div>
                                <h3 class="section-title">إحصائيات الخط الزمني</h3>
                                <p class="section-subtitle">نظرة شاملة على إنجازات المتجر والنمو عبر الوقت</p>
                            </div>
                        </div>
                        <div class="timeline-stats-controls">
                            <div class="stats-status" id="timelineStatsStatus">
                                <span class="status-indicator active" id="timelineStatusIndicator"></span>
                                <span class="status-text" id="timelineStatusText">مفعل</span>
                            </div>
                        </div>
                    </div>
                    <div class="timeline-stats-grid" id="timelineStatsGrid">
                        <!-- Years of Experience Card -->
                        <div class="timeline-stat-card" data-stat="years">
                            <div class="timeline-stat-header">
                                <div class="timeline-stat-icon years">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="timeline-stat-badge">
                                    <i class="fas fa-star"></i>
                                    <span>خبرة</span>
                                </div>
                            </div>
                            <div class="timeline-stat-content">
                                <div class="timeline-stat-value" id="timelineYears">
                                    <span class="loading-placeholder">6+</span>
                                </div>
                                <div class="timeline-stat-label">سنوات من الخبرة</div>
                                <div class="timeline-stat-description">في مجال التجميل والعناية</div>
                            </div>
                            <div class="timeline-stat-progress">
                                <div class="progress-bar years-progress"></div>
                            </div>
                        </div>

                        <!-- Satisfied Customers Card -->
                        <div class="timeline-stat-card" data-stat="customers">
                            <div class="timeline-stat-header">
                                <div class="timeline-stat-icon customers">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="timeline-stat-badge">
                                    <i class="fas fa-heart"></i>
                                    <span>عملاء</span>
                                </div>
                            </div>
                            <div class="timeline-stat-content">
                                <div class="timeline-stat-value" id="timelineCustomers">
                                    <span class="loading-placeholder">5000+</span>
                                </div>
                                <div class="timeline-stat-label">عميل راضٍ</div>
                                <div class="timeline-stat-description">حققوا أهدافهم في الجمال</div>
                            </div>
                            <div class="timeline-stat-progress">
                                <div class="progress-bar customers-progress"></div>
                            </div>
                        </div>

                        <!-- Diverse Products Card -->
                        <div class="timeline-stat-card" data-stat="products">
                            <div class="timeline-stat-header">
                                <div class="timeline-stat-icon products">
                                    <i class="fas fa-box-open"></i>
                                </div>
                                <div class="timeline-stat-badge">
                                    <i class="fas fa-gem"></i>
                                    <span>منتجات</span>
                                </div>
                            </div>
                            <div class="timeline-stat-content">
                                <div class="timeline-stat-value" id="timelineProducts">
                                    <span class="loading-placeholder">500+</span>
                                </div>
                                <div class="timeline-stat-label">منتج متنوع</div>
                                <div class="timeline-stat-description">لجميع احتياجات الجمال</div>
                            </div>
                            <div class="timeline-stat-progress">
                                <div class="progress-bar products-progress"></div>
                            </div>
                        </div>

                        <!-- Trusted Brands Card -->
                        <div class="timeline-stat-card" data-stat="brands">
                            <div class="timeline-stat-header">
                                <div class="timeline-stat-icon brands">
                                    <i class="fas fa-award"></i>
                                </div>
                                <div class="timeline-stat-badge">
                                    <i class="fas fa-crown"></i>
                                    <span>علامات</span>
                                </div>
                            </div>
                            <div class="timeline-stat-content">
                                <div class="timeline-stat-value" id="timelineBrands">
                                    <span class="loading-placeholder">50+</span>
                                </div>
                                <div class="timeline-stat-label">علامة تجارية</div>
                                <div class="timeline-stat-description">موثوقة وعالمية الجودة</div>
                            </div>
                            <div class="timeline-stat-progress">
                                <div class="progress-bar brands-progress"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Timeline Stats Footer -->
                    <div class="timeline-stats-footer">
                        <div class="stats-summary">
                            <div class="summary-item">
                                <i class="fas fa-chart-line"></i>
                                <span>نمو مستمر منذ التأسيس</span>
                            </div>
                            <div class="summary-item">
                                <i class="fas fa-shield-alt"></i>
                                <span>جودة مضمونة وثقة العملاء</span>
                            </div>
                        </div>
                        <div class="stats-actions">
                            <a href="site-settings.html" class="btn btn-sm btn-outline">
                                <i class="fas fa-cog"></i>
                                تحرير الإحصائيات
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Charts Section -->
            <div class="charts-section grid grid-cols-1 lg:grid-cols-2 gap-lg mb-lg">
                <div class="card chart-card enhanced-sales-card">
                    <div class="card-header">
                        <div class="flex items-center gap-md">
                            <div class="chart-icon sales-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="chart-title-section">
                                <h3 class="chart-title">المبيعات الشهرية</h3>
                                <p class="chart-subtitle">تتبع أداء المبيعات والنمو عبر الوقت</p>
                            </div>
                        </div>
                        <div class="enhanced-chart-controls">
                            <select class="modern-select" id="salesPeriod">
                                <option value="6">آخر 6 أشهر</option>
                                <option value="12">آخر 12 شهر</option>
                                <option value="24">آخر سنتين</option>
                            </select>
                        </div>
                    </div>
                    <div class="enhanced-sales-container">
                        <div class="chart-container">
                            <canvas id="salesChart" width="400" height="200"></canvas>
                            <div class="chart-loading" id="salesChartLoading">
                                <div class="loading-spinner"></div>
                                <p>جاري تحميل البيانات...</p>
                            </div>
                        </div>
                    </div>
                    <div class="enhanced-sales-footer">
                        <div class="sales-legend">
                            <div class="legend-item">
                                <div class="legend-color" style="background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);"></div>
                                <span class="legend-text">المبيعات الفعلية</span>
                                <span class="legend-value" id="actualSalesValue">0 د.ع</span>
                            </div>

                        </div>
                    </div>
                </div>

                <div class="card chart-card enhanced-products-card">
                    <div class="card-header">
                        <div class="flex items-center gap-md">
                            <div class="chart-icon products-icon">
                                <i class="fas fa-chart-pie"></i>
                            </div>
                            <div class="chart-title-section">
                                <h3 class="chart-title">توزيع المنتجات</h3>
                                <p class="chart-subtitle">توزيع المنتجات حسب الفئة والحالة</p>
                            </div>
                        </div>
                    </div>
                    <div class="enhanced-chart-container">
                        <div class="chart-wrapper">
                            <canvas id="productsChart" width="300" height="300"></canvas>
                            <div class="chart-loading" id="productsChartLoading">
                                <div class="loading-spinner"></div>
                                <p>جاري تحميل البيانات...</p>
                            </div>
                        </div>
                        <div class="products-legend" id="productsLegend">
                            <!-- Dynamic legend will be populated here -->
                        </div>
                    </div>
                    <!-- Enhanced chart footer removed since all stat cards were removed -->
                </div>
            </div>

            <!-- Enhanced Recent Activity Section -->
            <div class="activity-section grid grid-cols-1 lg:grid-cols-2 gap-lg">
                <div class="card enhanced-orders-card">
                    <div class="card-header">
                        <div class="flex items-center gap-md">
                            <div class="section-icon orders-icon-header">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div>
                                <h3 class="section-title">الطلبات الحديثة</h3>
                                <p class="section-subtitle">آخر الطلبات المستلمة والمعالجة</p>
                            </div>
                        </div>
                        <div class="card-actions">
                            <a href="orders.html" class="btn btn-sm btn-primary">
                                <i class="fas fa-external-link-alt"></i>
                                عرض الكل
                            </a>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="orders-container" id="recentOrders">
                            <div class="loading-state">
                                <div class="loading-spinner"></div>
                                <p>جاري تحميل الطلبات...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="flex items-center gap-md">
                            <div class="section-icon">
                                <i class="fas fa-bell"></i>
                            </div>
                            <div>
                                <h3>النشاط الحديث</h3>
                                <p class="text-sm text-secondary">آخر الأنشطة في النظام</p>
                            </div>
                        </div>
                        <div class="card-actions">
                            <!-- Clear button removed for simplified interface -->
                        </div>
                    </div>
                    <div class="card-content">
                        <div id="recentActivity">
                            <div class="activity-item">
                                <div class="activity-icon order">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-text">تم إضافة منتج جديد</div>
                                    <div class="activity-meta">
                                        <span class="activity-time">منذ ساعتين</span>
                                        <span class="activity-type">منتج</span>
                                    </div>
                                </div>
                                <div class="activity-status success">
                                    <i class="fas fa-check"></i>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon customer">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-text">طلب جديد من أحمد محمد</div>
                                    <div class="activity-meta">
                                        <span class="activity-time">منذ 3 ساعات</span>
                                        <span class="activity-type">طلب</span>
                                    </div>
                                </div>
                                <div class="activity-status pending">
                                    <i class="fas fa-clock"></i>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon product">
                                    <i class="fas fa-edit"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-text">تم تحديث معلومات منتج</div>
                                    <div class="activity-meta">
                                        <span class="activity-time">منذ 5 ساعات</span>
                                        <span class="activity-type">تحديث</span>
                                    </div>
                                </div>
                                <div class="activity-status info">
                                    <i class="fas fa-info"></i>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon user">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-text">عميل جديد سجل في الموقع</div>
                                    <div class="activity-meta">
                                        <span class="activity-time">منذ 6 ساعات</span>
                                        <span class="activity-type">عميل</span>
                                    </div>
                                </div>
                                <div class="activity-status success">
                                    <i class="fas fa-user-check"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Supabase configuration
        const SUPABASE_URL = 'https://krqijjttwllohulmdwgs.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70';
        
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

        // Get Supabase client function for consistency
        function getSupabaseClient() {
            return supabase;
        }

        // Enhanced authentication check
        function checkAuth() {
            const adminUser = sessionStorage.getItem('adminUser');
            if (!adminUser) {
                console.log('No admin user found in session storage, redirecting to login');
                window.location.href = 'login.html';
                return false;
            }

            try {
                const user = JSON.parse(adminUser);
                const loginTime = new Date(user.loginTime);
                const now = new Date();
                const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

                if (hoursDiff >= 8) {
                    console.log('Session expired, redirecting to login');
                    sessionStorage.removeItem('adminUser');
                    window.location.href = 'login.html';
                    return false;
                }

                // Update user info in sidebar - always use standardized values
                const sidebarUserName = document.getElementById('sidebarUserName');
                const sidebarUserAvatar = document.getElementById('sidebarUserAvatar');

                if (sidebarUserName) {
                    sidebarUserName.textContent = 'مدير النظام';
                }

                if (sidebarUserAvatar) {
                    sidebarUserAvatar.textContent = 'A';
                    sidebarUserAvatar.setAttribute('title', 'مدير النظام');
                }

                console.log('Authentication successful for user:', user.username);
                return true;
            } catch (error) {
                console.error('Authentication error:', error);
                sessionStorage.removeItem('adminUser');
                window.location.href = 'login.html';
                return false;
            }
        }

        // Enhanced dashboard utility functions









        function showLoadingState() {
            // Add loading class to stat cards
            document.querySelectorAll('.stat-card').forEach(card => {
                card.classList.add('loading');
            });
        }

        function hideLoadingState() {
            // Remove loading class from stat cards
            document.querySelectorAll('.stat-card').forEach(card => {
                card.classList.remove('loading');
            });
        }

        function showSuccessMessage(message) {
            showToast(message, 'success');
        }

        function showErrorMessage(message) {
            showToast(message, 'error');
        }

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'var(--color-success)' : type === 'error' ? 'var(--color-danger)' : 'var(--color-info)'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: var(--radius-md);
                box-shadow: var(--shadow-lg);
                z-index: 10000;
                animation: slideInRight 0.3s ease;
                max-width: 300px;
                font-family: 'Cairo', sans-serif;
                font-weight: 600;
            `;

            toast.innerHTML = `
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }

        // Enhanced logout function with modal
        function showLogoutModal() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                backdrop-filter: blur(5px);
                animation: fadeIn 0.3s ease;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 15px;
                    padding: 2rem;
                    max-width: 400px;
                    width: 90%;
                    text-align: center;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    animation: slideIn 0.3s ease;
                ">
                    <div style="color: #dc3545; font-size: 3rem; margin-bottom: 1rem;">
                        <i class="fas fa-sign-out-alt"></i>
                    </div>
                    <h3 style="color: #333; margin-bottom: 1rem;">تسجيل الخروج</h3>
                    <p style="color: #666; margin-bottom: 2rem;">هل أنت متأكد من تسجيل الخروج من لوحة التحكم؟</p>
                    <div style="display: flex; gap: 1rem; justify-content: center;">
                        <button onclick="confirmLogout()" style="
                            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                            color: white;
                            border: none;
                            padding: 0.8rem 1.5rem;
                            border-radius: 8px;
                            font-family: 'Cairo', sans-serif;
                            font-weight: 600;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        ">
                            <i class="fas fa-check"></i> نعم، تسجيل الخروج
                        </button>
                        <button onclick="closeLogoutModal()" style="
                            background: #6c757d;
                            color: white;
                            border: none;
                            padding: 0.8rem 1.5rem;
                            border-radius: 8px;
                            font-family: 'Cairo', sans-serif;
                            font-weight: 600;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        ">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            window.currentLogoutModal = modal;
        }

        function confirmLogout() {
            localStorage.removeItem('adminLoggedIn');
            localStorage.removeItem('adminUsername');
            window.location.href = 'login.html';
        }

        function closeLogoutModal() {
            if (window.currentLogoutModal) {
                window.currentLogoutModal.remove();
                window.currentLogoutModal = null;
            }
        }

        // Logout function - now uses professional modal system
        function logout() {
            showLogoutModal();
        }

        // Switch user function removed for cleaner interface

        // Sidebar toggle function is now in js/enhanced-sidebar.js

        // Format price in Iraqi Dinar
        function formatPrice(price) {
            return new Intl.NumberFormat('ar-IQ', {
                style: 'decimal',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(price) + ' د.ع';
        }

        // Format time ago in Arabic
        function formatTimeAgo(dateString) {
            const now = new Date();
            const date = new Date(dateString);
            const diffInMinutes = Math.floor((now - date) / (1000 * 60));

            if (diffInMinutes < 1) {
                return 'الآن';
            } else if (diffInMinutes < 60) {
                return `منذ ${diffInMinutes} دقيقة`;
            } else if (diffInMinutes < 1440) {
                const hours = Math.floor(diffInMinutes / 60);
                return `منذ ${hours} ساعة`;
            } else {
                const days = Math.floor(diffInMinutes / 1440);
                return `منذ ${days} يوم`;
            }
        }

        // Get status text in Arabic
        function getStatusText(status) {
            const statusMap = {
                'pending': 'في الانتظار',
                'confirmed': 'مؤكد',
                'processing': 'قيد التحضير',
                'shipped': 'تم الشحن',
                'delivered': 'تم التسليم',
                'cancelled': 'ملغي'
            };
            return statusMap[status] || 'غير محدد';
        }

        // Load Timeline Stats from Supabase
        async function loadTimelineStats() {
            console.log('🔄 Loading Timeline Stats...');
            try {
                // Get Supabase client using singleton pattern
                const supabase = getSupabaseClient();
                if (!supabase) {
                    console.error('❌ Supabase client not available for Timeline Stats');
                    showTimelineStatsError();
                    return;
                }

                // Show loading state
                showTimelineStatsLoading();

                // Load timeline stats settings from site_settings table
                const { data: settings, error } = await supabase
                    .from('site_settings')
                    .select('setting_key, setting_value')
                    .in('setting_key', [
                        'timeline_stats_enabled',
                        'timeline_stat_years',
                        'timeline_stat_customers',
                        'timeline_stat_products',
                        'timeline_stat_brands'
                    ]);

                if (error) {
                    console.error('❌ Error loading timeline stats:', error);
                    showTimelineStatsError();
                    return;
                }

                // Convert array to object for easier access
                const timelineSettings = {};
                if (settings && Array.isArray(settings)) {
                    settings.forEach(setting => {
                        timelineSettings[setting.setting_key] = setting.setting_value;
                    });
                }

                console.log('📊 Timeline Stats loaded:', timelineSettings);

                // Check if timeline stats are enabled
                const isEnabled = timelineSettings.timeline_stats_enabled === '1' || timelineSettings.timeline_stats_enabled === true;

                // Update status indicator
                updateTimelineStatsStatus(isEnabled);

                if (isEnabled) {
                    // Update timeline stats values
                    updateTimelineStatsValues({
                        years: timelineSettings.timeline_stat_years || '6+',
                        customers: timelineSettings.timeline_stat_customers || '5000+',
                        products: timelineSettings.timeline_stat_products || '500+',
                        brands: timelineSettings.timeline_stat_brands || '50+'
                    });

                    // Animate progress bars
                    animateTimelineProgress();
                } else {
                    // Show disabled state
                    showTimelineStatsDisabled();
                }

                // Hide loading state
                hideTimelineStatsLoading();

            } catch (error) {
                console.error('❌ Error loading Timeline Stats:', error);
                showTimelineStatsError();
            }
        }

        // Show Timeline Stats loading state
        function showTimelineStatsLoading() {
            const grid = document.getElementById('timelineStatsGrid');
            if (grid) {
                grid.classList.add('loading');
            }
        }

        // Hide Timeline Stats loading state
        function hideTimelineStatsLoading() {
            const grid = document.getElementById('timelineStatsGrid');
            if (grid) {
                grid.classList.remove('loading');
            }
        }

        // Update Timeline Stats status indicator
        function updateTimelineStatsStatus(isEnabled) {
            const statusIndicator = document.getElementById('timelineStatusIndicator');
            const statusText = document.getElementById('timelineStatusText');

            if (statusIndicator && statusText) {
                if (isEnabled) {
                    statusIndicator.className = 'status-indicator active';
                    statusText.textContent = 'مفعل';
                    statusText.parentElement.style.color = 'var(--color-success)';
                } else {
                    statusIndicator.className = 'status-indicator inactive';
                    statusText.textContent = 'معطل';
                    statusText.parentElement.style.color = 'var(--color-danger)';
                }
            }
        }

        // Update Timeline Stats values
        function updateTimelineStatsValues(values) {
            const elements = {
                years: document.getElementById('timelineYears'),
                customers: document.getElementById('timelineCustomers'),
                products: document.getElementById('timelineProducts'),
                brands: document.getElementById('timelineBrands')
            };

            Object.keys(elements).forEach(key => {
                const element = elements[key];
                if (element && values[key]) {
                    // Add animation effect
                    element.style.opacity = '0';
                    setTimeout(() => {
                        element.textContent = values[key];
                        element.style.opacity = '1';
                    }, 200);
                }
            });
        }

        // Animate Timeline Progress bars
        function animateTimelineProgress() {
            setTimeout(() => {
                const progressBars = document.querySelectorAll('.timeline-stat-progress .progress-bar');
                progressBars.forEach((bar, index) => {
                    setTimeout(() => {
                        bar.style.opacity = '1';
                        bar.style.transform = 'scaleX(1)';
                    }, index * 200);
                });
            }, 500);
        }

        // Show Timeline Stats error state
        function showTimelineStatsError() {
            const grid = document.getElementById('timelineStatsGrid');
            if (grid) {
                grid.innerHTML = `
                    <div class="timeline-stats-error" style="grid-column: 1 / -1; text-align: center; padding: 2rem; color: var(--color-danger);">
                        <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                        <p>حدث خطأ في تحميل إحصائيات الخط الزمني</p>
                        <button onclick="loadTimelineStats()" class="btn btn-sm btn-outline" style="margin-top: 1rem;">
                            <i class="fas fa-redo"></i> إعادة المحاولة
                        </button>
                    </div>
                `;
            }
            updateTimelineStatsStatus(false);
        }

        // Show Timeline Stats disabled state
        function showTimelineStatsDisabled() {
            const cards = document.querySelectorAll('.timeline-stat-card');
            cards.forEach(card => {
                card.style.opacity = '0.6';
                card.style.pointerEvents = 'none';
            });
        }

        // Load dashboard statistics
        async function loadDashboardStats() {
            console.log('🔄 Starting dashboard statistics loading...');
            try {
                // Show loading state
                document.getElementById('totalOrders').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                document.getElementById('totalProducts').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                document.getElementById('totalSales').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                document.getElementById('totalCustomers').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                document.getElementById('weeklyOrders').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                document.getElementById('weeklyRevenue').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

                console.log('✅ Loading spinners set for all statistics elements');

                // Load orders count and sales
                const { data: orders, error: ordersError } = await supabase
                    .from('orders')
                    .select('id, total, created_at, status');

                if (!ordersError && orders) {
                    document.getElementById('totalOrders').textContent = orders.length;

                    // Calculate total sales from delivered orders only
                    const deliveredOrders = orders.filter(order => order.status === 'delivered');
                    const totalSales = deliveredOrders.reduce((sum, order) => sum + parseFloat(order.total || 0), 0);
                    document.getElementById('totalSales').textContent = formatPrice(totalSales);

                    // Calculate growth percentage (compare with previous month)
                    const currentMonth = new Date().getMonth();
                    const currentYear = new Date().getFullYear();
                    const currentMonthOrders = orders.filter(order => {
                        const orderDate = new Date(order.created_at);
                        return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear;
                    });

                    const previousMonth = currentMonth === 0 ? 11 : currentMonth - 1;
                    const previousYear = currentMonth === 0 ? currentYear - 1 : currentYear;
                    const previousMonthOrders = orders.filter(order => {
                        const orderDate = new Date(order.created_at);
                        return orderDate.getMonth() === previousMonth && orderDate.getFullYear() === previousYear;
                    });

                    const growthPercentage = previousMonthOrders.length > 0
                        ? Math.round(((currentMonthOrders.length - previousMonthOrders.length) / previousMonthOrders.length) * 100)
                        : 0;

                    const ordersChangeElement = document.getElementById('ordersChange');
                    if (ordersChangeElement) {
                        ordersChangeElement.textContent = `${growthPercentage >= 0 ? '+' : ''}${growthPercentage}%`;
                        ordersChangeElement.className = `stat-change ${growthPercentage >= 0 ? 'positive' : 'negative'}`;
                    }

                    // Update sales values for charts
                    const actualSalesElement = document.getElementById('actualSalesValue');
                    if (actualSalesElement) {
                        actualSalesElement.textContent = formatPrice(totalSales);
                    }
                } else {
                    document.getElementById('totalOrders').textContent = '0';
                    document.getElementById('totalSales').textContent = formatPrice(0);

                    // Set default values for sales elements
                    const actualSalesElement = document.getElementById('actualSalesValue');
                    if (actualSalesElement) actualSalesElement.textContent = formatPrice(0);
                }

                // Load products count and categories
                const { data: allProducts, error: productsError } = await supabase
                    .from('products')
                    .select('id, created_at, is_active, category_id');

                if (!productsError && allProducts) {
                    // Active products
                    const activeProducts = allProducts.filter(product => product.is_active === true);
                    document.getElementById('totalProducts').textContent = activeProducts.length;





                    // Calculate products growth
                    const currentMonth = new Date().getMonth();
                    const currentYear = new Date().getFullYear();
                    const currentMonthProducts = activeProducts.filter(product => {
                        const productDate = new Date(product.created_at);
                        return productDate.getMonth() === currentMonth && productDate.getFullYear() === currentYear;
                    });

                    const productsGrowth = currentMonthProducts.length;
                    const productsChangeElement = document.getElementById('productsChange');
                    if (productsChangeElement) {
                        productsChangeElement.textContent = `+${productsGrowth}`;
                    }
                } else {
                    console.error('❌ Products loading failed:', productsError);
                    const totalProductsElement = document.getElementById('totalProducts');
                    if (totalProductsElement) {
                        totalProductsElement.textContent = '0';
                    }
                }

                // Load unique customers count
                const { data: customers, error: customersError } = await supabase
                    .from('orders')
                    .select('customer_phone, created_at');

                if (!customersError && customers) {
                    const uniqueCustomers = new Set(customers.map(c => c.customer_phone)).size;
                    document.getElementById('totalCustomers').textContent = uniqueCustomers;

                    // Calculate customer growth
                    const currentMonth = new Date().getMonth();
                    const currentYear = new Date().getFullYear();
                    const currentMonthCustomers = new Set(
                        customers
                            .filter(customer => {
                                const customerDate = new Date(customer.created_at);
                                return customerDate.getMonth() === currentMonth && customerDate.getFullYear() === currentYear;
                            })
                            .map(c => c.customer_phone)
                    ).size;

                    const customersChangeElement = document.getElementById('customersChange');
                    customersChangeElement.textContent = `+${currentMonthCustomers}`;
                } else {
                    document.getElementById('totalCustomers').textContent = '0';
                }

                // Load weekly statistics with real data from Supabase
                await loadWeeklyStats();

            } catch (error) {
                console.error('Error loading dashboard stats:', error);
                // Show error state
                document.getElementById('totalOrders').textContent = 'خطأ';
                document.getElementById('totalProducts').textContent = 'خطأ';
                document.getElementById('totalSales').textContent = 'خطأ';
                document.getElementById('totalCustomers').textContent = 'خطأ';
                document.getElementById('weeklyOrders').textContent = 'خطأ';
                document.getElementById('weeklyRevenue').textContent = 'خطأ';
            }
        }

        // Load weekly statistics with real data from Supabase
        async function loadWeeklyStats() {
            try {
                // Calculate current week start and end dates
                const now = new Date();
                const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
                const startOfWeek = new Date(now);
                startOfWeek.setDate(now.getDate() - currentDay); // Start from Sunday
                startOfWeek.setHours(0, 0, 0, 0);

                const endOfWeek = new Date(startOfWeek);
                endOfWeek.setDate(startOfWeek.getDate() + 6); // End on Saturday
                endOfWeek.setHours(23, 59, 59, 999);

                // Format dates for Supabase query (YYYY-MM-DD format)
                const startDate = startOfWeek.toISOString().split('T')[0];
                const endDate = endOfWeek.toISOString().split('T')[0];

                // Load weekly orders from Supabase
                const { data: weeklyOrdersData, error: weeklyOrdersError } = await supabase
                    .from('orders')
                    .select('id, total, created_at, status')
                    .gte('created_at', startDate)
                    .lte('created_at', endDate + 'T23:59:59.999Z');

                if (!weeklyOrdersError && weeklyOrdersData) {
                    // Count total weekly orders
                    const weeklyOrdersCount = weeklyOrdersData.length;
                    document.getElementById('weeklyOrders').textContent = weeklyOrdersCount;

                    // Calculate weekly revenue from delivered orders only
                    const deliveredWeeklyOrders = weeklyOrdersData.filter(order => order.status === 'delivered');
                    const weeklyRevenue = deliveredWeeklyOrders.reduce((sum, order) => sum + parseFloat(order.total || 0), 0);
                    document.getElementById('weeklyRevenue').textContent = formatPrice(weeklyRevenue);

                    // Update change indicators with week info
                    const weeklyOrdersChangeElement = document.getElementById('weeklyOrdersChange');
                    const weeklyRevenueChangeElement = document.getElementById('weeklyRevenueChange');

                    // Format week display
                    const weekStart = startOfWeek.toLocaleDateString('ar-IQ', { month: 'short', day: 'numeric' });
                    const weekEnd = endOfWeek.toLocaleDateString('ar-IQ', { month: 'short', day: 'numeric' });
                    const weekDisplay = `${weekStart} - ${weekEnd}`;

                    weeklyOrdersChangeElement.textContent = weekDisplay;
                    weeklyRevenueChangeElement.textContent = weekDisplay;
                    weeklyOrdersChangeElement.className = 'stat-change neutral';
                    weeklyRevenueChangeElement.className = 'stat-change neutral';

                } else {
                    console.error('Error loading weekly orders:', weeklyOrdersError);
                    document.getElementById('weeklyOrders').textContent = '0';
                    document.getElementById('weeklyRevenue').textContent = formatPrice(0);

                    document.getElementById('weeklyOrdersChange').textContent = 'لا توجد بيانات';
                    document.getElementById('weeklyRevenueChange').textContent = 'لا توجد بيانات';
                }

            } catch (error) {
                console.error('Error loading weekly stats:', error);
                document.getElementById('weeklyOrders').textContent = 'خطأ';
                document.getElementById('weeklyRevenue').textContent = 'خطأ';
                document.getElementById('weeklyOrdersChange').textContent = 'خطأ في التحميل';
                document.getElementById('weeklyRevenueChange').textContent = 'خطأ في التحميل';
            }
        }

        // Load monthly sales data
        async function loadMonthlySalesData() {
            try {
                const currentYear = new Date().getFullYear();
                const { data: orders, error } = await supabase
                    .from('orders')
                    .select('total, created_at, status')
                    .gte('created_at', `${currentYear}-01-01`)
                    .lte('created_at', `${currentYear}-12-31`)
                    .eq('status', 'delivered');

                if (error) throw error;

                // Initialize monthly data
                const monthlyData = new Array(12).fill(0);
                const monthNames = [
                    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
                ];

                // Calculate monthly sales
                if (orders && orders.length > 0) {
                    orders.forEach(order => {
                        const orderDate = new Date(order.created_at);
                        const month = orderDate.getMonth();
                        monthlyData[month] += parseFloat(order.total || 0);
                    });
                }

                return {
                    labels: monthNames,
                    data: monthlyData
                };
            } catch (error) {
                console.error('Error loading monthly sales data:', error);
                return {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    data: [0, 0, 0, 0, 0, 0]
                };
            }
        }

        // Load products distribution data
        async function loadProductsDistribution() {
            try {
                const { data: products, error } = await supabase
                    .from('products')
                    .select('product_type')
                    .eq('is_active', true);

                if (error) throw error;

                const distribution = {
                    'skincare': 0,
                    'haircare': 0,
                    'general': 0
                };

                if (products && products.length > 0) {
                    products.forEach(product => {
                        const type = product.product_type || 'general';
                        distribution[type]++;
                    });
                }

                return {
                    labels: ['العناية بالبشرة', 'العناية بالشعر', 'عام'],
                    data: [distribution.skincare, distribution.haircare, distribution.general],
                    colors: ['#82877a', '#e74c3c', '#3498db']
                };
            } catch (error) {
                console.error('Error loading products distribution:', error);
                return {
                    labels: ['العناية بالبشرة', 'العناية بالشعر', 'عام'],
                    data: [0, 0, 0],
                    colors: ['#82877a', '#e74c3c', '#3498db']
                };
            }
        }

        // Load recent orders
        async function loadRecentOrders() {
            try {
                const { data: orders, error } = await supabase
                    .from('orders')
                    .select('*')
                    .order('created_at', { ascending: false })
                    .limit(5);

                if (error) throw error;

                const container = document.getElementById('recentOrders');

                if (orders && orders.length > 0) {
                    container.innerHTML = orders.map(order => {
                        const orderDate = new Date(order.created_at);
                        const timeAgo = getTimeAgo(orderDate);
                        const statusName = getStatusName(order.status);
                        const customerInitial = order.customer_name ? order.customer_name.charAt(0).toUpperCase() : 'ع';

                        return `
                            <div class="order-item">
                                <div class="order-avatar">
                                    ${customerInitial}
                                </div>
                                <div class="order-content">
                                    <div class="order-header">
                                        <span class="order-customer">${order.customer_name || 'عميل'}</span>
                                        <span class="order-amount">${formatPrice(order.total || 0)}</span>
                                    </div>
                                    <div class="order-meta">
                                        <span class="order-time">
                                            <i class="fas fa-clock"></i>
                                            ${timeAgo}
                                        </span>
                                        <span class="order-status ${order.status || 'pending'}">${statusName}</span>
                                    </div>
                                </div>
                            </div>
                        `;
                    }).join('');
                } else {
                    container.innerHTML = `
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <h4>لا توجد طلبات حديثة</h4>
                            <p>لم يتم استلام أي طلبات جديدة بعد</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error loading recent orders:', error);
                document.getElementById('recentOrders').innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h4>خطأ في تحميل الطلبات</h4>
                        <p>حدث خطأ أثناء تحميل الطلبات الحديثة. يرجى المحاولة مرة أخرى.</p>
                        <button class="btn btn-sm btn-primary" onclick="loadRecentOrders()" style="margin-top: 1rem;">
                            <i class="fas fa-refresh"></i>
                            إعادة المحاولة
                        </button>
                    </div>
                `;
            }
        }

        // Load recent activity
        async function loadRecentActivity() {
            try {
                const container = document.getElementById('recentActivity');
                container.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i><p>جاري التحميل...</p></div>';

                // Get recent orders
                const { data: recentOrders, error: ordersError } = await supabase
                    .from('orders')
                    .select('customer_name, created_at, status')
                    .order('created_at', { ascending: false })
                    .limit(3);

                // Get recent products
                const { data: recentProducts, error: productsError } = await supabase
                    .from('products')
                    .select('name, created_at, updated_at')
                    .order('created_at', { ascending: false })
                    .limit(2);

                let activities = [];

                // Add recent orders to activities
                if (!ordersError && recentOrders) {
                    recentOrders.forEach(order => {
                        activities.push({
                            type: 'order',
                            text: `طلب جديد من ${order.customer_name}`,
                            time: order.created_at,
                            icon: 'fas fa-shopping-cart'
                        });
                    });
                }

                // Add recent products to activities
                if (!productsError && recentProducts) {
                    recentProducts.forEach(product => {
                        const isNew = new Date(product.created_at) > new Date(product.updated_at || product.created_at);
                        activities.push({
                            type: 'product',
                            text: isNew ? `تم إضافة منتج: ${product.name}` : `تم تحديث منتج: ${product.name}`,
                            time: product.updated_at || product.created_at,
                            icon: isNew ? 'fas fa-plus' : 'fas fa-edit'
                        });
                    });
                }

                // Sort activities by time
                activities.sort((a, b) => new Date(b.time) - new Date(a.time));

                // Display activities
                if (activities.length > 0) {
                    container.innerHTML = activities.slice(0, 5).map(activity => {
                        const timeAgo = getTimeAgo(new Date(activity.time));
                        return `
                            <div class="activity-item">
                                <div class="activity-icon ${activity.type}">
                                    <i class="${activity.icon}"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-text">${activity.text}</div>
                                    <div class="activity-time">${timeAgo}</div>
                                </div>
                            </div>
                        `;
                    }).join('');
                } else {
                    container.innerHTML = '<p style="text-align: center; color: #666; padding: 2rem;">لا يوجد نشاط حديث</p>';
                }

            } catch (error) {
                console.error('Error loading recent activity:', error);
                document.getElementById('recentActivity').innerHTML = '<p style="text-align: center; color: #e74c3c;">خطأ في تحميل النشاط</p>';
            }
        }

        // Helper functions
        function getTimeAgo(date) {
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) {
                return 'منذ لحظات';
            } else if (diffInSeconds < 3600) {
                const minutes = Math.floor(diffInSeconds / 60);
                return `منذ ${minutes} دقيقة`;
            } else if (diffInSeconds < 86400) {
                const hours = Math.floor(diffInSeconds / 3600);
                return `منذ ${hours} ساعة`;
            } else {
                const days = Math.floor(diffInSeconds / 86400);
                return `منذ ${days} يوم`;
            }
        }

        function getStatusName(status) {
            const statusMap = {
                'pending': 'معلق',
                'confirmed': 'مؤكد',
                'preparing': 'قيد التحضير',
                'shipped': 'تم الشحن',
                'delivered': 'تم التسليم',
                'cancelled': 'ملغي'
            };
            return statusMap[status] || status;
        }

        // Initialize charts with real data
        async function initializeCharts() {
            try {
                // Load real sales data
                const salesData = await loadMonthlySalesData();

                // Sales Chart
                const salesCtx = document.getElementById('salesChart').getContext('2d');
                new Chart(salesCtx, {
                    type: 'line',
                    data: {
                        labels: salesData.labels,
                        datasets: [{
                            label: 'المبيعات',
                            data: salesData.data,
                            borderColor: '#82877a',
                            backgroundColor: 'rgba(130, 135, 122, 0.1)',
                            tension: 0.4,
                            fill: true,
                            pointBackgroundColor: '#82877a',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 5
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return `المبيعات: ${formatPrice(context.parsed.y)}`;
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return formatPrice(value);
                                    }
                                },
                                grid: {
                                    color: 'rgba(0,0,0,0.1)'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });

                // Load real products distribution data
                const productsData = await loadProductsDistribution();

                // Products Chart
                const productsCtx = document.getElementById('productsChart').getContext('2d');
                new Chart(productsCtx, {
                    type: 'doughnut',
                    data: {
                        labels: productsData.labels,
                        datasets: [{
                            data: productsData.data,
                            backgroundColor: productsData.colors,
                            borderWidth: 0,
                            hoverOffset: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true,
                                    font: {
                                        family: 'Cairo',
                                        size: 12
                                    }
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = total > 0 ? Math.round((context.parsed / total) * 100) : 0;
                                        return `${context.label}: ${context.parsed} (${percentage}%)`;
                                    }
                                }
                            }
                        }
                    }
                });

            } catch (error) {
                console.error('Error initializing charts:', error);

                // Fallback to default charts if data loading fails
                const salesCtx = document.getElementById('salesChart').getContext('2d');
                new Chart(salesCtx, {
                    type: 'line',
                    data: {
                        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                        datasets: [{
                            label: 'المبيعات',
                            data: [0, 0, 0, 0, 0, 0],
                            borderColor: '#82877a',
                            backgroundColor: 'rgba(130, 135, 122, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: { display: false }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return formatPrice(value);
                                    }
                                }
                            }
                        }
                    }
                });

                const productsCtx = document.getElementById('productsChart').getContext('2d');
                new Chart(productsCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['لا توجد بيانات'],
                        datasets: [{
                            data: [1],
                            backgroundColor: ['#ddd'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: { position: 'bottom' }
                        }
                    }
                });
            }
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', async function() {
            // Small delay to ensure all scripts are loaded
            setTimeout(async () => {
                if (checkAuth()) {
                    try {
                        // Load all dashboard data
                        await Promise.all([
                            loadDashboardStats(),
                            loadTimelineStats(),
                            loadRecentOrders(),
                            loadRecentActivity(),
                            initializeCharts()
                        ]);
                    } catch (error) {
                        console.error('Error initializing dashboard:', error);
                    }
                }
            }, 100);
        });

        // Production-ready authentication system - debug functions removed

        // ===== UNIFIED MOBILE NAVIGATION SYSTEM =====
        // Mobile navigation is now handled by unified-sidebar-manager.js
        // Legacy functions maintained for backward compatibility
    </script>

    <!-- Unified Sidebar System -->
    <script src="js/unified-sidebar-manager.js"></script>

    <!-- Unified Notification System -->
    <script src="js/unified-notification-system.js"></script>

    <!-- Professional Logout System -->
    <script src="js/logout-system.js"></script>
</body>
</html>
